/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/23
 */
package com.xmcares.platform.hookbox.common.job.error;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public class JobExecutionException extends JobException {

    private final String jobInstanceId;

    public JobExecutionException(String jobId, String jobInstanceId) {
        super(jobId);
        this.jobInstanceId = jobInstanceId;
    }

    public JobExecutionException(String jobId, String jobInstanceId, String message) {
        super(jobId, message);
        this.jobInstanceId = jobInstanceId;
    }

    public JobExecutionException(String jobId, String jobInstanceId, String message, Throwable cause) {
        super(jobId, message, cause);
        this.jobInstanceId = jobInstanceId;
    }

    public JobExecutionException(String jobId, String jobInstanceId, Throwable cause) {
        super(jobId, cause);
        this.jobInstanceId = jobInstanceId;
    }

    public JobExecutionException(String jobId, String jobInstanceId, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(jobId, message, cause, enableSuppression, writableStackTrace);
        this.jobInstanceId = jobInstanceId;
    }

    public String getJobInstanceId() {
        return jobInstanceId;
    }

}
