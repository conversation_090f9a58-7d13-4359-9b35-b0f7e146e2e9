/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.sink;

import com.xmcares.platform.seatunnel.connectors.mqtt.client.MqttClientManager;
import com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttSinkConfig;
import com.xmcares.platform.seatunnel.connectors.mqtt.exception.MqttConnectorErrorCode;
import com.xmcares.platform.seatunnel.connectors.mqtt.exception.MqttConnectorException;
import org.apache.seatunnel.api.sink.SinkWriter;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.common.utils.JsonUtils;
import org.apache.seatunnel.connectors.seatunnel.common.sink.AbstractSinkWriter;

import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/** MQTT Sink Writer实现 负责将SeaTunnelRow转换为MQTT消息并发布 */
public class MqttSinkWriter extends AbstractSinkWriter<SeaTunnelRow, Void> {

    private static final Logger log = LoggerFactory.getLogger(MqttSinkWriter.class);


    private final MqttSinkConfig config;
    private final SinkWriter.Context context;
    private final SeaTunnelRowType rowType;
    private final AtomicLong messageCounter;

    private MqttClientManager clientManager;
    private MqttClient mqttClient;

    public MqttSinkWriter(MqttSinkConfig config, SinkWriter.Context context) {
        this.config = config;
        this.context = context;
        this.rowType = config.getCatalogTable().getTableSchema().toPhysicalRowDataType();
        this.messageCounter = new AtomicLong(0);

        log.info(
                "Created MQTT sink writer for subtask: {}, table: {}",
                context.getIndexOfSubtask(),
                config.getCatalogTable().getTableId());
    }

    @Override
    public void write(SeaTunnelRow element) throws IOException {
        try {
            // 确保MQTT客户端连接
            ensureClientConnected();

            // 解析目标Topic
            String targetTopic = resolveTargetTopic(element);

            // 序列化消息内容
            byte[] messagePayload = serializeMessage(element);

            // 创建MQTT消息
            MqttMessage mqttMessage = new MqttMessage(messagePayload);
            mqttMessage.setQos(config.getQos());
            mqttMessage.setRetained(config.isRetained());

            // 发布消息
            mqttClient.publish(targetTopic, mqttMessage);

            long count = messageCounter.incrementAndGet();
            if (count % 1000 == 0) {
                log.info("Published {} messages to MQTT broker", count);
            }

            log.debug(
                    "Published message to topic: {}, qos: {}, retained: {}, payload size: {}",
                    targetTopic,
                    config.getQos(),
                    config.isRetained(),
                    messagePayload.length);

        } catch (Exception e) {
            log.error("Failed to write message to MQTT broker", e);
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.PUBLISH_FAILED,
                    "Failed to publish message to MQTT broker: " + e.getMessage(),
                    e);
        }
    }

    @Override
    public List<Void> snapshotState(long checkpointId) throws IOException {
        log.debug(
                "Snapshot state for checkpoint: {}, published messages: {}",
                checkpointId,
                messageCounter.get());
        // MQTT Sink是无状态的，返回空列表
        return java.util.Collections.emptyList();
    }

    @Override
    public void close() throws IOException {
        log.info("Closing MQTT sink writer, total published messages: {}", messageCounter.get());

        try {
            if (clientManager != null) {
                clientManager.close();
            }
        } catch (Exception e) {
            log.warn("Error closing MQTT client manager", e);
        }
    }

    /** 确保MQTT客户端已连接 */
    private void ensureClientConnected() throws Exception {
        if (clientManager == null) {
            log.info("Initializing MQTT client manager for sink");
            clientManager = new MqttClientManager(config);
            clientManager.connect();
        }

        if (mqttClient == null || !mqttClient.isConnected()) {
            mqttClient = clientManager.getClient();
            log.info("MQTT client connected for sink writer");
        }
    }

    /** 解析目标Topic名称 */
    private String resolveTargetTopic(SeaTunnelRow element) {
        if (!config.isDynamicTopic()) {
            return config.getTopic();
        }

        // 支持动态Topic解析
        String tableId = config.getCatalogTable().getTableId().toTablePath().toString();

        // 可以扩展支持更多占位符，比如从row中提取字段值
        return config.resolveTopicName(tableId);
    }

    /** 序列化消息内容 */
    private byte[] serializeMessage(SeaTunnelRow element) {
        String messageFormat = config.getMessageFormat().toLowerCase();

        switch (messageFormat) {
            case "json":
                return serializeAsJson(element);
            case "text":
                return serializeAsText(element);
            default:
                throw new MqttConnectorException(
                        MqttConnectorErrorCode.SERIALIZATION_ERROR,
                        "Unsupported message format: " + messageFormat);
        }
    }

    /** JSON格式序列化 */
    private byte[] serializeAsJson(SeaTunnelRow element) {
        try {
            Map<String, Object> messageMap = new HashMap<>();

            String[] fieldNames = rowType.getFieldNames();
            SeaTunnelDataType<?>[] fieldTypes = rowType.getFieldTypes();
            Object[] fieldValues = element.getFields();

            for (int i = 0; i < fieldNames.length; i++) {
                Object value = fieldValues[i];
                // 处理特殊数据类型的序列化
                messageMap.put(fieldNames[i], convertFieldValue(fieldTypes[i], value));
            }

            // 根据配置决定是否添加元数据信息
            if (config.isIncludeMetadata()) {
                messageMap.put("__seatunnel_table_id", element.getTableId());
                messageMap.put("__seatunnel_row_kind", element.getRowKind().toString());
                messageMap.put("__seatunnel_timestamp", System.currentTimeMillis());
            }

            return JsonUtils.toJsonString(messageMap).getBytes("UTF-8");

        } catch (Exception e) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.SERIALIZATION_ERROR,
                    "Failed to serialize row as JSON: " + e.getMessage(),
                    e);
        }
    }

    /** 文本格式序列化（CSV格式） */
    private byte[] serializeAsText(SeaTunnelRow element) {
        try {
            StringBuilder sb = new StringBuilder();
            Object[] fieldValues = element.getFields();

            for (int i = 0; i < fieldValues.length; i++) {
                if (i > 0) {
                    sb.append(",");
                }

                Object value = fieldValues[i];
                if (value != null) {
                    // 处理包含逗号的字段，用双引号包围
                    String strValue = value.toString();
                    if (strValue.contains(",")
                            || strValue.contains("\"")
                            || strValue.contains("\n")) {
                        sb.append("\"").append(strValue.replace("\"", "\"\"")).append("\"");
                    } else {
                        sb.append(strValue);
                    }
                }
            }

            return sb.toString().getBytes("UTF-8");

        } catch (Exception e) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.SERIALIZATION_ERROR,
                    "Failed to serialize row as text: " + e.getMessage(),
                    e);
        }
    }

    /** 转换字段值以适应JSON序列化 */
    private Object convertFieldValue(SeaTunnelDataType<?> fieldType, Object value) {
        if (value == null) {
            return null;
        }

        // 根据数据类型进行适当转换
        switch (fieldType.getSqlType()) {
            case BYTES:
                // 字节数组转为Base64字符串
                if (value instanceof byte[]) {
                    return java.util.Base64.getEncoder().encodeToString((byte[]) value);
                }
                break;
            case DATE:
            case TIME:
            case TIMESTAMP:
                // 时间类型转为ISO字符串
                return value.toString();
            default:
                // 其他类型直接返回
                break;
        }

        return value;
    }
}
