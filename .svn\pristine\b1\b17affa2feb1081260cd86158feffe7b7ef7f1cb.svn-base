import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Button, Transfer, Pagination } from 'antd';
import * as utils from '@/utils/utils';

const serviceStatus = [{ code: 0, text: '禁用' }, { code: 1, text: '正常' }];
const appStatus = [{ code: 0, text: '不需要' }, { code: 1, text: '需要' }];
const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    lg: { span: 5 },
  },
  wrapperCol: {
    lg: { span: 19 },
  },
};
const textItemLayout = {
  labelCol: {
    lg: { span: 4 },
  },
  wrapperCol: {
    lg: { span: 18 },
  },
};

class ServiceModel extends Component {
  state = {
    visable: false,
    buttonLoading: false,
    modalTitle: '默认',
    markStatus: 1,
    mockData: [],
    targetKeys: [],
  };

  filterOption = (inputValue, option) => option.name.indexOf(inputValue) > -1;
  handleChange = targetKeys => {
    this.setState({ targetKeys });
  };
  getData = (data, listData) => {
    let targetKeys = [];
    if (listData && listData) {
      listData.map(item => {
        targetKeys.push(item.id);
      });
    }
    if (data && data.data) {
      data.data.map((item, index) => {
        data.data[index].key = item.id;
        data.data[index].disabled = item.status == '0';
      });
    }
    this.setState({
      mockData: data,
      targetKeys: targetKeys,
      total: data?.total || 0,
    });
  };
  handleMinModalVisible = (flag, title, mark) => {
    this.setState({
      visable: flag,
      modalTitle: title,
      markStatus: mark,
    });
  };

  okHandle = () => {
    const { submitForm } = this.props;
    submitForm({ appIdList: this.state.targetKeys });
  };

  // 穿梭框左分页
  handleLeftChange = (page, pagesize) => {
    const { reloadTranferData } = this.props;
    reloadTranferData({ pageNo: page, pageSize: pagesize });
  };

  // 穿梭框右分页
  handleRightChange = () => {};

  getFooter = (props, total, targetKeys) => {
    return (
      <Pagination
        size="small"
        total={props.direction == 'left' ? total : targetKeys.length}
        showSizeChanger
        showQuickJumper
        onShowSizeChange={
          props.direction == 'left' ? this.handleLeftChange : this.handleRightChange
        }
        onChange={props.direction == 'left' ? this.handleLeftChange : this.handleRightChange}
      />
    );

    // if(props.direction=='left'){
    //  return
    // }
  };
  render() {
    const { visable, buttonLoading, modalTitle, mockData, targetKeys, total } = this.state;
    return (
      <Modal
        width={700}
        title={modalTitle}
        visible={visable}
        centered
        onCancel={() => this.handleMinModalVisible(false)}
        footer={[
          <Button onClick={this.okHandle} type={'primary'} loading={buttonLoading} key={1}>
            保存
          </Button>,
          <Button onClick={() => this.handleMinModalVisible(false)} key={2}>
            取消
          </Button>,
        ]}
        maskClosable={false}
      >
        <Transfer
          dataSource={mockData && mockData.data}
          listStyle={{
            width: 300,
            height: 400,
          }}
          showSearch
          filterOption={this.filterOption}
          targetKeys={targetKeys && targetKeys}
          onChange={this.handleChange}
          render={item => `${item.name}`}
          // footer={(item)=>this.getFooter(item,total,targetKeys)}
        />
      </Modal>
    );
  }
}

export default Form.create({})(ServiceModel);
