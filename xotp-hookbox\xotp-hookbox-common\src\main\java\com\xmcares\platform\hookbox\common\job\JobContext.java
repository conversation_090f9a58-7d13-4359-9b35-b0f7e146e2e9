/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/28
 */
package com.xmcares.platform.hookbox.common.job;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public interface JobContext extends Serializable, JobConstants {

    String getJobId();

    String getJobName();

    String getJobInstanceId();

    String getJobOptions();

    Date getScheduleTime();

    Date getTriggerTime();

    JobParams getJobParams();

}
