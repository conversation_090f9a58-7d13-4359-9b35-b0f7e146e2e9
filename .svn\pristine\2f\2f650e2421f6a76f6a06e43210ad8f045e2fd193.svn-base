/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/20
 */
package com.xmcares.platform.hookbox.common.job.seatunnel;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

/**
 * SeaTunnel Job 事件处理控制器
 * <AUTHOR>
 * @since 2.1.0
 */
@RestController
public class SeaTunnelJobEventHandler {
    private final Logger logger = LoggerFactory.getLogger(SeaTunnelJobEventHandler.class);
    private final String EXPECTED_AUTH_TOKEN = "`1234567890"; // 将此替换为你的实际密钥



    @PostMapping("/seatunnel/events")
    public ResponseEntity<String> receiveEvent(
            @RequestHeader(name = "Authorization") String authHeader,
            @RequestBody String body) {
        //  验证 Authorization 请求头
        if (!EXPECTED_AUTH_TOKEN.equals(authHeader)) {
            logger.warn("Unauthorized request received. Invalid token: {}", authHeader);
            return new ResponseEntity<>("Unauthorized", HttpStatus.UNAUTHORIZED);
        }

        // 处理事件
        //TODO
        System.out.println("接收到事件：" + body);
        // 返回成功响应给 SeaTunnel Engine
        return new ResponseEntity<>("Event received successfully", HttpStatus.OK);


    }
}
