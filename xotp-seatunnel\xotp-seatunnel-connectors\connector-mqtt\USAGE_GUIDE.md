# MQTT Connector 使用指南

## 支持的消息格式

MQTT Connector现在支持三种消息格式：

1. **JSON格式（默认）**：`message.format = "json"`
2. **纯文本格式**：`message.format = "text"` 或 `"plain"`
3. **XML格式**：`message.format = "xml"`

## 快速配置示例

### 1. JSON格式配置

```hocon
source {
  plugin_name = "Mqtt"
  broker.urls = ["tcp://***********:11883"]
  topics = ["iot/test/topic"]
  qos = 1
  client.id = "seatunnel-source-001" 
  message.format = "json"  # 可省略，默认就是json
  
  schema = {
    fields = {
      id = "string"
      epoch = "int"
      model_name = "string"
    }
  }
}
```

**对应的JSON消息：**
```json
{
  "id": "sensor001",
  "epoch": 1640995200,
  "model_name": "temperature_sensor"
}
```

### 2. XML格式配置

```hocon
source {
  plugin_name = "Mqtt"
  broker.urls = ["tcp://localhost:1883"]
  topics = ["sensors/xml"]
  qos = 1
  message.format = "xml"
  
  schema = {
    fields = {
      sensor_id = "string"           # 对应<sensor_id>
      temperature = "double"         # 对应<temperature>
      "location.city" = "string"     # 对应<location><city>
    }
  }
}
```

**对应的XML消息：**
```xml
<sensor>
  <sensor_id>temp001</sensor_id>
  <temperature>23.5</temperature>
  <location>
    <city>Beijing</city>
  </location>
</sensor>
```

### 3. 纯文本格式配置

**单字段模式：**
```hocon
source {
  plugin_name = "Mqtt"
  broker.urls = ["tcp://localhost:1883"]
  topics = ["logs/text"]
  qos = 1
  message.format = "text"  # 或者 "plain"
  
  schema = {
    fields = {
      log_message = "string"  # 整个消息内容
    }
  }
}
```

**CSV模式：**
```hocon
source {
  plugin_name = "Mqtt"
  broker.urls = ["tcp://localhost:1883"]
  topics = ["data/csv"]
  qos = 1
  message.format = "text"
  
  schema = {
    fields = {
      name = "string"    # 第1个字段
      age = "int"        # 第2个字段  
      city = "string"    # 第3个字段
    }
  }
}
```

**对应的CSV消息：**`"John,25,Beijing"`

## 重要修复

### 1. 解决导入问题
- ✅ 修正了`TextDeserializationSchema`的导入路径
- ✅ 现在使用`org.apache.seatunnel.format.text.TextDeserializationSchema`

### 2. 解决重复订阅问题
- ✅ 添加了订阅状态管理
- ✅ 防止重复订阅同一个topic
- ✅ 日志不再重复出现"Subscribed to MQTT topic"

### 3. 新增XML支持
- ✅ 添加了`jackson-dataformat-xml`依赖
- ✅ 创建了`MqttXmlDeserializationSchema`类
- ✅ 支持嵌套字段访问（如`location.city`）

## 依赖配置

在`pom.xml`中已添加：

```xml
<dependency>
    <groupId>com.fasterxml.jackson.dataformat</groupId>
    <artifactId>jackson-dataformat-xml</artifactId>
    <version>2.15.2</version>
</dependency>
```

## 使用建议

1. **默认使用JSON格式**：性能最佳，解析最稳定
2. **XML格式**：适合复杂的嵌套数据结构
3. **文本格式**：适合简单的日志消息或CSV数据

## 测试你的配置

使用配置文件：`connector-mqtt/src/test/resources/mqtt-multi-format-example.conf`

修改其中的broker地址和topic，然后运行测试来验证不同格式的解析功能。

## 故障排除

如果遇到解析问题：

1. **检查消息格式**：确保实际消息格式与`message.format`设置一致
2. **检查schema定义**：字段名必须与消息中的字段名完全匹配
3. **XML嵌套字段**：使用点号语法，如`"parent.child"`
4. **查看日志**：启用DEBUG日志查看详细的解析过程

```
log.info("Creating deserialization schema for message format: {}", messageFormat);
``` 
