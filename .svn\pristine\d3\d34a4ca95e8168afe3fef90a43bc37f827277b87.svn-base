package com.xmcares.platform.hookbox.integrator.handler;

import com.xmcares.platform.hookbox.common.job.JobParams;
import com.xmcares.platform.hookbox.common.job.context.JsonJobParams;
import com.xmcares.platform.hookbox.integrator.manager.DatasyncJobManager;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.Date;

/**
 * 数据同步任务 XXlJob的处理器，用于启动数据同步任务
 * 该类只关注与xxl-job的关联的逻辑处理，不需要介入实际业务逻辑
 * <AUTHOR>
 * @since  2025-06-30
 */
@Component
public class XxlJobDatasyncJobHandler {
    // MDC 上下文的键和值
    public static final String XXL_JOB_MDC_KEY = "XXL_JOB_EXECUTOR";
    public static final String XXL_JOB_MDC_VALUE = "true";

    @Resource
    private DatasyncJobManager datasyncJobManager;



    /**
     * 数据同步任务 XXlJob的处理器，用于开始数据同步任务
     */
    @XxlJob(value = "DatasyncJobStartHandler")
    public void handleStartJob() {
        // 在任务开始时设置 MDC 上下文，使线程下所有logger日志接入到xxl-job的日志中---
        MDC.put(XXL_JOB_MDC_KEY, XXL_JOB_MDC_VALUE);
        try {
            startJob();
        } catch (Exception e) {
            XxlJobHelper.handleFail("数据同步任务[XxlJobId = "+XxlJobHelper.getJobId()+"] 开始失败"+ e.getMessage());
        } finally {
            // 在任务结束时清除 MDC 上下文 ---
            MDC.remove(XXL_JOB_MDC_KEY);
        }
    }

    /**
     * 数据同步任务 XXlJob的处理器，用于停止数据同步任务
     */
    @XxlJob(value = "DatasyncJobStopHandler")
    public void handleStopJob() {
        // 在任务开始时设置 MDC 上下文，使线程下所有logger日志接入到xxl-job的日志中---
        MDC.put(XXL_JOB_MDC_KEY, XXL_JOB_MDC_VALUE);
        try {
            stopJob();
        } finally {
            // 在任务结束时清除 MDC 上下文 ---
            //MDC.remove(XXL_JOB_MDC_KEY);
        }
    }



    /**
     * 开始执行数据同步任务。
     * 该方法首先获取当前时间作为触发时间，然后从XxlJobHelper中获取任务ID和参数。
     * 随后，尝试从日志文件名中解析出日志ID，并将所有必要的参数封装到JsonJobParams对象中。
     * 最后，调用datasyncJobManager的startJob方法来启动实际的数据同步作业。
     * 如果在过程中遇到任何异常，将通过XxlJobHelper处理失败情况并记录相应的错误信息；如果成功，则会记录成功的消息。
     */
    protected void startJob() {
        Date triggerTime = new Date();
        long xxlJobId = XxlJobHelper.getJobId();
        String xxlJobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("开始数据同步任务[xxlJobId = {}]，参数：{}", xxlJobId, xxlJobParam);

        JobParams jobParams;
        try {
            jobParams = getJobParams(xxlJobId, triggerTime, triggerTime);
        } catch (Exception e) {
            XxlJobHelper.handleFail("开始数据同步任务[XxlJobId = "+xxlJobId+"]参数序列化失败: "+ e.getMessage());
            return;
        }

        try {
            datasyncJobManager.startJob(jobParams);
        } catch (Exception e) {
            XxlJobHelper.handleFail("开始数据同步任务[XxlJobId = "+xxlJobId+"]失败: "+ e.getMessage());
            return;
        }
        XxlJobHelper.handleSuccess("开始数据同步任务务[XxlJobId = "+xxlJobId+"]成功!!!");
    }

    /**
     * 停止数据同步任务。此方法通过获取当前时间、任务ID及参数，从日志文件名解析出日志ID，并将这些信息封装到JsonJobParams对象中。
     * 使用datasyncJobManager的stopJob方法来停止指定的数据同步作业。如果在处理过程中遇到异常（如无法获取日志ID或参数序列化失败等），
     * 将通过XxlJobHelper处理失败情况并记录相应的错误信息。
     */
    protected void stopJob() {
        Date triggerTime = new Date();
        long xxlJobId = XxlJobHelper.getJobId();
        String xxlJobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("停止数据同步任务[xxlJobId = {}]，参数：{}", xxlJobId, xxlJobParam);

        JobParams jobParams;
        try {
            jobParams = getJobParams(xxlJobId, triggerTime, triggerTime);
        } catch (Exception e) {
            XxlJobHelper.handleFail("开始数据同步任务[XxlJobId = "+xxlJobId+"]参数序列化失败: "+ e.getMessage());
            return;
        }
        try {
            datasyncJobManager.stopJob(jobParams);
        } catch (Exception e) {
            XxlJobHelper.handleFail("停止数据同步任务[XxlJobId = " + xxlJobId + "]失败: " + e.getMessage());
        }

    }


    /**
     * 获取任务参数
     * @param xxlJobId xxl-job任务ID
     * @param scheduleTime 调度时间
     * @param triggerTime 触发时间
     * @return 任务参数
     */
    private static JobParams getJobParams(long xxlJobId, Date scheduleTime, Date triggerTime) {
        String xxlJobParam = XxlJobHelper.getJobParam();
        long xxlJobLogId = getXxlJobLogId();
        JsonJobParams jobParams = new JsonJobParams(xxlJobParam);
        jobParams.putParam(JobParams.KEY_SCHEDULE_ID, xxlJobId);
        jobParams.putParam(JobParams.KEY_SCHEDULE_LOG_ID, xxlJobLogId);
        jobParams.putParam(JobParams.KEY_SCHEDULE_TIME, scheduleTime);// xxl-job无法获取计划任务时间，所以只能使用当前时间;
        jobParams.putParam(JobParams.KEY_TRIGGER_TIME, triggerTime);
        return jobParams;
    }


    /**
     * 从日志文件名中获取日志ID
     * @return 日志ID
     */
    private static long getXxlJobLogId() {
        String jobLogFileName = XxlJobHelper.getJobLogFileName();
        if (jobLogFileName == null) {
            return -1;
        }
        int idx = jobLogFileName.lastIndexOf(File.separator);
        String substring = jobLogFileName.substring(idx + 1, jobLogFileName.length() - 4);
        return Long.parseLong(substring);
    }


}
