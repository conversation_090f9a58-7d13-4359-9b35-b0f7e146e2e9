<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <option name="BUILD_PROCESS_HEAP_SIZE" value="8192" />
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="xotp-openapi-dataview" />
        <module name="xotp-datax-cdc-oraclereader" />
        <module name="xotp-datax-common" />
        <module name="xotp-datax-rabbitmqreader" />
        <module name="xotp-openapi-dataservice" />
        <module name="xotp-datax-rocketmqreader" />
        <module name="xotp-release-admin-integrator" />
        <module name="xotp-release-hookbox-integrator" />
        <module name="xotp-datax-kafkawriter" />
        <module name="xotp-admin-common" />
        <module name="xotp-release-admin" />
        <module name="connector-starter" />
        <module name="connector-mqtt" />
        <module name="xotp-hookbox-governance" />
        <module name="xotp-release-admin-scheduler" />
        <module name="xotp-flinkx-sdk" />
        <module name="xotp-datax-kafkareader" />
        <module name="xotp-hookbox-integrator" />
        <module name="xotp-admin-integrator" />
        <module name="xotp-admin-metadata" />
        <module name="xotp-release-admin-dataservice" />
        <module name="xotp-seatunnel-plugins" />
        <module name="xotp-datax-rocketmqwriter" />
        <module name="xotp-hookbox-common" />
        <module name="xotp-admin-developer" />
        <module name="xotp-datax-ximcreader" />
        <module name="xotp-datax-rabbitmqwriter" />
        <module name="xotp-hookbox-developer" />
        <module name="xotp-admin-scheduler" />
        <module name="xotp-release-hookbox" />
        <module name="xotp-datax-sdk" />
        <module name="connector-artemis" />
        <module name="xotp-admin-governance" />
        <module name="xotp-admin-dataservice" />
        <module name="xotp-release-openapi" />
        <module name="xotp-openapi-client" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="connector-fako" target="1.8" />
      <module name="xotp-flinkx-demo" target="1.8" />
      <module name="xotp-flinkx-quality" target="1.8" />
      <module name="xotp-release-seatunnel-integrator" target="1.8" />
      <module name="xotp-seatunnel-examples" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="connector-artemis" options="-parameters" />
      <module name="connector-fako" options="-parameters" />
      <module name="connector-mqtt" options="-parameters" />
      <module name="connector-starter" options="-parameters" />
      <module name="xotp" options="" />
      <module name="xotp-admin" options="" />
      <module name="xotp-admin-common" options="-parameters" />
      <module name="xotp-admin-dataservice" options="-parameters" />
      <module name="xotp-admin-developer" options="-parameters" />
      <module name="xotp-admin-governance" options="-parameters" />
      <module name="xotp-admin-integrator" options="-parameters" />
      <module name="xotp-admin-metadata" options="-parameters" />
      <module name="xotp-admin-scheduler" options="-parameters" />
      <module name="xotp-datax" options="" />
      <module name="xotp-datax-cdc-oraclereader" options="-parameters" />
      <module name="xotp-datax-common" options="-parameters" />
      <module name="xotp-datax-kafkareader" options="-parameters" />
      <module name="xotp-datax-kafkawriter" options="-parameters" />
      <module name="xotp-datax-rabbitmqreader" options="-parameters" />
      <module name="xotp-datax-rabbitmqwriter" options="-parameters" />
      <module name="xotp-datax-rocketmqreader" options="-parameters" />
      <module name="xotp-datax-rocketmqwriter" options="-parameters" />
      <module name="xotp-datax-sdk" options="-parameters" />
      <module name="xotp-datax-ximcreader" options="-parameters" />
      <module name="xotp-flinkx" options="" />
      <module name="xotp-flinkx-demo" options="-parameters" />
      <module name="xotp-flinkx-quality" options="-parameters" />
      <module name="xotp-flinkx-sdk" options="-parameters" />
      <module name="xotp-hookbox" options="" />
      <module name="xotp-hookbox-common" options="-parameters" />
      <module name="xotp-hookbox-developer" options="-parameters" />
      <module name="xotp-hookbox-governance" options="-parameters" />
      <module name="xotp-hookbox-integrator" options="-parameters" />
      <module name="xotp-openapi" options="" />
      <module name="xotp-openapi-client" options="-parameters" />
      <module name="xotp-openapi-dataservice" options="-parameters" />
      <module name="xotp-openapi-dataview" options="-parameters" />
      <module name="xotp-release" options="" />
      <module name="xotp-release-admin" options="-parameters" />
      <module name="xotp-release-admin-dataservice" options="-parameters" />
      <module name="xotp-release-admin-integrator" options="-parameters" />
      <module name="xotp-release-admin-scheduler" options="-parameters" />
      <module name="xotp-release-hookbox" options="-parameters" />
      <module name="xotp-release-hookbox-integrator" options="-parameters" />
      <module name="xotp-release-openapi" options="-parameters" />
      <module name="xotp-release-seatunnel-integrator" options="-parameters" />
      <module name="xotp-seatunnel-examples" options="-parameters" />
      <module name="xotp-seatunnel-plugins" options="-parameters" />
    </option>
  </component>
</project>