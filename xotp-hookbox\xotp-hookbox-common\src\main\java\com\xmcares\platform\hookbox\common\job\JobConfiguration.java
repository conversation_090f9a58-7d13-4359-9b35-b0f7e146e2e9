/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/19
 */
package com.xmcares.platform.hookbox.common.job;

import com.xmcares.platform.hookbox.common.job.seatunnel.RemoteSeaTunnelJobContextService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * Job 配置
 * <AUTHOR>
 * @since 2.1.0
 */
@Configuration
@EnableConfigurationProperties(JobProperties.class)
public class JobConfiguration {



    @Configuration
    @ConditionalOnProperty(value = "xbdp.hookbox.job.engine", havingValue = "seatunnel", matchIfMissing = true)
    static
    class SeaTunnelConfiguration {

        @Bean
        public JobContextService jobContextService(JobProperties jobProperties,
                                                   @Autowired(required = false) RestTemplate restTemplate) {
            RemoteSeaTunnelJobContextService service = new RemoteSeaTunnelJobContextService();
            if (restTemplate != null) {
                service.setRestTemplate(restTemplate);
            }
            service.setSeaTunnelApiEndpoint(jobProperties.getSeatunnel().getApiEndpoint());
            return service;
        }
    }

}
