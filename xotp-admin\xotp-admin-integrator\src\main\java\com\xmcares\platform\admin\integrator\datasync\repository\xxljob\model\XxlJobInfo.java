/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/3/2
 */
package com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model;

import com.alibaba.fastjson.JSON;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.util.CommonConstants;
import com.xmcares.platform.admin.integrator.common.util.ConstantUtils;
import com.xmcares.platform.admin.integrator.common.util.IntegratorMod;
import com.xmcares.platform.admin.integrator.common.util.MyGlueTypeEnum;
import com.xmcares.platform.admin.integrator.common.util.XxlJobTaskType;
import com.xmcares.platform.admin.integrator.datasync.model.Datasync;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncInstance;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncJob;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.enums.XxlJobIncrementMode;
import com.xmcares.platform.admin.integrator.datasync.util.JsonJobParamBuilder;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Map;

/**
 * 从xxl-job拷贝属性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class XxlJobInfo {

    private int id;                // 主键ID

    private int jobGroup;        // 执行器主键ID
    private String jobDesc;
    private String jobTaskType;    // 20220329 Add ChenYG Job任务的类型 {@link XxlJobTaskType}

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String author;        // 负责人
    private String alarmEmail;    // 报警邮件

    private String scheduleType;            // 调度类型
    private String scheduleConf;            // 调度配置，值含义取决于调度类型
    private String misfireStrategy;            // 调度过期策略

    private String executorRouteStrategy;    // 执行器路由策略
    private String executorHandler;            // 执行器，任务Handler名称
    private String executorParam;            // 执行器，任务参数
    private String executorBlockStrategy;    // 阻塞处理策略
    private int executorTimeout;            // 任务执行超时时间，单位秒
    private int executorFailRetryCount;        // 失败重试次数

    private String glueType;        // GLUE类型	#com.xxl.job.core.glue.GlueTypeEnum
    private String glueSource;        // GLUE源代码
    private String glueRemark;        // GLUE备注
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date glueUpdatetime;    // GLUE更新时间

    private String childJobId;        // 子任务ID，多个逗号分隔

    private int triggerStatus;        // 调度状态：0-停止，1-运行
    private long triggerLastTime;    // 上次调度时间
    private long triggerNextTime;    // 下次调度时间

    private String jvmParam; // 20220330 Add ChenYG Jvm参数 {@link XxlJobTaskType}

    private XxlJobIncrement xxlJobIncrement; // 20220329 Add ChenYG Job任务的类型为增量任务时，必须携带的参数


    public static XxlJobInfo createNewFromByBeginHandler(Datasync baseInfo, String username, int groupId, String email, String param) {
        XxlJobInfo result = new XxlJobInfo();
        result.setJobGroup(groupId);
        result.setJobTaskType(XxlJobTaskType.DATAX_DEF.name());
        result.setJobDesc("[" + baseInfo.getInstanceName()  + "]" + "[将数据从'" + baseInfo.getOrginDatasourceName() + "'迁移到'" + baseInfo.getDestDatasourceName() + "']");
        result.setAuthor("INTG_" + username);
        result.setAlarmEmail(email);
        if (StringUtils.equalsIgnoreCase(baseInfo.getJobMode(), Datasync.JOB_TYPE_STREAMING)) {
            result.setScheduleType(ConstantUtils.SCHEDULER_DEF_STREAMING_SCHEDULE_TYPE);
            result.setScheduleConf("");
        } else {
            result.setScheduleType(ConstantUtils.SCHEDULER_DEF_SCHEDULE_TYPE);
            result.setScheduleConf(baseInfo.getSchedulerExpr());
        }
        result.setMisfireStrategy(ConstantUtils.SCHEDULER_DEF_MISFIRE);
        result.setExecutorRouteStrategy(baseInfo.getRouteStrategy());
        result.setExecutorHandler(ConstantUtils.HOOKBOX_INTEGRATOR_START_HANDLER);
        result.setExecutorParam(param);
        result.setExecutorBlockStrategy(baseInfo.getBlockStrategy());
        result.setExecutorTimeout(baseInfo.getExecutorTimeout());
        result.setExecutorFailRetryCount(baseInfo.getExecutorFailRetryCount());
        result.setGlueType(MyGlueTypeEnum.BEAN.getDesc());
        return result;
    }

    public static XxlJobInfo createUpdateFromByBeginHandler(String dispatchId, String jobDesc, Datasync baseInfo, String username, int groupId, String email, String param) {
        XxlJobInfo result = new XxlJobInfo();
        result.setId(Integer.parseInt(dispatchId));
        result.setJobGroup(groupId);
        result.setJobTaskType(XxlJobTaskType.DATAX_DEF.name());
        result.setJobDesc(jobDesc);
        result.setAuthor("INTG_" + username);
        result.setAlarmEmail(email);
        result.setScheduleType(ConstantUtils.SCHEDULER_DEF_SCHEDULE_TYPE);
        result.setScheduleConf(baseInfo.getSchedulerExpr());
        result.setMisfireStrategy(ConstantUtils.SCHEDULER_DEF_MISFIRE);
        result.setExecutorRouteStrategy(baseInfo.getRouteStrategy());
        result.setExecutorHandler(ConstantUtils.HOOKBOX_INTEGRATOR_START_HANDLER);
        result.setExecutorParam(param);
        result.setExecutorBlockStrategy(baseInfo.getBlockStrategy());
        result.setExecutorTimeout(baseInfo.getExecutorTimeout());
        result.setExecutorFailRetryCount(baseInfo.getExecutorFailRetryCount());
        result.setGlueType(MyGlueTypeEnum.BEAN.getDesc());
        return result;
    }

    public static XxlJobInfo buildTriggerJob(DatasyncJob byDispatchId) {
        // 触发调度任务
        XxlJobInfo result = new XxlJobInfo();
        result.setId(Integer.parseInt(byDispatchId.getScheduleId()));
        result.setExecutorParam(byDispatchId.getExecutorParams());
        return result;
    }

    public static XxlJobInfo buildStopDatasyncTriggerJob(DatasyncJob byDispatchId) {
        XxlJobInfo result = new XxlJobInfo();
        // 停止任务的ID，目前为 1 ，先写死，来源于XXL-JOB对应的xxl-job-info数据库
        result.setId(1);
        // 2. 使用简化的JSON格式构建参数
        JsonJobParamBuilder paramBuilder = new JsonJobParamBuilder();
        // 2.1. 设置任务ID（原DATASYNC_INSTANCE_ID）
        paramBuilder.setJobId(byDispatchId.getId());
        result.setExecutorParam(paramBuilder.build());
        return result;

    }


    public XxlJobInfo buildIncrement(Map<String, Object> entity, IntegratorMod fromMod, String datasourceId) {
        if (entity.containsKey(ConstantUtils.READER_MODE) && ObjectUtils.isEmpty(entity.get(ConstantUtils.READER_MODE))
                && entity.get(ConstantUtils.READER_MODE).toString().toLowerCase().equals("all")) {
            return this;
        }
        // 仅用户在设置了 incMode 时 才启用增量
        if (entity.containsKey(ConstantUtils.INCREMENT_MODE_KEY) && ObjectUtils.isNotEmpty(entity.get(ConstantUtils.INCREMENT_MODE_KEY))) {
            // 从全量实体中 读取参数成 增量对象
            XxlJobIncrement jobIncrement = JSON.parseObject(JSON.toJSONString(entity), XxlJobIncrement.class);
            if (jobIncrement.getIncMode().equalsIgnoreCase(XxlJobIncrementMode.TIME.name())) {
                if (StringUtils.isEmpty(jobIncrement.getReplaceParam())) {
                    throw new BusinessException("请设置需要替换的动态参数");
                }
                if (StringUtils.isEmpty(jobIncrement.getPrimaryKey())) {
                    throw new BusinessException("请设置需要作为增量使用的字段名称");
                }
                if (jobIncrement.getIncStartTime() == null) {
                    throw new BusinessException("当配置了时间增量后， 开始时间不允许为空");
                }
                if (StringUtils.isEmpty(jobIncrement.getReplaceParamType())) {
                    throw new BusinessException("当配置了时间增量后，需要设置时间的格式");
                }
                this.setJobTaskType(XxlJobTaskType.DATAX_INC.name());
            } else if (jobIncrement.getIncMode().equalsIgnoreCase(XxlJobIncrementMode.PRIMARY_KEY.name())) {
                if (fromMod == IntegratorMod.PLUGIN) {
                    throw new BusinessException("自定义插件不支持增量ID模式");
                }
                if (StringUtils.isEmpty(jobIncrement.getReplaceParam())) {
                    throw new BusinessException("请设置需要替换的动态参数");
                }
                if (StringUtils.isEmpty(jobIncrement.getPrimaryKey())) {
                    throw new BusinessException("请设置需要作为增量使用的字段名称");
                }
                if (jobIncrement.getIncStartValue() == null) {
                    throw new BusinessException("当配置了值增量后， 开始值不允许为空");
                }
                if (entity.containsKey(CommonConstants.DATASOURCE_COMMON_PARAM_TABLE)) {
                    jobIncrement.setReaderTable(entity.get(CommonConstants.DATASOURCE_COMMON_PARAM_TABLE).toString());
                } else {
                    throw new BusinessException("当配置了值增量后， 请设置数据源 表的Key为 " + CommonConstants.DATASOURCE_COMMON_PARAM_TABLE);
                }
                jobIncrement.setDatasourceId(datasourceId);
                this.setJobTaskType(XxlJobTaskType.DATAX_INC.name());
            } else if (jobIncrement.getIncMode().equalsIgnoreCase(XxlJobIncrementMode.HIVE.name())) {
                if (StringUtils.isEmpty(jobIncrement.getPartitionInfo())) {
                    throw new BusinessException("Hive分区信息不允许为空");
                }
                this.setJobTaskType(XxlJobTaskType.DATAX_INC.name());
            }
            this.xxlJobIncrement = jobIncrement;
        }
        return this;
    }

    public XxlJobInfo buildJvm(Map<String, Object> entity) {
        if (entity.containsKey(ConstantUtils.DATAX_FIELDS_JVM) && entity.get(ConstantUtils.DATAX_FIELDS_JVM) != null) {
            this.jvmParam = entity.get(ConstantUtils.DATAX_FIELDS_JVM).toString();
        }
        return this;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getJobGroup() {
        return jobGroup;
    }

    public void setJobGroup(int jobGroup) {
        this.jobGroup = jobGroup;
    }

    public String getJobDesc() {
        return jobDesc;
    }

    public void setJobDesc(String jobDesc) {
        this.jobDesc = jobDesc;
    }

    public String getJobTaskType() {
        return jobTaskType;
    }

    public void setJobTaskType(String jobTaskType) {
        this.jobTaskType = jobTaskType;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getAlarmEmail() {
        return alarmEmail;
    }

    public void setAlarmEmail(String alarmEmail) {
        this.alarmEmail = alarmEmail;
    }

    public String getScheduleType() {
        return scheduleType;
    }

    public void setScheduleType(String scheduleType) {
        this.scheduleType = scheduleType;
    }

    public String getScheduleConf() {
        return scheduleConf;
    }

    public void setScheduleConf(String scheduleConf) {
        this.scheduleConf = scheduleConf;
    }

    public String getMisfireStrategy() {
        return misfireStrategy;
    }

    public void setMisfireStrategy(String misfireStrategy) {
        this.misfireStrategy = misfireStrategy;
    }

    public String getExecutorRouteStrategy() {
        return executorRouteStrategy;
    }

    public void setExecutorRouteStrategy(String executorRouteStrategy) {
        this.executorRouteStrategy = executorRouteStrategy;
    }

    public String getExecutorHandler() {
        return executorHandler;
    }

    public void setExecutorHandler(String executorHandler) {
        this.executorHandler = executorHandler;
    }

    public String getExecutorParam() {
        return executorParam;
    }

    public void setExecutorParam(String executorParam) {
        this.executorParam = executorParam;
    }

    public String getExecutorBlockStrategy() {
        return executorBlockStrategy;
    }

    public void setExecutorBlockStrategy(String executorBlockStrategy) {
        this.executorBlockStrategy = executorBlockStrategy;
    }

    public int getExecutorTimeout() {
        return executorTimeout;
    }

    public void setExecutorTimeout(int executorTimeout) {
        this.executorTimeout = executorTimeout;
    }

    public int getExecutorFailRetryCount() {
        return executorFailRetryCount;
    }

    public void setExecutorFailRetryCount(int executorFailRetryCount) {
        this.executorFailRetryCount = executorFailRetryCount;
    }

    public String getGlueType() {
        return glueType;
    }

    public void setGlueType(String glueType) {
        this.glueType = glueType;
    }

    public String getGlueSource() {
        return glueSource;
    }

    public void setGlueSource(String glueSource) {
        this.glueSource = glueSource;
    }

    public String getGlueRemark() {
        return glueRemark;
    }

    public void setGlueRemark(String glueRemark) {
        this.glueRemark = glueRemark;
    }

    public Date getGlueUpdatetime() {
        return glueUpdatetime;
    }

    public void setGlueUpdatetime(Date glueUpdatetime) {
        this.glueUpdatetime = glueUpdatetime;
    }

    public String getChildJobId() {
        return childJobId;
    }

    public void setChildJobId(String childJobId) {
        this.childJobId = childJobId;
    }

    public int getTriggerStatus() {
        return triggerStatus;
    }

    public void setTriggerStatus(int triggerStatus) {
        this.triggerStatus = triggerStatus;
    }

    public long getTriggerLastTime() {
        return triggerLastTime;
    }

    public void setTriggerLastTime(long triggerLastTime) {
        this.triggerLastTime = triggerLastTime;
    }

    public long getTriggerNextTime() {
        return triggerNextTime;
    }

    public void setTriggerNextTime(long triggerNextTime) {
        this.triggerNextTime = triggerNextTime;
    }

    public String getJvmParam() {
        return jvmParam;
    }

    public void setJvmParam(String jvmParam) {
        this.jvmParam = jvmParam;
    }

    public XxlJobIncrement getXxlJobIncrement() {
        return xxlJobIncrement;
    }

    public void setXxlJobIncrement(XxlJobIncrement xxlJobIncrement) {
        this.xxlJobIncrement = xxlJobIncrement;
    }
}
