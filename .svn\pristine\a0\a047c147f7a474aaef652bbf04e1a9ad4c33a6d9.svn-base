import React, {useEffect, useImperativeHandle, useMemo, useRef, useState} from 'react';
import {<PERSON>ton, Col, Divider, Dropdown, Modal, Row, Space, Tag, Typography} from 'antd';

import ProCard from '@ant-design/pro-card';
import <PERSON>Form, {ProFormRadio, ProFormSelect, ProFormText, StepsForm} from '@ant-design/pro-form';
import {EditableProTable} from '@ant-design/pro-table';
import CronSelect from "./CronSelect";
import * as styles from "@/pages/modules/Integrate/DataSync/DataSync.less";
import {cronRegex} from './cron/cron-regex';
import {
    datasChildJobIdList,
    datasyncAdd,
    datasyncPluginGet,
    datasyncPluginList,
    datasyncSourceGet,
    datasyncSourceList,
    datasyncSourceTableList,
    datasyncUpdate
} from '@/services/Integrate/DataSync/DataSync';
import {getDataSource} from "@/services/Metadata/DataSource/DataSource"
import {messageModal} from "@/utils/messageModal";
import * as utils from "@/utils/utils";
import AutoFromRender from './AutoFormRender'
import {DownOutlined, ThunderboltOutlined, CloudSyncOutlined, InfoCircleOutlined} from '@ant-design/icons';
import EditableDragableTable from "@/pages/modules/Integrate/DataSync/components/editableDragableTable";
import AsyncTable from "@/pages/modules/Integrate/DataSync/components/AsyncTable";

import {CheckCard} from '@ant-design/pro-components';

// 其他导入保持不变...

const {Paragraph, Title} = Typography;

export default ({wrappedComponentRef, handleSearch}) => {

    const formRef = useRef();
    const childRef1 = useRef();
    const childRef2 = useRef();
    const childRef3 = useRef();
    const childRef4 = useRef();
    const childRef5 = useRef();
    const childRef6 = useRef();

    const [sourceFormLoading, setSourceFormLoading] = useState(false);
    const [gotoFormLoading, setGotoFormLoading] = useState(false);
    const [isModalVisible, setIsModalVisible] = useState(false);  //弹窗是否打开
    //

    const [dataSource, setDataSource] = useState([]);


    const [childJobId, setChildJobId] = useState([])
    const [childJobIdKeys, setchildJobIdKeys] = useState(() =>
        childJobId.map((item) => item.ownId));
    const [gotoDataSource, setGotoDataSource] = useState([]);

    const [editableSourceKeys, setEditableSourceRowKeys] = useState(() =>
        dataSource.map((item) => item.ownId));

    const [editableGotoKeys, setEditableGotoRowKeys] = useState(() =>
        gotoDataSource.map((item) => item.ownId));

    const [sourceTab, setSourceTab] = useState('tab1'); // 设置数据源tab
    const [gotoTab, setGotoTab] = useState('t1');  // 设置数据去向tab

    const [defaultSourceRadio, setSourceRadio] = useState('0'); // 设置数据源同步组件Radio
    const [defaultGotoRadio, setGotoRadio] = useState('0');   // 设置数据去向同步组件Radio

    const [selectList, setSelectList] = useState([]);   // 选择当前数据源列表
    const [selectGotoList, setSelectGotoList] = useState([]);  // 选择当前数据去向列表


    const [current, setCurrent] = useState(0); // 设置当前第几步

    const [sourceModel, setSourceModel] = useState(null);   // 数据源设置下拉列表选择获取的数据


    const [gotoModel, setGotoModel] = useState(null);    // 数据去向设置下拉列表选择获取的数据


    const [destDatasourceName, setDestDatasourceName] = useState('');  // 数据源设置当前下拉列表选择后的名称
    const [orginDatasourceName, setOrginDatasourceName] = useState(''); // 数据去向设置当前下拉列表选择后的名称

    const [allData, setAllData] = useState({}); // 表单所有数据
    const [markAdd, setMarkAdd] = useState(0); // 判断是编辑还是新增
    const [cronExpression, setCron] = useState('0 0 0 * * ? *');

    const [sourceUseWay, setSourceUseWay] = useState('数据源'); // 设置来源数据方式名称
    const [destUseWay, setDestUseWay] = useState('数据源'); // 设置去向数据方式名称
    const [jobSource, setJobSource] = useState([]);
    const [originDatasourceId, setOriginDatasourceId] = useState(null);
    const [destDatasourceId, setDestDatasourceId] = useState(null);
    const [jobMode, setJobMode] = useState("STREAMING")  // 模式

    useImperativeHandle(wrappedComponentRef, () => ({
        handleMinModalVisible: async (flag, title, mark, data) => {
            // 打开弹窗请求
            if (flag) {
                getChildJobIdList();
                setChildJobId([])
                setchildJobIdKeys([])
            }

            setIsModalVisible(flag);
            setMarkAdd(mark)
            if (mark == 0) {
                await getSelectList(defaultSourceRadio, 0);

            }
            if (mark == 1) {
                setSourceRadio(data?.orginType);
                await setAllData(data);
                setGotoRadio(data?.destType);
                await getSelectList(data?.orginType, 0)
                formRef?.current?.setFieldsValue({
                    ...data
                });

                setSourceTab('tab1')
                setOriginDatasourceId(data?.orginDatasourceId)
                setDestDatasourceId(data?.destDatasourceId)
                await getModels(data?.orginDatasourceId, 0, data?.orginType);
                await getModels(data?.destDatasourceId, 1, data?.destType);
                if (data?.orginColumnJson) {
                    const {orginColumnJson} = data;
                    if (typeof orginColumnJson == "string") {
                        let ids = []
                        let d = JSON.parse(orginColumnJson)?.map(item => {
                            const ownId = (Math.random() * 1000000000).toFixed(0);
                            ids.push(ownId);
                            return {ownId, ...item}
                        });

                        setEditableSourceRowKeys(ids);
                        setDataSource(d)
                    }
                }

                if (data?.childJobid) {
                    let childJobIdArrs = []
                    let ids = []
                    const dataArr = data.childJobid.split(",");
                    childJobIdArrs = dataArr.map((item, i) => {
                        const ownId = (Math.random() * 1000000000).toFixed(0);
                        ids.push(ownId)
                        const newItem = {
                            ownId,
                            index: i,
                            task: item,
                        }
                        return newItem
                    })
                    setChildJobId(childJobIdArrs)
                    setchildJobIdKeys(ids)
                }

                if (data?.destColumnJson) {
                    const {destColumnJson} = data;
                    if (typeof destColumnJson == "string") {
                        let ids = []
                        let d = JSON.parse(destColumnJson)?.map(item => {
                            const ownId = (Math.random() * 1000000000).toFixed(0);
                            ids.push(ownId);
                            return {ownId, ...item}
                        });

                        setEditableGotoRowKeys(ids);
                        setGotoDataSource(d)
                    }
                }
            }
        }
    }), [formRef])
    const save = (data) => {
        formRef.current.setFieldsValue({
            schedulerExpr: data
        });
        setCron(data);
    };

    // 判断获取数据源还是插件列表
    const getSelectList = async (mark, current) => {
        let res;
        if (mark == 0) {
            res = await getSourceList();
        } else {
            res = await getPluginList();
        }
        if (res && res.length > 0) {
            const data = res.map(item => {
                if (mark == 0) {
                    return {label: item.name, value: item.id, type: item.type}
                } else {
                    return {label: item.modelName, value: item.id, type: item.type}
                }
            });
            if (current == 0) {
                setSelectList(data);
            }
            if (current == 1) {
                setSelectGotoList(data)
            }
        }
    }

    // 获取数据源列表
    const getSourceList = async () => {
        const res = await datasyncSourceList();
        return res;
    }

    // 获取数据表字段
    const getTableColumns = async (data) => {
        const res = await datasyncSourceTableList(data);
        return res
    }

    const getChildJobIdList = async () => {
        const res = await datasChildJobIdList();
        if (res && res.length > 0) {
            const data = res.map(item => {
                return {
                    label: item.instanceName, value: item.dispatchId
                }
            });
            setJobSource(data)
        }
    }

    // 获取插件列表
    const getPluginList = async () => {
        const res = await datasyncPluginList({
            integrationWay: 1,
            integrationType: current
        });
        return res;
    }

    //判断是查询数据源还是插件模板信息，mark：0代表数据来源，1代表数据去向
    const getModels = async (id, mark, radio) => {
        let res;
        if (mark == 1) {
            setSourceFormLoading(true);
        }
        if (mark == 2) {
            setGotoFormLoading(true);
        }

        if (radio == '0') {
            res = await getSourceModel({
                datasourceId: id,
                type: mark-1
            });
        } else {
            res = await getPluginModel({
                id,
            })
        }
        if (!!res) {
            if (mark === 1) {
                // const {columnJson,...rest}=res;
                setSourceModel({...res});
                setSourceFormLoading(false);
            }
            if (mark === 2) {
                setGotoModel({...res});
                setGotoFormLoading(false);
            }
        } else {
            // 没有需要清除
            if (mark === 1) {
                // const {columnJson,...rest}=res;
                setSourceModel(null);
                setSourceFormLoading(false);
            }
            if (mark === 2) {
                setGotoModel(null);
                setGotoFormLoading(false);
            }
        }
        if (res === undefined) {
            if (mark === 0) {
                setSourceFormLoading(false);
            }
            if (mark === 1) {
                setGotoFormLoading(false);
            }
        }
    }


    //数据源选择获取模板信息
    const getSourceModel = async (data) => {
        const res = await datasyncSourceGet({
            ...data
        });
        return res
    }

    //插件选择获取模板信息
    const getPluginModel = async (data) => {
        const res = await datasyncPluginGet({
            ...data
        });
        return res
    }

    const returnJson = (str) => {
        if (typeof str == 'string') {
            return str ? JSON.parse(str) : undefined
        }
        if (typeof str == "object") {
            return str
        }
        return undefined
    }

    const onClose = () => {
        setIsModalVisible(false);
        setSourceModel(null);
        setGotoModel(null);
        setCurrent(0);
        setSourceTab('tab1');
        setGotoTab('t1');
        setSourceRadio('0');
        setGotoRadio('0')
        setAllData({});
        setDestDatasourceName('');
        setOrginDatasourceName('');
        setDataSource([]);
        setGotoDataSource([]);
        setSourceFormLoading(false);
        setGotoFormLoading(false);
        setDestDatasourceId(null);
        setOriginDatasourceId(null);
        setJobMode("BATCH"); // 重置处理模式状态
    }

    const handleCronItemMenuClick = (e) => {
        console.log('click', e);
        setCron(e.key);
        formRef.current.setFieldsValue({
            schedulerExpr: e.key
        });
    };

    const cronItems = [
        {
            label: '每天凌晨执行一次',
            key: '0 0 0 * * ? *',
        },
        {
            label: '每小时执行一次',
            key: '0 0 * * * ? *',
        },
        {
            label: '每分钟执行一次',
            key: '0 * * * * ? *',
        },
        {
            label: '每月1号凌晨执行一次',
            key: '0 0 0 1 * ? *',
        },
    ];

    const cronItemMenuProps = {
        items: cronItems,
        onClick: handleCronItemMenuClick,
    };

    // 设置表名称
    const setTableName = (name, mark) => {
        console.log(name, mark)
        if (mark === "origin") {
            const oldV = childRef1?.current?.getValues();
            childRef1?.current?.setValues({...oldV, table: name})
        }
        if (mark === "dest") {
            const oldV = childRef4?.current?.getValues();
            childRef4?.current?.setValues({...oldV, table: name})
        }

    }

    const DynamicExpressionGuide = () => (
        <ProCard
            style={{ marginBottom: 16, width: "100%" }}
            title={
                <Space>
                    <InfoCircleOutlined />
                    <span>动态表达式使用说明</span>
                </Space>
            }
            headerBordered
            collapsible
            defaultCollapsed={true}
        >
            <div style={{ padding: '8px 0' }}>
                <Typography.Title level={5}>1. 动态参数</Typography.Title>
                <Typography.Paragraph>
                    <Typography.Text strong>使用格式：</Typography.Text> <Typography.Text code>${'{参数名}'}</Typography.Text>
                </Typography.Paragraph>
                <Typography.Paragraph>
                    <Typography.Text strong>系统内置参数：</Typography.Text>
                    <ul style={{ marginBottom: '8px' }}>
                        <li><Typography.Text code>${'{triggerTime}'}</Typography.Text>：任务触发时间</li>
                    </ul>
                </Typography.Paragraph>
                <Typography.Paragraph>
                    <Typography.Text strong>用户自定义参数：</Typography.Text>
                    <ul style={{ marginBottom: '8px' }}>
                        <li>例如：<Typography.Text code>${'{startDate}'}</Typography.Text>、<Typography.Text code>${'{bizType}'}</Typography.Text> 等（需在任务参数中定义）</li>
                    </ul>
                </Typography.Paragraph>

                <Divider style={{ margin: '12px 0' }} />

                <Typography.Title level={5}>2. 动态函数</Typography.Title>
                <Typography.Paragraph>
                    <Typography.Text strong>使用格式：</Typography.Text> <Typography.Text code>$函数名{'{参数1, 参数2, ...}'}</Typography.Text>
                </Typography.Paragraph>
                <Typography.Paragraph>
                    <Typography.Text strong>目前支持函数：</Typography.Text>
                    <ul style={{ marginBottom: '8px' }}>
                        <li><Typography.Text code>$FMT_DT{'{时间参数名, 格式化字符串, 偏移量}'}</Typography.Text>：用于对日期时间参数进行格式化和偏移计算</li>
                    </ul>
                </Typography.Paragraph>
                <Typography.Paragraph>
                    <Typography.Text strong>示例说明：</Typography.Text>
                    <ul style={{ marginBottom: '8px' }}>
                        <li><Typography.Text code>$FMT_DT{'{triggerTime, yyyy-MM-dd, -1d}'}</Typography.Text>，表示触发时间前一天的日期，格式为 yyyy-MM-dd</li>
                        <li><Typography.Text code>$FMT_DT{'{triggerTime, yyyyMMddHHmm, -15m}'}</Typography.Text>，表示触发时间前15分钟，格式为 yyyyMMddHHmm</li>
                    </ul>
                </Typography.Paragraph>
                <Typography.Paragraph>
                    <Typography.Text strong>偏移单位说明：</Typography.Text>
                    <ul style={{ marginBottom: '8px' }}>
                        <li>1）支持单位：y 年、M 月、d 天、h 小时、m 分钟、s 秒</li>
                        <li>2）正数表示向后推，负数表示向前推</li>
                    </ul>
                </Typography.Paragraph>
            </div>
        </ProCard>
    );
    const renderModeSelection= () => {
        return (
            <StepsForm.StepForm
                name="selectMode"
                title="处理模式"
                onFinish={async () => {
                    return true;
                }}
            >
                <Typography>
                    <Title level={5}>请选择数据处理模式：</Title>
                    <Paragraph>根据您的业务需求，选择合适的数据处理方式。</Paragraph>
                </Typography>

                <CheckCard.Group
                    onChange={(value) => {
                        setJobMode(value);
                        console.log('已选择模式:', value);
                    }}
                    value={jobMode}
                    style={{ width: '100%' }}
                >
                    <CheckCard
                        title="批处理模式 (Batch)"
                        description="一次性处理大量数据，适合定时任务和历史数据处理。批处理提供更高的吞吐量和资源利用率。"
                        value="BATCH"
                        style={{ width: '48%', marginRight: '2%' }}
                        avatar={<ThunderboltOutlined style={{ fontSize: '28px', color: '#1890ff' }} />}
                    />
                    <CheckCard
                        title="流处理模式 (Streaming)"
                        description="实时处理数据流，适合需要即时反馈和低延迟场景。流处理提供更快的响应时间和连续性处理能力。"
                        value="STREAMING"
                        style={{ width: '48%' }}
                        avatar={<CloudSyncOutlined style={{ fontSize: '28px', color: '#52c41a' }} />}
                    />
                </CheckCard.Group>

                <div style={{ margin: "20px 0px" }}>
                    {jobMode === 'BATCH' && (
                        <div className="mode-details" style={{ backgroundColor: '#f0f5ff', padding: 16, borderRadius: 8 }}>
                            <Typography>
                                <Paragraph strong>批处理模式特点：</Paragraph>
                                <ul>
                                    <li>高吞吐量，适合大规模数据处理</li>
                                    <li>资源利用效率高，计算成本较低</li>
                                    <li>适合有明确时间窗口的处理任务</li>
                                    <li>通常用于ETL、报表生成、数据分析等场景</li>
                                </ul>
                            </Typography>
                        </div>
                    )}

                    {jobMode === 'STREAMING' && (
                        <div className="mode-details" style={{ backgroundColor: '#f6ffed', padding: 16, borderRadius: 8 }}>
                            <Typography>
                                <Paragraph strong>流处理模式特点：</Paragraph>
                                <ul>
                                    <li>低延迟，实时数据处理和响应</li>
                                    <li>连续性处理，支持实时监控和预警</li>
                                    <li>适合需要即时反馈的业务场景</li>
                                    <li>通常用于实时分析、监控仪表盘、异常检测等场景</li>
                                </ul>
                            </Typography>
                        </div>
                    )}
                </div>
            </StepsForm.StepForm>
        )
    };

    const renderStepFirst = useMemo(() => {
        return (
            <StepsForm.StepForm
                name="source"
                initialValues={{
                    orginType: defaultSourceRadio,
                }}
                title="数据来源"
                onFinish={async () => {
                    return true;
                }}
                onValuesChange={async (_, values) => {
                    // 判断radio变化
                    if (values.orginType !== undefined) {
                        if (values.orginType == '0') {
                            setSourceUseWay('数据源')
                        } else {
                            setSourceUseWay('插件')
                        }
                        if (values.orginType != defaultSourceRadio) {
                            formRef.current.setFieldsValue({
                                orginDatasourceId: null
                            });
                            setSourceModel(null);
                            await getSelectList(values.orginType, 0);
                        }
                        await setSourceRadio(values.orginType);
                    }
                }}
            >
                <ProForm.Group>
                    <ProFormRadio.Group
                        name="orginType"
                        width="md"
                        layout={"horizontal"}
                        label="同步组件"
                        fieldProps={{disabled: markAdd == 1}}
                        options={[
                            {
                                label: '内置',
                                value: '0',
                            },
                            {
                                label: '插件',
                                value: '1',
                            },
                        ]}
                    />
                    <ProFormSelect
                        name="orginDatasourceId"
                        options={selectList}
                        showSearch
                        fieldProps={{
                            disabled: markAdd == 1,
                            filterOption: (input, option) =>
                                (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
                            onSelect: async (x, option) => {
                                console.log(option)
                                if (markAdd == 0) {
                                    formRef.current.setFieldsValue({
                                        intgName: `${option.label}->${destDatasourceName}`
                                    });
                                }

                                setOriginDatasourceId(option?.key)

                                setOrginDatasourceName(option.label);
                                // ximc、rabbitMQ 新增选择时自动带入账号、密码
                                if (["ximc", "rabbitmq"].includes(option?.type?.toLowerCase())) {
                                    const res = await getDataSource({id: option.value});
                                    setAllData({
                                        orginBaseJson: res?.options || {}
                                    })
                                    console.log(res, option)
                                }

                                await getModels(option.value, current, defaultSourceRadio);
                            },
                            onClear: () => {
                                setOriginDatasourceId(null)
                            },
                            optionItemRender: (option) => {
                                return (<Space align={"end"}>
                                        {option.label || ""}
                                        <Tag color="#1677FF">{option.type}</Tag>
                                    </Space>
                                )
                            }
                        }}
                        width={"md"}
                        label={sourceUseWay}
                        placeholder="请选择"
                        rules={[{required: true, message: '不能为空!'}]}
                    />
                </ProForm.Group>

                <ProCard
                    tabs={{
                        value: sourceTab,
                        type: 'card',
                        onChange: async (key) => {
                            setSourceTab(key);
                        },
                    }}
                    style={{marginTop: 8, width: "800px"}}
                >
                    <ProCard.TabPane key="tab1" tab="基础信息">
                        <AutoFromRender ref={childRef1} formLoading={sourceFormLoading} tab={sourceTab} id={'1'}
                                        schema={returnJson(sourceModel?.baseJson)} data={{
                            ...JSON.parse(allData?.orginBaseJson || '{}')
                        }}/>
                        <AsyncTable datasourceId={originDatasourceId || null}
                                    setTableName={(name) => setTableName(name, "origin")}/>
                    </ProCard.TabPane>
                    <ProCard.TabPane key="tab2" tab="进阶信息">
                        <AutoFromRender ref={childRef2} formLoading={sourceFormLoading} tab={sourceTab} id={'2'}
                                        schema={returnJson(sourceModel?.advJson)} data={{
                            ...JSON.parse(allData?.orginAdvJson || '{}')
                        }}/>
                    </ProCard.TabPane>
                    <ProCard.TabPane key="tab3" tab="高级信息">
                        <AutoFromRender ref={childRef3} formLoading={sourceFormLoading} tab={sourceTab} id={'3'}
                                        schema={returnJson(sourceModel?.highJson)} data={{
                            ...JSON.parse(allData?.orginHighJson || '{}')
                        }}/>
                    </ProCard.TabPane>
                </ProCard>
                <DynamicExpressionGuide />
            </StepsForm.StepForm>
        )
    }, [defaultSourceRadio, markAdd, selectList, {...sourceModel}, sourceFormLoading]);


    const renderStepSecond = useMemo(() => {
        return (
            <StepsForm.StepForm
                name="goto"
                initialValues={{
                    destType: defaultGotoRadio,
                }}
                onValuesChange={async (_, values) => {
                    if (values.destType == '0') {
                        setDestUseWay('数据源')
                    } else {
                        setDestUseWay('插件')
                    }
                    if (values.destType !== undefined) {
                        if (values.destType != defaultGotoRadio) {
                            formRef.current.setFieldsValue({
                                destDatasourceId: null
                            });
                            setGotoModel(null);

                            await getSelectList(values.destType, 1);
                        }
                        await setGotoRadio(values.destType);

                    }
                }}
                title="数据去向"
                onFinish={async () => {
                    return true;
                }}
            >
                <ProForm.Group>
                    <ProFormRadio.Group
                        name="destType"
                        width="md"
                        label="同步组件"
                        fieldProps={{disabled: markAdd == 1}}
                        options={[
                            {
                                label: '内置',
                                value: '0',
                            },
                            {
                                label: '插件',
                                value: '1',
                            },
                        ]}
                    />
                    <ProFormSelect
                        name="destDatasourceId"
                        width="sm"
                        label={destUseWay}
                        showSearch
                        fieldProps={{
                            disabled: markAdd == 1,
                            onSelect(x, option) {
                                setDestDatasourceName(option.label);
                                setDestDatasourceId(option?.key)
                                if (markAdd == 0) {
                                    formRef.current.setFieldsValue({
                                        intgName: `${orginDatasourceName}->${option.label}`
                                    });
                                }
                                getModels(option.value, current, defaultGotoRadio)
                            },
                            onClear: () => {
                                setDestDatasourceId(null)
                            },
                            filterOption: (input, option) =>
                                (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
                            optionItemRender: (option) => {

                                return (<Space align={"end"}>
                                        {option.label || ""}
                                        <Tag color="#1677FF">{option.type}</Tag>
                                    </Space>

                                )
                            }
                        }}
                        width={"md"}
                        options={selectGotoList}
                        placeholder="请选择"
                        rules={[{required: true, message: '不能为空!'}]}
                    />
                </ProForm.Group>
                <ProCard
                    tabs={{
                        value: gotoTab,
                        type: 'card',
                        onChange: (key) => {
                            setGotoTab(key);
                        },
                    }}
                    style={{marginTop: 8, width: "800px"}}
                >
                    <ProCard.TabPane key="t1" tab="基础信息">
                        <AutoFromRender ref={childRef4} formLoading={gotoFormLoading} id={'4'} tab={gotoTab}
                                        schema={returnJson(gotoModel?.baseJson)} data={{
                            ...JSON.parse(allData?.destBaseJson || '{}')
                        }}/>
                        <AsyncTable datasourceId={destDatasourceId || null}
                                    setTableName={(name) => setTableName(name, "dest")}/>
                    </ProCard.TabPane>
                    <ProCard.TabPane key="t2" tab="进阶信息">
                        <AutoFromRender ref={childRef5} formLoading={gotoFormLoading} id={'5'} tab={gotoTab}
                                        schema={returnJson(gotoModel?.advJson)} data={{
                            ...JSON.parse(allData?.destAdvJson || '{}')
                        }}/>
                    </ProCard.TabPane>
                    <ProCard.TabPane key="t3" tab="高级信息">
                        <AutoFromRender ref={childRef6} formLoading={gotoFormLoading} id={'6'} tab={gotoTab}
                                        schema={returnJson(gotoModel?.highJson)} data={{
                            ...JSON.parse(allData?.destHighJson || '{}')
                        }}/>
                    </ProCard.TabPane>
                </ProCard>
                <DynamicExpressionGuide />
            </StepsForm.StepForm>
        )
    }, [defaultGotoRadio, markAdd, selectGotoList, {...gotoModel}], gotoFormLoading)
    const renderStepThird = () => {
        return (
            <StepsForm.StepForm
                name="shine"
                title="数据映射"
            >
                <Space align="start">
                    <ProForm.Item
                        label="数据来源"
                        name="orginColumnJson"
                        initialValue={dataSource}
                        trigger="onValuesChange"
                    >
                        <EditableDragableTable
                            id={"ownId"}
                            formColumns={sourceModel?.columnJson ? JSON.parse(sourceModel?.columnJson) : []}
                            onChange={setEditableSourceRowKeys}
                            editableKeys={editableSourceKeys}
                            onValuesChange={setOrginColumnJsonProps}
                            dataSource={dataSource}
                        />

                    </ProForm.Item>
                    <ProForm.Item
                        label="数据去向"
                        name="destColumnJson"
                        initialValue={gotoDataSource}
                        trigger="onValuesChange"
                    >
                        <EditableDragableTable
                            id={"ownId"}
                            formColumns={gotoModel?.columnJson ? JSON.parse(gotoModel?.columnJson) : []}
                            onChange={setEditableGotoRowKeys}
                            editableKeys={editableGotoKeys}
                            onValuesChange={setDestColumnJsonProps}
                            dataSource={gotoDataSource}
                        />
                    </ProForm.Item>
                </Space>
            </StepsForm.StepForm>
        )
    }
    const renderStepEnd = () => {
        const columns = [
            {
                title: '序列',
                textAlign: 'center',
                dataIndex: 'index',
                tooltip: '只读，作为排序',
                editable: false,
                render: (_, record, index) => {
                    return index + 1;
                },
                width: '15%',
            },
            {
                title: '任务项',
                textAlign: 'center',
                dataIndex: 'task',
                editable: true,
                width: '65%',
                valueType: 'select',
                request: async () => jobSource,
                fieldProps: (_, {rowIndex}) => {
                    return {
                        onSelect: () => {
                            console.log(rowIndex)
                        },
                        filterOption: (input, option) =>
                            (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
                    };
                },
            },
            {
                title: '操作',
                textAlign: 'center',
                valueType: 'option',
            }
        ];

        return (
            <StepsForm.StepForm
                name="dispatch"
                title="调度信息"
            >
                <Row gutter={12}>
                    <Col span={10}>
                        <ProFormText
                            name="intgName"
                            label="数据同步名称"
                            initialValue=""
                            rules={[
                                {
                                    required: true, message: '请输入数据同步名称'
                                },
                            ]}
                        />
                    </Col>

                    {/* 当jobMode不是STREAMING时才显示以下调度相关字段 */}
                    {jobMode !== 'STREAMING' ?
                        <Col span={10}>
                            <ProFormText
                                name="schedulerExpr"
                                label="cron规则"
                                initialValue={cronExpression}
                                fieldProps={{
                                    suffix: <Space>
                                        <Dropdown menu={cronItemMenuProps} trigger={['click']}>
                                            <Button size={"small"}>
                                                <Space>
                                                    常用
                                                    <DownOutlined/>
                                                </Space>
                                            </Button>
                                        </Dropdown>
                                        <CronSelect onSave={save} cronExpression={cronExpression}/>
                                    </Space>,
                                    onChange: (event) => setCron(event.target.value)
                                }}
                                rules={[
                                    {required: true, message: '请输入cron规则'},
                                    {
                                        pattern: cronRegex,
                                        message: 'cron表达式不匹配'
                                    }
                                ]}
                            />
                        </Col>
                        :
                        <Col span={10}>
                            <ProFormText
                                name="schedulerExpr"
                                label="调度规则"
                                initialValue="STREAMING"
                                disabled={true}
                            />
                        </Col>
                    }
                </Row>

                {/* 当jobMode不是STREAMING时才显示以下调度相关字段 */}
                {jobMode !== 'STREAMING' && (
                    <>
                        <Row gutter={12}>
                            <Col span={10}>
                                <ProFormSelect
                                    initialValue="FIRST"
                                    options={[
                                        {
                                            value: 'FIRST',
                                            label: '第一个',
                                        },
                                        {
                                            value: 'LAST',
                                            label: '最后一个',
                                        },
                                        {
                                            value: 'RANDOM',
                                            label: '任意一个',
                                        },
                                    ]}
                                    name="routeStrategy"
                                    label="路由策略"
                                />
                            </Col>
                            <Col span={10}>
                                <ProFormSelect
                                    initialValue="SERIAL_EXECUTION"
                                    options={[
                                        {
                                            value: 'SERIAL_EXECUTION',
                                            label: '单机串行',
                                        },
                                        {
                                            value: 'DISCARD_LATER',
                                            label: '丢弃后续调度',
                                        },
                                        {
                                            value: 'COVER_EARLY',
                                            label: '覆盖之前调度',
                                        },
                                    ]}
                                    name="blockStrategy"
                                    label="阻塞处理"
                                />
                            </Col>
                        </Row>

                        <Row gutter={12}>
                            <Col span={10}>
                                <ProFormText
                                    name="executorFailRetryCount"
                                    label="失败重试次数"
                                    initialValue="3"
                                    rules={[
                                        {
                                            required: true, message: '请输入失败重试次数'
                                        },
                                    ]}
                                />
                            </Col>
                            <Col span={10}>
                                <ProFormText
                                    name="executorTimeout"
                                    label="超时时间(秒)"
                                    initialValue="30"
                                    rules={[
                                        {
                                            required: true, message: '请选择时间'
                                        },
                                    ]}
                                />
                            </Col>
                        </Row>
                    </>
                )}


                <Row gutter={12}>
                    <ProForm.Item
                        label="子任务"
                        name="childJobId"
                        initialValue={childJobId}
                        trigger="onValuesChange"
                    >
                        <EditableProTable
                            rowKey="ownId"
                            toolBarRender={false}
                            scroll={{y: 200}}
                            columns={columns}
                            recordCreatorProps={{
                                newRecordType: 'dataSource',
                                position: 'bottom',
                                record: () => ({
                                    ownId: (Math.random() * 1000000000).toFixed(0),
                                    title: ''
                                }),
                            }}
                            editable={{
                                type: 'multiple',
                                editableKeys: [...childJobIdKeys],
                                onChange: setchildJobIdKeys,
                                actionRender: (row, _, dom) => {
                                    console.log(row)
                                    return [dom.delete];
                                },
                                onValuesChange: (record, recordList) => {
                                    setChildJobId(recordList);
                                },
                            }}
                        />
                    </ProForm.Item>
                </Row>
            </StepsForm.StepForm>
        );
    };

    const handleSubmit = async (data) => {

        let destBaseJson = childRef4?.current?.getValues() || {
            ...returnJson(allData?.destBaseJson)
        };
        let destAdvJson = childRef5?.current?.getValues() || {
            ...returnJson(allData?.destAdvJson)
        };
        let destHighJson = childRef6?.current?.getValues() || {
            ...returnJson(allData?.destHighJson)
        };
        let orginBaseJson = childRef1?.current?.getValues() || {
            ...returnJson(allData?.orginBaseJson)
        };
        let orginAdvJson = childRef2?.current?.getValues() || {
            ...returnJson(allData?.orginAdvJson)
        };
        let orginHighJson = childRef3?.current?.getValues() || {
            ...returnJson(allData?.orginHighJson)
        };


        let {advJson, baseJson, highJson, columnJson} = sourceModel;

        let {advJson: advJson1, baseJson: baseJson1, highJson: highJson1, columnJson: columnJson1} = gotoModel;
        baseJson1 = returnJson(baseJson1);
        advJson1 = returnJson(advJson1);
        highJson1 = returnJson(highJson1);
        columnJson1 = returnJson(columnJson1)
        baseJson = returnJson(baseJson);
        advJson = returnJson(advJson);
        highJson = returnJson(highJson);
        columnJson = returnJson(columnJson)

        if (baseJson1.properties) {
            destBaseJson = utils.changeFormData(baseJson1.properties, destBaseJson);
        }

        if (advJson1.properties) {
            destAdvJson = utils.changeFormData(advJson1.properties, destAdvJson);
        }

        if (highJson1.properties) {
            destHighJson = utils.changeFormData(highJson1.properties, destHighJson);
        }

        if (baseJson.properties) {
            orginBaseJson = utils.changeFormData(baseJson.properties, orginBaseJson);
        }

        if (advJson.properties) {
            orginAdvJson = utils.changeFormData(advJson.properties, orginAdvJson);
        }

        if (highJson.properties) {
            orginHighJson = utils.changeFormData(highJson.properties, orginHighJson);
        }


        let {orginColumnJson, destColumnJson, childJobId, ...destData} = data;
        let fun, ids = {};

        if (markAdd == 0) {
            fun = datasyncAdd;
        } else {
            fun = datasyncUpdate;
            ids = {id: allData.id}
        }
        if (orginColumnJson && orginColumnJson.length > 0) {
            orginColumnJson = orginColumnJson.map(item => {
                // const {ownId, ...rest} = item;
                return utils.changeColumnData(columnJson, item)
            })
        }
        if (destColumnJson && destColumnJson.length > 0) {
            destColumnJson = destColumnJson.map(item => {
                return utils.changeColumnData(columnJson1, item)
            })
        }

        // 创建字段映射 JSON
        let fieldMappingJson = [];
        if (orginColumnJson && destColumnJson && orginColumnJson.length > 0 && destColumnJson.length > 0) {
            // 取两个数组的较小长度，确保不会超出范围
            const minLength = Math.min(orginColumnJson.length, destColumnJson.length);

            for (let i = 0; i < minLength; i++) {
                fieldMappingJson.push({
                    sourceField: orginColumnJson[i].title,
                    sinkField: destColumnJson[i].title
                });
            }
        }

        // 流水线任务id
        let childJobIdStr = ""
        const childJobIdArr = []
        if (childJobId && childJobId.length > 0) {
            childJobId.map((item, index) => {
                childJobIdArr.push(item.task)
            })
            childJobIdStr = childJobIdArr.join(",")
        }
        const res = await fun({
            ...ids,
            destDatasourceName,
            orginDatasourceName,
            destBaseJson: JSON.stringify(destBaseJson),
            destAdvJson: JSON.stringify(destAdvJson),
            destHighJson: JSON.stringify(destHighJson),
            orginBaseJson: JSON.stringify(orginBaseJson),
            orginAdvJson: JSON.stringify(orginAdvJson),
            orginHighJson: JSON.stringify(orginHighJson),
            orginColumnJson: JSON.stringify(orginColumnJson),
            destColumnJson: JSON.stringify(destColumnJson),
            childJobid: childJobIdStr,
            fieldMappingJson: JSON.stringify(fieldMappingJson),
            ...destData,
        });

        if (res) {
            handleSearch();
            onClose();
            setIsModalVisible(false);
            messageModal('success', markAdd == 0 ? res.message || '新增成功' : res.message || '修改成功');
        }
    };

    const setOrginColumnJsonProps = (data) => {
        formRef?.current?.setFieldsValue({
            orginColumnJson: data,
        });
        setDataSource([...data]);
    }

    const setDestColumnJsonProps = (data) => {
        formRef?.current?.setFieldsValue({
            destColumnJson: data,
        });
        setGotoDataSource([...data]);
    }

    return (
        <>
            <Modal
                title={`${markAdd == 0 ? '新增' : '修改'}数据集成`}
                width={1070}
                footer={null}
                destroyOnClose={true}
                onCancel={() => onClose()}
                maskClosable={false}
                visible={isModalVisible}
                className={styles.modalBox}
            >
                <StepsForm
                    formRef={formRef}
                    onFinish={(data) => handleSubmit(data)}
                    onCurrentChange={async (index) => {
                        console.log(index)
                        await setCurrent(index);

                        // 调整索引逻辑
                        if (index == 1) {
                            // 原来的第一步 (数据来源)
                            await getSelectList(defaultSourceRadio, 0);
                        }

                        if (index == 2) {
                            // 原来的第二步 (数据去向)
                            await getSelectList(defaultGotoRadio, 1);
                        }

                        if (index == 3 && markAdd == 1) {
                            // 原来的第三步 (数据映射)
                            console.log(dataSource, gotoDataSource)
                            formRef?.current?.setFieldsValue({
                                orginColumnJson: dataSource,
                                destColumnJson: gotoDataSource,
                            });
                        }

                        if (index == 3 && markAdd == 0) {
                            // 同步字段逻辑保持不变，只是索引调整
                            let destBaseJson = childRef4?.current?.getValues()
                            let orginBaseJson = childRef1?.current?.getValues();

                            // 同步字段
                            const oriRes = await getTableColumns({
                                tableName: orginBaseJson.table || "",
                                dataSourceId: originDatasourceId
                            });
                            const destRes = await getTableColumns({
                                tableName: destBaseJson.table || "",
                                dataSourceId: destDatasourceId
                            });

                            const orginColumnJson = (oriRes.datasourceColumns || []).map(item => ({
                                id: item.id,
                                ownId: item.id,
                                title: item.name,
                            }));
                            const destColumnJson = (destRes.datasourceColumns || []).map(item => ({
                                id: item.id,
                                ownId: item.id,
                                title: item.name
                            }));
                            formRef?.current?.setFieldsValue({
                                orginColumnJson: orginColumnJson,
                                destColumnJson: destColumnJson,
                            });
                            const orginIds = orginColumnJson.map(item => item.id);
                            const destIds = destColumnJson.map(item => item.id);
                            setEditableGotoRowKeys(destIds);
                            setEditableSourceRowKeys(orginIds);
                            setDataSource([...orginColumnJson]);
                            setGotoDataSource([...destColumnJson])
                        }

                        if (index == 4 && markAdd == 0) {
                            // 原来的第四步 (调度信息)
                            formRef?.current?.setFieldsValue({
                                intgName: `${orginDatasourceName}->${destDatasourceName}`
                            })
                        }

                        if (index != 3 && markAdd == 1) {
                            formRef?.current?.setFieldsValue({
                                ...allData,
                                childJobId: childJobId
                            })
                        }
                    }}
                    stepsProps={{
                        validateMessages: {
                            required: '此项为必填项',
                        },
                    }}
                    submitter={{
                        render: (props) => {
                            // 处理模式选择步骤 (新的第一步)
                            if (props.step === 0) {
                                return [
                                    <Button type="primary" key="next" onClick={() => props.onSubmit?.()}>
                                        下一步
                                    </Button>
                                ];
                            }

                            // 数据来源步骤 (原第一步，现第二步)
                            if (props.step === 1) {
                                return [
                                    <Button key="pre" onClick={() => props.onPre?.()}>
                                        上一步
                                    </Button>,
                                    <Button type="primary" onClick={async () => {
                                        const res1 = await childRef1?.current?.submit();
                                        if (res1?.errors && res1.errors?.length > 0) {
                                            return
                                        }
                                        const res2 = childRef2?.current?.submit();
                                        if (res2?.errors && res2.errors?.length > 0) {
                                            return
                                        }
                                        const res3 = childRef3?.current?.submit();
                                        if (res3?.errors && res3.errors?.length > 0) {
                                            return
                                        }
                                        props.onSubmit?.();
                                    }}>
                                        下一步
                                    </Button>
                                ];
                            }

                            // 数据去向步骤 (原第二步，现第三步)
                            if (props.step === 2) {
                                return [
                                    <Button key="pre" onClick={() => props.onPre?.()}>
                                        上一步
                                    </Button>,
                                    <Button type="primary" key="goToTree" onClick={async () => {
                                        const res1 = await childRef4?.current?.submit();
                                        if (res1?.errors && res1.errors?.length > 0) {
                                            return
                                        }
                                        const res2 = childRef5?.current?.submit();
                                        if (res2?.errors && res2.errors?.length > 0) {
                                            return
                                        }
                                        const res3 = childRef6?.current?.submit();
                                        if (res3?.errors && res3.errors?.length > 0) {
                                            return
                                        }
                                        props.onSubmit?.();
                                    }}>
                                        下一步
                                    </Button>
                                ];
                            }

                            // 数据映射步骤 (原第三步，现第四步)
                            if (props.step === 3) {
                                return [
                                    <Button key="pre" onClick={() => props.onPre?.()}>
                                        上一步
                                    </Button>,
                                    <Button type="primary" key="goToTree" onClick={() => props.onSubmit?.()}>
                                        下一步
                                    </Button>
                                ];
                            }

                            // 调度信息步骤 (原第四步，现第五步)
                            return [
                                <Button key="pre" onClick={() => props.onPre?.()}>
                                    上一步
                                </Button>,
                                <Button type="primary" key="goToTree" onClick={() => props.onSubmit?.()}>
                                    提交
                                </Button>
                            ];
                        },
                    }}
                >
                    {renderModeSelection()}
                    {renderStepFirst}
                    {renderStepSecond}
                    {renderStepThird()}
                    {renderStepEnd()}
                </StepsForm>
            </Modal>
        </>
    );

};
