<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
    <option name="ignoredFiles">
      <set>
        <option value="$PROJECT_DIR$/xotp-release/xotp-release-seatunnel-integrator/pom.xml" />
        <option value="$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/connector-fako/pom.xml" />
      </set>
    </option>
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State>
            <id>Ali-Check</id>
          </State>
          <State>
            <id>Code maturityJava</id>
          </State>
          <State>
            <id>Java</id>
          </State>
          <State>
            <id>Kotlin</id>
          </State>
          <State>
            <id>Naming conventionsJava</id>
          </State>
          <State>
            <id>Other problemsKotlin</id>
          </State>
          <State>
            <id>PerformanceJava</id>
          </State>
          <State>
            <id>Probable bugsJava</id>
          </State>
          <State>
            <id>Probable bugsKotlin</id>
          </State>
          <State>
            <id>SQL</id>
          </State>
          <State>
            <id>Verbose or redundant code constructsJava</id>
          </State>
        </expanded-state>
        <selected-state>
          <State>
            <id>User defined</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" project-jdk-name="1.8" project-jdk-type="JavaSDK" />
  <component name="SvnBranchConfigurationManager">
    <option name="myConfigurationMap">
      <map>
        <entry key="$PROJECT_DIR$">
          <value>
            <SvnBranchConfiguration>
              <option name="branchUrls">
                <list>
                  <option value="https://10.83.100.235:18080/svn/11-XBigData/branches" />
                  <option value="https://10.83.100.235:18080/svn/11-XBigData/tags" />
                </list>
              </option>
              <option name="trunkUrl" value="https://10.83.100.235:18080/svn/11-XBigData/trunk" />
            </SvnBranchConfiguration>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>