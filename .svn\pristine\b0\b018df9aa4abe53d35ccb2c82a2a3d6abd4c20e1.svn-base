management.endpoints.web.exposure.include=*
spring.main.allow-bean-definition-overriding=true


spring.datasource.url=****************************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.hikari.minimum-idle=100
spring.datasource.hikari.maximum-pool-size=500
## idle timeout, default value 600000ms(10 minutes)
spring.datasource.hikari.idle-timeout=600000
## max live time, default value 1800000ms(30 minutes)
spring.datasource.hikari.max-lifetime=1800000
## connection timeout, default value 30000ms(30 seconds)
spring.datasource.hikari.connection-timeout=30000
# jdbc template
spring.jdbc.template.fetch-size=1000
spring.jdbc.template.max-rows=1000
spring.jdbc.template.query-timeout=10s

spring.servlet.multipart.max-file-size=256MB
spring.servlet.multipart.max-request-size=256MB

xcnf.data.fsclient.ftp.host=*************
xcnf.data.fsclient.ftp.username=tech
xcnf.data.fsclient.ftp.password=tech@2023
xcnf.data.fsclient.ftp.pool.jmxEnabled=false
xcnf.data.fsclient.type=ftp
xcnf.data.fsclient.ftp.port=21


xbdp.hookbox.job.engine=seatunnel
xbdp.hookbox.job.seatunnel.api-endpoint=


### \u6587\u4EF6\u670D\u52A1\u5668\u7684\u6839\u8DEF\u5F84
xbdp.fileServer.root=projectUploadFile\\xbdp
### \u6587\u4EF6\u670D\u52A1\u5668\u7684\u5206\u9694\u7B26
xbdp.fileServer.separator=\\
### TODO: \u672C\u5730\u6587\u4EF6\u7F13\u5B58\u5730\u5740
xbdp.fileLocal.root=/opt/tmp
### datax bin \u76EE\u5F55 \u7528\u4E8E\u542F\u52A8 datax
xbdp.hooks.datax.base-path=../bin/datax.py
### datax python\u89E3\u91CA\u5668\u8DEF\u5F84\uFF0C\u9ED8\u8BA4\u81EA\u52A8\u67E5\u627E\uFF0C\u6700\u597D\u8FD8\u662F\u624B\u52A8\u5199\u4E0A
xbdp.hooks.datax.python.path=D:/software/miniconda3/python.exe
### TODO: datax \u63D2\u4EF6\u6267\u884C\u5730\u5740
xbdp.hooks.datax.plugin.run.path=/opt/tmp
### TODO: developer \u8D44\u6E90\u8FD0\u884C\u8DEF\u5F84
xbdp.hooks.developer.resource.run.path=/opt/tmp
### flink \u542F\u52A8\u57FA\u7840\u8DEF\u5F84
xbdp.hooks.flink.base-path=C:/Xmcares/env/apache-flink-1.10.0/bin/flink.bat
### flink \u4E3B\u670D\u52A1\u5730\u5740
xbdp.hooks.flink.master.url=https://xcf-center:8081

xbdp.hooks.flink.ssl.enable=true
xbdp.hooks.flink.ssl.keystore.path=../conf/rest.signed.keystore
xbdp.hooks.flink.ssl.keystore.password=flinkflink
xbdp.hooks.flink.ssl.keystore.type=PKCS12
xbdp.hooks.flink.ssl.encryption=SunX509

# no web
#spring.main.web-environment=false
### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
# TODO: \u63D0\u4EA4\u9700\u8981\u56DE\u6EDA \u8FD9\u662Fscheduler\u7684\u5730\u5740\u7AEF\u53E3
#xxl.job.admin.addresses=http://*************:31411
xxl.job.admin.addresses=http://127.0.0.1:8080
### xxl-job, access token
xxl.job.accessToken=default_token
### xxl-job executor appname
xxl.job.executor.appname=${spring.application.name}
### xxl-job executor registry-address: default use address to registry , otherwise use ip:port if address is null
xxl.job.executor.address=http://127.0.0.1:${xxl.job.executor.port}
### xxl-job executor server-info
xxl.job.executor.ip=127.0.0.1
xxl.job.executor.port=29999
### xxl-job executor log-path
xxl.job.executor.logpath=/data/applogs-xotp-release-hookbox-integrator/xxl-job/jobhandler
### xxl-job executor log-retention-days
xxl.job.executor.logretentiondays=30

#xxl.job.executor.registry.nacos=true
xcnf.swagger.enabled=true
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

# \u6B64\u5904\u914D\u7F6E\u7684\u8DEF\u5F84\u4E0D\u5141\u8BB8\u6709/\u5F00\u5934,\u5F71\u54CD\u8DEF\u5F84\u89E3\u6790
datax.job.json.output.path=integrator
