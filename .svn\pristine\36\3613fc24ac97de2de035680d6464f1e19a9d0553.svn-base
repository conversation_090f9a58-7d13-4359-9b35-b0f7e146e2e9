<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmcares.platform.hookbox.integrator.mapper.DatasyncJobInstanceMapper">
  <resultMap id="BaseResultMap" type="com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance">
    <!--@mbg.generated-->
    <!--@Table xotp.bdp_intg_datasync_job_instance-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="job_id" jdbcType="VARCHAR" property="jobId" />
    <result column="job_params" jdbcType="LONGVARCHAR" property="jobParams" />
    <result column="schedule_id" jdbcType="VARCHAR" property="scheduleId" />
    <result column="schedule_time" jdbcType="TIMESTAMP" property="scheduleTime" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="status_message" jdbcType="LONGVARCHAR" property="statusMessage" />
    <result column="received_count" jdbcType="INTEGER" property="receivedCount" />
    <result column="recevied_qps" jdbcType="DOUBLE" property="receviedQps" />
    <result column="writed_count" jdbcType="INTEGER" property="writedCount" />
    <result column="writed_qps" jdbcType="DOUBLE" property="writedQps" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, job_id, job_params, schedule_id, schedule_time, finish_time, `status`, status_message,
    received_count, recevied_qps, writed_count, writed_qps, create_time,
    update_time
  </sql>

  <resultMap id="JobInstanceWithJobResultMap" type="com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance">
    <id property="id" column="instance_id"/>
    <result property="jobId" column="job_id"/>
    <result property="status" column="instance_status"/>
    <result property="jobParams" column="job_params"/>
    <result property="scheduleId" column="schedule_id"/>
    <result property="scheduleTime" column="schedule_time"/>
    <result property="finishTime" column="finish_time"/>
    <result property="statusMessage" column="status_message"/>
    <result property="createTime" column="instance_create_time"/>
    <result property="updateTime" column="instance_update_time"/>

    <association property="datasyncJob" javaType="com.xmcares.platform.hookbox.integrator.model.DatasyncJob">
      <id property="id" column="job_id"/>
      <result property="jobName" column="job_name"/>
      <result property="status" column="job_status"/>
      <result property="jobOptions" column="job_options"/>
    </association>
  </resultMap>

  <select id="selectAllJobInstancesWithJob" resultMap="JobInstanceWithJobResultMap">
    SELECT
      dji.id AS instance_id,
      dji.job_id,
      dji.job_params,
      dji.schedule_id,
      dji.schedule_time,
      dji.finish_time,
      dji.status AS instance_status,
      dji.status_message,
      dji.create_time AS instance_create_time,
      dji.update_time AS instance_update_time,
      dj.job_name,
      dj.job_options,
      dj.status AS job_status,
      dj.create_user AS job_create_user,
      dj.update_user AS job_update_user
    FROM
      xotp.bdp_intg_datasync_job_instance dji
        INNER JOIN
      xotp.bdp_intg_datasync_job dj ON dji.job_id = dj.id
    <where>
      dj.deleted = '0'

      <if test="ew != null and ew.nonEmptyOfWhere">
        AND ${ew.sqlSegment}
      </if>
    </where>
  </select>

  <select id="selectJobInstanceWithJobByWrapper" resultMap="JobInstanceWithJobResultMap">
    SELECT
      dji.id AS instance_id,
      dji.job_id,
      dji.job_params,
      dji.schedule_id,
      dji.schedule_time,
      dji.finish_time,
      dji.status AS instance_status,
      dji.status_message,
      dji.create_time AS instance_create_time,
      dji.update_time AS instance_update_time,
      dj.job_name,
      dj.job_options,
      dj.status AS job_status,
      dj.create_user AS job_create_user,
      dj.update_user AS job_update_user
    FROM
      xotp.bdp_intg_datasync_job_instance dji
        INNER JOIN
      xotp.bdp_intg_datasync_job dj ON dji.job_id = dj.id
    ${ew.customSqlSegment}
  </select>


</mapper>
