package com.xmcares.platform.admin.common.database;

import com.xmcares.platform.admin.common.errors.MetadataException;
import com.xmcares.platform.admin.common.jdbc.JdbcUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 基于数据源({@link javax.sql.DataSource})的数据库元数据查询
 * <AUTHOR>
 * @since 2.1.0
 */
public abstract class DataSourceBasedMetaQuery implements DatabaseMetaQuery{
    /** Logger available to subclasses. */
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected final DataSource dataSource;
    protected final String schema;

    public DataSourceBasedMetaQuery(DataSource dataSource) {
        this(dataSource, null);
    }

    public DataSourceBasedMetaQuery(DataSource dataSource, String schema) {
        if (dataSource == null) {
            throw new IllegalArgumentException("datasource不可为空");
        }
        if (schema == null || schema.isEmpty()) {
            schema = getCurrentSchema(dataSource);
        }
        this.dataSource = dataSource;
        this.schema = schema;
    }


    @Override
    public Object getColumnMaxValue(String tableName, String columnName) {
        String sql = String.format("SELECT Max(%s) as max_id FROM %s", columnName, tableName);
        Object result = "0";
        try {
            List<Map<String, Object>> maps = JdbcUtils.executeQuery(dataSource, sql);
            if (!maps.isEmpty()) {
                result = maps.get(0).get("max_id");
            }
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取表列[%s.%s]最大值失败", tableName, columnName), e);
        }
        return result;
    }

    public DataSource getDataSource() {
        return this.dataSource;
    }

    public String getCurrentSchema() {
        return this.schema;
    }

    /**
     * 获取当前schema
     * @param dataSource 数据源
     * @return schema name
     */
    private String getCurrentSchema(DataSource dataSource) {
        Connection connection;
        try {
            connection = dataSource.getConnection();
        } catch (SQLException e) {
            throw new MetadataException(String.format("数据源[%s]连接不可用", dataSource), e);
        }
        //默认为空值
        String res = "";
        try {
            res = connection.getCatalog();
        } catch (SQLException e) {
            logger.info("当前数据源不支持[connection.getCatalog()]: {}", e.getMessage());
            try {
                res = connection.getSchema();
            } catch (SQLException e1) {
                logger.info("当前数据源不支持[connection.getSchema()]: {}", e.getMessage());
            }
        }
        return res;
    }


}
