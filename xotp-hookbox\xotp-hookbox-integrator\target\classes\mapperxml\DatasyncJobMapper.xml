<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmcares.platform.hookbox.integrator.mapper.DatasyncJobMapper">
  <resultMap id="BaseResultMap" type="com.xmcares.platform.hookbox.integrator.model.DatasyncJob">
    <!--@mbg.generated-->
    <!--@Table xotp.bdp_intg_datasync_job-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="datasync_id" jdbcType="VARCHAR" property="datasyncId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="job_name" jdbcType="VARCHAR" property="jobName" />
    <result column="job_options" jdbcType="LONGVARCHAR" property="jobOptions" />
    <result column="orgin_type" jdbcType="CHAR" property="orginType" />
    <result column="orgin_datasource_name" jdbcType="VARCHAR" property="orginDatasourceName" />
    <result column="orgin_plugin_path" jdbcType="VARCHAR" property="orginPluginPath" />
    <result column="dest_type" jdbcType="CHAR" property="destType" />
    <result column="dest_datasource_name" jdbcType="VARCHAR" property="destDatasourceName" />
    <result column="dest_plugin_path" jdbcType="VARCHAR" property="destPluginPath" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="deleted" jdbcType="CHAR" property="deleted" />
    <result column="schedule_id" jdbcType="VARCHAR" property="scheduleId" />
    <result column="schedule_group" jdbcType="INTEGER" property="scheduleGroup" />
    <result column="schedule_type" jdbcType="VARCHAR" property="scheduleType" />
    <result column="schedule_conf" jdbcType="VARCHAR" property="scheduleConf" />
    <result column="misfire_strategy" jdbcType="VARCHAR" property="misfireStrategy" />
    <result column="executor_route_strategy" jdbcType="VARCHAR" property="executorRouteStrategy" />
    <result column="executor_handler" jdbcType="VARCHAR" property="executorHandler" />
    <result column="executor_params" jdbcType="VARCHAR" property="executorParams" />
    <result column="executor_block_strategy" jdbcType="VARCHAR" property="executorBlockStrategy" />
    <result column="executor_timeout" jdbcType="INTEGER" property="executorTimeout" />
    <result column="executor_fail_retry_count" jdbcType="INTEGER" property="executorFailRetryCount" />
    <result column="child_jobids" jdbcType="VARCHAR" property="childJobids" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, datasync_id, create_time, create_user, update_time, update_user, job_name, job_options,
    orgin_type, orgin_datasource_name, orgin_plugin_path, dest_type, dest_datasource_name,
    dest_plugin_path, `status`, deleted, schedule_id, schedule_group, schedule_type,
    schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_params,
    executor_block_strategy, executor_timeout, executor_fail_retry_count, child_jobids
  </sql>
</mapper>
