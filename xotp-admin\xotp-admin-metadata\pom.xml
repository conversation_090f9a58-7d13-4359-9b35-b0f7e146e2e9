<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xmcares.platform</groupId>
        <artifactId>xotp-admin</artifactId>
        <version>1.0.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>xotp-admin-metadata</artifactId>

    <properties>

    </properties>


    <dependencies>
        <!-- ::开始:: XOTP 其他模块依赖 :::: -->
        <dependency>
            <groupId>com.xmcares.platform</groupId>
            <artifactId>xotp-admin-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- ::结束:: XOTP 其他模块依赖 :::: -->

        <!-- ::开始:: XCNF & Spring Boot & Spring Cloud :::: -->
        <dependency>
            <groupId>com.xmcares.framework</groupId>
            <artifactId>spring-boot-starter-xcnf-web</artifactId>
        </dependency>
        <!-- XCNF 身份验证与访问控制 -->
        <dependency>
            <groupId>com.xmcares.framework</groupId>
            <artifactId>xcnf-security-rbac</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xmcares.framework</groupId>
            <artifactId>xcnf-rbac-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xmcares.framework</groupId>
            <artifactId>xcnf-data-fsclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <!-- ::结束:: XCNF & Spring Boot & Spring Cloud :::: -->


        <!-- ::开始:: 管理数据库驱动库 & 平台支持的数据源驱动 :::: -->
        <!-- 管理数据库驱动：mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!--支持的数据源驱动 -->
        <!--oracle-->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
        </dependency>
        <!--dm-->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>dm-jdbc</artifactId>
        </dependency>
        <!--open gauss-->
        <dependency>
            <groupId>org.opengauss</groupId>
            <artifactId>opengauss-jdbc</artifactId>
        </dependency>
        <!-- SQL Server 驱动 -->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>
        <!-- mongodb -->
        <!--<dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
        </dependency>-->
        <!--es-->
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
        </dependency>
        <!--  大数据 组件 Begin !-->
        <!-- Hadoop Common -->
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
        </dependency>
        <!-- Hadoop -->
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs</artifactId>
        </dependency>
        <!-- HBase !-->
        <dependency>
            <groupId>org.apache.hbase</groupId>
            <artifactId>hbase-client</artifactId>
        </dependency>
        <!-- HBase !-->
        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-jdbc</artifactId>
        </dependency>
        <!--  大数据 组件 End !-->
        <!-- ::结束:: 管理数据库驱动库 & 平台支持的数据源驱动 :::: -->

        <!--swagger依赖-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <!--swagger ui-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>


</project>
