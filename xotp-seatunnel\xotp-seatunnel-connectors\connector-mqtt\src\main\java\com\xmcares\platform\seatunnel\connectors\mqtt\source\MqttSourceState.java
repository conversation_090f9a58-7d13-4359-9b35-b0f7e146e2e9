/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.source;

import java.io.Serializable;
import java.util.Set;

public class MqttSourceState implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 已分配的分片集合 */
    private Set<MqttSourceSplit> assignedSplits;

    @Override
    public String toString() {
        return "MqttSourceState{" + "assignedSplits=" + assignedSplits + '}';
    }

    public MqttSourceState() {
    }

    public MqttSourceState(Set<MqttSourceSplit> assignedSplits) {
        this.assignedSplits = assignedSplits;
    }

    public Set<MqttSourceSplit> getAssignedSplits() {
        return assignedSplits;
    }

    public void setAssignedSplits(Set<MqttSourceSplit> assignedSplits) {
        this.assignedSplits = assignedSplits;
    }
}
