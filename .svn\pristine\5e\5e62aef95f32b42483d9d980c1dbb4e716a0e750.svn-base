package com.xmcares.platform.hookbox.integrator.model;

/**
 * JobLogVO
 *
 * <AUTHOR>
 * @Descriptions JobLogVO
 * @Date 2025/7/23 10:50
 */
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * JobLogVO
 * <AUTHOR>
 * @Descriptions Aggregated job log view object
 * @Date 2025/7/23
 */
@ApiModel(description = "Aggregated Log View Object")
public class JobLogVO {

    @ApiModelProperty(value = "XXL-Job scheduling log")
    private String xxlJobLog;

    @ApiModelProperty(value = "SeaTunnel engine execution log")
    private String seaTunnelLog;

    @ApiModelProperty(value = "XXL-Job 执行器详细过程日志")
    private String xxlJobDetailLog;

    public String getXxlJobLog() {
        return xxlJobLog;
    }

    public void setXxlJobLog(String xxlJobLog) {
        this.xxlJobLog = xxlJobLog;
    }

    public String getSeaTunnelLog() {
        return seaTunnelLog;
    }

    public void setSeaTunnelLog(String seaTunnelLog) {
        this.seaTunnelLog = seaTunnelLog;
    }

    public String getXxlJobDetailLog() {
        return xxlJobDetailLog;
    }

    public void setXxlJobDetailLog(String xxlJobDetailLog) {
        this.xxlJobDetailLog = xxlJobDetailLog;
    }
}
