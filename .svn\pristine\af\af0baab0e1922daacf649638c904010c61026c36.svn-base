/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/28
 */
package com.xmcares.platform.hookbox.common.job.context;

import com.xmcares.platform.hookbox.common.job.JobContext;
import com.xmcares.platform.hookbox.common.job.JobParams;

import java.util.Date;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public class JobContextImpl implements JobContext {
    private static final long serialVersionUID = 489538519132645612L;

    private final String jobId;
    private final String jobInstanceId;
    private String jobName;
    private String jobOptions;
    private JobParams jobParams =  new JsonJobParams("{}");

    public JobContextImpl(String jobId, String jobInstanceId) {
        this.jobId = jobId;
        this.jobInstanceId = jobInstanceId;
    }

    public JobContextImpl(String jobId, String jobInstanceId, String jobName) {
        this.jobId = jobId;
        this.jobInstanceId = jobInstanceId;
        this.jobName = jobName;
    }

    @Override
    public String getJobId() {
        return jobId;
    }

    @Override
    public String getJobName() {
        return this.jobName;
    }

    @Override
    public String getJobInstanceId() {
        return jobInstanceId;
    }

    @Override
    public String getJobOptions() {
        return jobOptions;
    }

    public void setJobOptions(String jobOptions) {
        this.jobOptions = jobOptions;
    }

    public void setJobParams(JobParams jobParams) {
        this.jobParams = jobParams;
    }

    @Override
    public JobParams getJobParams() {
        return this.jobParams;
    }

    @Override
    public Date getTriggerTime() {
        return this.getJobParams().getTriggerTime();
    }

    @Override
    public Date getScheduleTime() {
        return this.getJobParams().getScheduleTime();
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", JobContextImpl.class.getSimpleName() + "[", "]")
                .add("jobId='" + jobId + "'")
                .add("jobName='" + jobName + "'")
                .add("scheduleTime=" + getScheduleTime())
                .add("triggerTime=" + getTriggerTime())
                .add("jobParams=" + jobParams)
                .toString();
    }
}
