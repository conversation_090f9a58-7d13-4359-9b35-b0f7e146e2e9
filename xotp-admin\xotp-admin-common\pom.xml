<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xmcares.platform</groupId>
        <artifactId>xotp-admin</artifactId>
        <version>1.0.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>xotp-admin-common</artifactId>

    <properties>

    </properties>


    <dependencies>
        <!-- ::开始:: XCNF  ::::-->
        <!-- XCNF commons -->
        <dependency>
            <groupId>com.xmcares.framework</groupId>
            <artifactId>xcnf-commons-lang</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xmcares.framework</groupId>
            <artifactId>xcnf-data-fsclient</artifactId>
        </dependency>
        <!-- ::结束:: XCNF  ::::-->

        <!-- ::开始:: 辅助类公共库  ::::-->
        <dependency>
            <groupId>com.caucho</groupId>
            <artifactId>hessian</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-dbutils</groupId>
            <artifactId>commons-dbutils</artifactId>
        </dependency>
        <!-- ::结束:: 辅助类公共库  ::::-->

        <!-- ::开始:: com.xmcares.platform.admin.common.datasource 数据源支持需要的jar ::::-->
        <!-- JDBC数据库数据源 -->
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <!-- rocketmq -->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-acl</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-tools</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- 消息中心2.0-->
        <dependency>
            <groupId>com.xmcares.framework.imc</groupId>
            <artifactId>imc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xmcares.framework.imc</groupId>
            <artifactId>imc-client-adapter</artifactId>
        </dependency>
        <!-- pulsar 队列-->
        <dependency>
            <groupId>org.apache.pulsar</groupId>
            <artifactId>pulsar-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pulsar</groupId>
            <artifactId>pulsar-client-admin-original</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- activemq artemis 队列-->
        <dependency>
            <groupId>org.apache.activemq</groupId>
            <artifactId>artemis-core-client</artifactId>
        </dependency>
        <!-- rabbitmq 队列 -->
        <dependency>
            <groupId>com.rabbitmq</groupId>
            <artifactId>amqp-client</artifactId>
        </dependency>
        <!-- mqtt 数据源支持 -->
        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
        </dependency>

        <!-- ::结束:: com.xmcares.platform.admin.common.datasource 数据源支持需要的jar ::::-->
    </dependencies>

</project>
