/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/10
 */
package com.xmcares.platform.hookbox.common.mq;

import com.xmcares.platform.admin.common.datasource.mq.artemis.ArtemisDataSource;
import com.xmcares.platform.admin.common.datasource.mq.artemis.ArtemisProperties;
import com.xmcares.platform.admin.common.datasource.mq.pulsar.PulsarDataSource;
import com.xmcares.platform.admin.common.datasource.mq.pulsar.PulsarProperties;
import com.xmcares.platform.hookbox.common.mq.artemis.ArtemisEmbeddedServer;
import com.xmcares.platform.hookbox.common.mq.artemis.ArtemisEmbeddedProperties;
import com.xmcares.platform.hookbox.common.mq.artemis.ArtemisMqTemplate;
import com.xmcares.platform.hookbox.common.mq.pulsar.PulsarMqTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
@Configuration
public class MqConfiguration {

    /**
     * 启用内嵌的消息队列，默认关闭
     */
    @Configuration
    @ConditionalOnProperty(prefix = "xbdp.hookbox.mq.server", name = "enabled", havingValue = "true", matchIfMissing = false)
    @EnableConfigurationProperties(ArtemisEmbeddedProperties.class)
    static class EmbeddedServerConfiguration {
        @Bean(initMethod = "start", destroyMethod = "stop")
        public ArtemisEmbeddedServer artemisEmbeddedBroker(ArtemisEmbeddedProperties properties) {
            return new ArtemisEmbeddedServer(properties);
        }

    }


    @Configuration
    @ConditionalOnProperty(prefix = "xbdp.hookbox.mq.client", name = "type", havingValue = "artemis", matchIfMissing = true)
    static class ArtemisClientConfiguration {

        @Bean
        @ConfigurationProperties(prefix = "xbdp.hookbox.mq.client.artemis")
        public ArtemisProperties artemisProperties() {
            return new ArtemisProperties();
        }
        @Bean(destroyMethod = "close")
        public ArtemisDataSource mqDataSource(ArtemisProperties properties) {
            return new ArtemisDataSource("internal-artemis", properties);
        }

        @Bean
        public MqTemplate mqTemplate(ArtemisDataSource dataSource){
            return new ArtemisMqTemplate(dataSource);
        }

    }

    @Configuration
    @ConditionalOnProperty(prefix = "xbdp.hookbox.mq.client", name = "type", havingValue = "pulsar", matchIfMissing = false)
    static class PulsarClientConfiguration {

        @Bean
        @ConfigurationProperties(prefix = "xbdp.hookbox.mq.client.pulsar")
        public PulsarProperties pulsarProperties() {
            return new PulsarProperties();
        }

        @Bean(destroyMethod = "close")
        public PulsarDataSource mqDataSource(PulsarProperties properties) {
            return new PulsarDataSource("internal-pulsar", properties);
        }

        @Bean
        public MqTemplate mqTemplate(PulsarDataSource dataSource) {
            return new PulsarMqTemplate(dataSource);
        }
    }

}
