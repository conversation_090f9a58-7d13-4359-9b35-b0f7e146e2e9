/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.source;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.xmcares.platform.seatunnel.connectors.mqtt.exception.MqttConnectorErrorCode;
import com.xmcares.platform.seatunnel.connectors.mqtt.exception.MqttConnectorException;
import org.apache.seatunnel.api.serialization.DeserializationSchema;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * MQTT XML反序列化Schema，使用Jackson XML解析器解析XML消息
 */
public class MqttXmlDeserializationSchema implements DeserializationSchema<SeaTunnelRow> {

    private static final Logger log = LoggerFactory.getLogger(MqttXmlDeserializationSchema.class);


    private final SeaTunnelRowType rowType;
    private final XmlMapper xmlMapper;
    private final String[] fieldNames;
    private final SeaTunnelDataType<?>[] fieldTypes;

    public MqttXmlDeserializationSchema(CatalogTable catalogTable) {
        this.rowType = catalogTable.getSeaTunnelRowType();
        this.xmlMapper = new XmlMapper();
        this.fieldNames = rowType.getFieldNames();
        this.fieldTypes = rowType.getFieldTypes();

        log.info("Initialized XML deserialization schema with fields: {}", String.join(", ", fieldNames));
    }

    @Override
    public SeaTunnelRow deserialize(byte[] message) throws IOException {
        try {
            String xmlContent = new String(message);
            log.debug("Deserializing XML message: {}", xmlContent);

            // 将XML解析为JsonNode，然后按照字段映射转换
            JsonNode xmlNode = xmlMapper.readTree(xmlContent);
            Object[] values = new Object[fieldNames.length];

            for (int i = 0; i < fieldNames.length; i++) {
                String fieldName = fieldNames[i];
                SeaTunnelDataType<?> fieldType = fieldTypes[i];
                JsonNode fieldNode = findFieldInXml(xmlNode, fieldName);

                if (fieldNode != null && !fieldNode.isNull()) {
                    values[i] = convertXmlValue(fieldNode, fieldType);
                } else {
                    values[i] = null;
                    log.debug("Field '{}' not found in XML message or is null", fieldName);
                }
            }

            SeaTunnelRow row = new SeaTunnelRow(values);
            log.debug("Successfully deserialized XML message to SeaTunnelRow: {}", row);
            return row;

        } catch (Exception e) {
            log.error("Failed to deserialize XML message: {}", new String(message), e);
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.SERIALIZATION_ERROR,
                    "Failed to deserialize XML message: " + e.getMessage(),
                    e);
        }
    }

    @Override
    public SeaTunnelDataType<SeaTunnelRow> getProducedType() {
        return rowType;
    }

    /**
     * 在XML节点中查找指定字段
     * 支持多层级查找，如 root.child.field
     */
    private JsonNode findFieldInXml(JsonNode rootNode, String fieldName) {
        // 首先尝试直接查找
        JsonNode directField = rootNode.get(fieldName);
        if (directField != null) {
            return directField;
        }

        // 如果包含.，尝试层级查找
        if (fieldName.contains(".")) {
            String[] parts = fieldName.split("\\.");
            JsonNode currentNode = rootNode;
            for (String part : parts) {
                if (currentNode == null) {
                    break;
                }
                currentNode = currentNode.get(part);
            }
            return currentNode;
        }

        // 递归查找第一层子节点
        if (rootNode.isObject()) {
            rootNode.fieldNames().forEachRemaining(name -> {
                // 这里可以实现更复杂的查找逻辑
            });
        }

        return null;
    }

    /**
     * 将XML节点的值转换为指定的数据类型
     */
    private Object convertXmlValue(JsonNode node, SeaTunnelDataType<?> fieldType) {
        try {
            switch (fieldType.getSqlType()) {
                case STRING:
                    return node.asText();
                case INT:
                    return node.asInt();
                case BIGINT:
                    return node.asLong();
                case DOUBLE:
                    return node.asDouble();
                case FLOAT:
                    return (float) node.asDouble();
                case BOOLEAN:
                    return node.asBoolean();
                case DECIMAL:
                    return node.decimalValue();
                default:
                    log.debug("Unsupported field type: {}, converting to string", fieldType.getSqlType());
                    return node.asText();
            }
        } catch (Exception e) {
            log.warn("Failed to convert XML value '{}' to type {}, using string representation",
                    node.asText(), fieldType.getSqlType());
            return node.asText();
        }
    }
}
