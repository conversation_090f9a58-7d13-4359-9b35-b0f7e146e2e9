export default [
  {
    path: '/metadata',
    name: '元数据管理',
    icon: 'system.png',
    routes: [
      {
        path: '/metadata/dataSourceModel',
        icon: 'user',
        name: '数据源模型管理',
        component: './modules/Metadata/DataSourceModel/DataSourceModel',
      },
      {
        path: '/metadata/dataSource',
        icon: 'user',
        name: '数据源管理',
        component: './modules/Metadata/DataSource/DataSource',
      },
      {
        path: '/metadata/dataSourceTable',
        icon: 'user',
        name: '数据源表管理',
        component: './modules/Metadata/DataSourceTable/DataSourceTable',
      },

      {
        path: '/metadata/dataSourceResource',
        icon: 'user',
        name: '数据源资源管理',
        component: './modules/Metadata/DataSourceResource/DataSourceResource',
      },
    ],
  },
  {
    path: '/integrate',
    icon: 'system.png',
    name: '数据集成管理',
    routes: [
      {
        path: '/integrate/dataSync',
        name: '数据同步任务',
        icon: 'user',
        component: './modules/Integrate/DataSync/DataSync',
      },
      {
        path: '/integrate/dataSyncModel',
        icon: 'user',
        name: '数据同步模型',
        component: './modules/Metadata/DataSyncModel/DataSyncModel',
      },
      {
        path: '/integrate/dataSyncLog',
        name: '数据同步日志',
        icon: 'user',
        component: './modules/Scheduler/Log/Log',
      },
    ],
  },
  {
    path: '/scheduler',
    icon: 'system.png',
    name: '调度任务管理',
    routes: [],
  },
  {
    path: '/developer',
    icon: 'system.png',
    name: '数据开发管理',
    routes: [
      {
        path: '/developer/process',
        name: '数据开发列表',
        icon: 'user',
        component: './modules/Developer/Process/Process',
      },
      // {
      //   path: '/developer/newProcess',
      //   name: '数据开发列表',
      //   icon: 'user',
      //   component: './modules/Developer/NewProcess/index',
      // },
      {
        path: '/developer/processEditor',
        icon: 'system.png',
        name: '数据开发编辑',
        component: './modules/Developer/Process/Editor/index.tsx',
      },
      {
        path: '/developer/processRecord',
        name: '数据开发运行记录',
        icon: 'user',
        component: './modules/Developer/Process/Record',
      },
      {
        path: '/developer/history',
        name: '运行历史记录',
        icon: 'user',
        component: './modules/Developer/History/History',
      },
    ],
  },
  {
    path: '/dataservice',
    icon: 'system.png',
    name: '数据服务管理',
    routes: [
      {
        path: '/dataservice/list',
        name: '数据服务列表',
        icon: 'user',
        component: './modules/DataService/DataService',
      },

      {
        path: '/dataservice/serviceAuthorization',
        icon: 'database',
        name: '数据服务类别',
        component: './modules/Sharing/ServiceManager/ServiceManager',
      },
      {
        path: '/dataservice/list',
        name: '消息服务授权',
        icon: 'user',
        component: './modules/DataService/DataService',
      },
      {
        path: '/dataservice/messageTopic',
        name: '消息主题授权',
        icon: 'user',
        component: './modules/DataService/MessageTopic/index',
      },
      {
        path: '/dataservice/applicationAuthorization',
        icon: 'database',
        name: '客户访问授权',
        component: './modules/Sharing/ApplicationManager/ApplicationManager',
      },
      {
        path: '/dataservice/modeldesign',
        name: '数据服务模型',
        icon: 'user',
        component: './modules/DataService/ModelDesign',
      },
    ],
  },
  {
    path: '/asset',
    icon: 'system.png',
    name: '数据资产管理',
    routes: [
      {
        path: '/asset/summary',
        name: '数据资产总览',
        icon: 'user',
        component: './governance/AssetSummary/index',
      },
      {
        path: '/asset/category',
        name: '数据资产目录',
        icon: 'user',
        component: './governance/AssetCategory/index',
      },
      {
        path: '/asset/search',
        name: '数据资产搜索',
        icon: 'user',
        component: './governance/AssetSearch/index',
      },
    ],
  },
  {
    path: '/lifecycle',
    icon: 'system.png',
    name: '数据生命周期',
    routes: [
      {
        path: '/lifecycle/dataLineage',
        name: '数据血缘关系',
        icon: 'user',
        component: './modules/Lifecycle/DataLineage/index',
      },
      {
        path: '/lifecycle/fileclearance',
        name: '数据归档清除',
        icon: 'user',
        component: './modules/Lifecycle/FileClearance/index',
      },
      {
        path: '/lifecycle/archivingRecord',
        name: '归档记录',
        icon: 'user',
        component: './modules/Lifecycle/FileClearance/component/ArchivingRecord',
      },
    ],
  },
  {
    path: '/quality',
    icon: 'system.png',
    name: '数据质量管理',
    routes: [
      {
        path: '/quality/summary',
        name: '数据质量总览',
        icon: 'user',
        component: './governance/QualitySummary/index',
      },
      {
        path: '/quality/ruleconfig',
        name: '质量规则配置',
        icon: 'user',
        component: './governance/QualityRuleConfig/index',
      },
      {
        path: '/quality/ruleconfigmanage',
        name: '表规则配置',
        icon: 'user',
        component: './governance/QualityRuleConfig/DatatableRuleConfig',
      },
      {
        path: '/quality/ruletemp',
        name: '质量规则模板',
        icon: 'user',
        component: './governance/QualityRuleTemp/index',
      },
      {
        path: '/quality/runlog',
        name: '规则运行日志',
        icon: 'user',
        component: './governance/QualityRuleRunLog/index',
      },
      {
        path: '/quality/schedulerlog',
        name: '规则调度日志',
        icon: 'user',
        component: './governance/QualityRuleSchedulerLog/index',
      },
    ],
  },
];
