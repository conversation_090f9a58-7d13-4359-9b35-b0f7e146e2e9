<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xmcares.platform</groupId>
        <artifactId>xotp-release</artifactId>
        <version>1.0.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>xotp-release-hookbox</artifactId>

    <properties>
        <project.artifactName>xotp-hookbox</project.artifactName>
    </properties>


    <dependencies>

        <dependency>
            <groupId>com.xmcares.platform</groupId>
            <artifactId>xotp-hookbox-integrator</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.datax</groupId>
            <artifactId>datax-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.datax</groupId>
            <artifactId>datax-transformer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.datax</groupId>
            <artifactId>datax-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.datax</groupId>
            <artifactId>plugin-rdbms-util</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xmcares.framework</groupId>
            <artifactId>xcnf-data-fsclient</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-net</artifactId>
                    <groupId>commons-net</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xmcares.platform</groupId>
            <artifactId>xotp-admin-common</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <!-- The exclusion rule is recommended to be consistent with the import rules in the assembly.xml file -->
                    <excludes>
                        <!-- Custom configuration -->
                        <exclude>*.yml</exclude>
                        <exclude>*.xml</exclude>
                        <exclude>*.properties</exclude>
                        <exclude>static/**</exclude>
                        <!-- Must be configured -->
                        <exclude>*.conf</exclude>
                        <exclude>tools/**</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <executions>
                    <execution>
                        <id>copy-seatunnel-zip</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/${project.artifactName}</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/pack</directory>
                                    <includes>
                                        <include>*.zip</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>appassembler-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>generate-daemon-scripts</id>
                        <phase>package</phase>
                        <goals>
                            <goal>assemble</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <!-- 支持平台 -->
                    <platforms>
                        <platform>windows</platform>
                        <platform>unix</platform>
                    </platforms>
                    <!-- 打包根目录 -->
                    <assembleDirectory>${project.build.directory}/${project.artifactName}</assembleDirectory>
                    <!-- JAR依赖包放到这个目录里面 -->
                    <repositoryName>lib</repositoryName>
                    <!-- lib目录中jar的存放规则，默认是${groupId}/${artifactId}的目录格式，flat表示直接把jar放到lib目录 -->
                    <repositoryLayout>flat</repositoryLayout>
                    <useWildcardClassPath>true</useWildcardClassPath>
                    <!-- bin脚本生成目录 -->
                    <binFolder>bin</binFolder>
                    <!-- 拷贝配置文件到conf -->
                    <configurationDirectory>conf</configurationDirectory>
                    <copyConfigurationDirectory>true</copyConfigurationDirectory>
                    <configurationSourceDirectory>src/main/resources</configurationSourceDirectory>
                    <includeConfigurationDirectoryInClasspath>true</includeConfigurationDirectoryInClasspath>

                    <!-- 日志和临时目录 -->
                    <logsDirectory>logs</logsDirectory>
                    <tempDirectory>tmp</tempDirectory>

                    <encoding>UTF-8</encoding>

                    <!-- 主程序配置 -->
                    <programs>
                        <program>
                            <id>${project.artifactName}</id>
                            <mainClass>com.xmcares.platform.hookbox.HookboxApplication</mainClass>
                            <jvmSettings>
                                <extraArguments>
                                    <extraArgument>-server</extraArgument>
                                    <extraArgument>-Xmx2G</extraArgument>
                                    <extraArgument>-Xms2G</extraArgument>
                                    <extraArgument>-Dapplication.version=${project.version}</extraArgument>
                                </extraArguments>
                            </jvmSettings>
                        </program>
                    </programs>
                </configuration>
            </plugin>

        </plugins>
    </build>
</project>