/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.hookbox.core.util;

/**
 * data types in user define parameter
 */
public enum DataType {
    /**
     * 0 string
     * 1 integer
     * 2 long
     * 3 float
     * 4 double
     * 5 date, "YYYY-MM-DD"
     * 6 time, "HH:MM:SS"
     * 7 time stamp
     * 8 Boolean
     * 9 list <String>
     */
    VARCHAR,INTEGER,LONG,FLOAT,DOUBLE,DATE,TIME,TIMESTAMP,BOOLEAN,LIST
}
