/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/16
 */
package com.xmcares.platform.hookbox.common.util;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.IThrowableProxy;
import ch.qos.logback.classic.spi.ThrowableProxyUtil;
import ch.qos.logback.core.AppenderBase;
import com.xxl.job.core.context.XxlJobHelper;

import java.util.List;

/**
 * 自定义 Logback Appender，将日志转发到 XxlJobHelper.log
 * <AUTHOR>
 * @since 2.1.0
 */
public class XxlJobLogAppender extends AppenderBase<ILoggingEvent> {

    @Override
    protected void append(ILoggingEvent eventObject) {
        if (!isStarted()) {
            return; // Appender 未启动时不处理日志
        }

        // 获取原始日志消息模式和参数
        String messagePattern = eventObject.getMessage();
        Object[] argumentArray = eventObject.getArgumentArray();

        // 构建日志前缀，包含级别和 Logger 名
        StringBuilder logPrefix = new StringBuilder();
        logPrefix.append("[").append(eventObject.getLevel().toString()).append("]");
        logPrefix.append(" [").append(eventObject.getLoggerName()).append("] ");

        // 最终的日志模式
        String finalLogPattern = logPrefix.append(messagePattern).toString();

        // 检查是否有异常信息
        IThrowableProxy throwableProxy = eventObject.getThrowableProxy();

        if (throwableProxy != null) {
            // 将异常堆栈格式化为字符串。
            // 将 IThrowableProxy 转换为完整的堆栈字符串。
            String stackTrace = ThrowableProxyUtil.asString(throwableProxy);

            // 将原始参数数组转换为 List，以便添加新的元素
            List<Object> argList = argumentArray != null ?
                    new java.util.ArrayList<>(java.util.Arrays.asList(argumentArray)) :
                    new java.util.ArrayList<>();

            // 在日志消息的末尾添加堆栈信息
            // XxlJobHelper.log(String, Object...) 就可以接收到完整的日志和堆栈
            // 在日志模式中为堆栈信息添加一个额外的占位符
            finalLogPattern += "\n{}"; // 添加一个占位符用于堆栈
            argList.add(stackTrace); // 将堆栈字符串作为最后一个参数

            XxlJobHelper.log(finalLogPattern, argList.toArray());

        } else {
            // 没有异常，直接调用带占位符的 log 方法
            XxlJobHelper.log(finalLogPattern, argumentArray);
        }
    }
}
