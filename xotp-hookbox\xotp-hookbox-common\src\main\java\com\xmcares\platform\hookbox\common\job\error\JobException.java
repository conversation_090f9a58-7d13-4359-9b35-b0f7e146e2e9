/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/3
 */
package com.xmcares.platform.hookbox.common.job.error;

import com.xmcares.framework.commons.error.BaseException;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public abstract class JobException extends BaseException {

    private final String jobId;

    public JobException(String jobId) {
        this.jobId = jobId;
    }

    public JobException(String jobId, String message) {
        super(message);
        this.jobId = jobId;
    }

    public JobException(String jobId, String message, Throwable cause) {
        super(message, cause);
        this.jobId = jobId;
    }

    public JobException(String jobId, Throwable cause) {
        super(cause);
        this.jobId = jobId;
    }

    public JobException(String jobId, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.jobId = jobId;
    }

    public String getJobId() {
        return jobId;
    }


    @Override
    public String getMessage() {
        return "[任务ID = "+this.getJobId()+"]"+ super.getMessage();
    }

}
