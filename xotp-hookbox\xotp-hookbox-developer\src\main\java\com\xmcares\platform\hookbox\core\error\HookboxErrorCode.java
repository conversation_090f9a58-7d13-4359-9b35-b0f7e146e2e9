package com.xmcares.platform.hookbox.core.error;

import com.xmcares.framework.commons.error.ErrorCode;

/**
 * <AUTHOR>
 * @date 2022/6/29 9:08
 **/
public enum HookboxErrorCode implements ErrorCode {

    /** 数据开发文件下载异常 */
    DEV_FILE_DOWNLOAD("10000001", "数据开发文件下载异常"),
    HOOK_LOCAL_FILE_UNDEFINE("00000001", "勾盒未配置本地管理路径"),
    HOOK_LOCAL_FILE_CRETE_ERROR("00000002", "勾盒配置的本地管理路径自动创建失败"),
    HOOK_LOCAL_FILE_NOT_DIR("00000003", "勾盒配置的本地管理路径不是一个目录"),
    HOOK_RUN_PARAM_NOT_FIND("00000004","运行时缺少必要的参数"),

    HOOK_SQL_EXECUTE_ERROR("20000001", "批SQL任务执行SQL时错误"),
    HOOK_SQL_NOT_SUPPORT_ERROR("20000002", "批SQL任务为得到支持"),
    HOOK_SQL_EXECUTE_CREATE_ERROR("20000003", "批SQL任务执行器创建失败"),
    HOOK_SQL_CONN_CREATE_ERROR("20000004","创建连接失败"),
    HOOK_SQL_HIVE_DOWNLOAD_KRB5_ERROR("20000005","下载KRB5文件失败"),
    HOOK_SQL_HIVE_DOWNLOAD_KEY_ERROR("20000006","下载KEYTAB文件失败"),
    HOOK_SQL_HIVE_CREATE_PROXY_USER_ERROR("20000007","创建代理用户失败"),

    HOOK_SYNC_PARAM_UNFIND_ERROR("30000001", "动态参数解析异常，动态参数值未找到"),


    HOOK_FLINK_JOB_UNFIND("40000001","JOB不存在"),
    HOOK_FLINK_JOB_CANCEL_ERROR("40000002","JOB任务取消失败"),
    HOOK_FLINK_FIND_JOB_ERROR("40000003","获取JOB信息失败"),

    ;


    private final String code;

    private final String description;

    private HookboxErrorCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String code() {
        return this.code;
    }

    public String description() {
        return description;
    }

    @Override
    public String toString() {
        return String.format("Code:[%s], Description:[%s]. ", this.code,
                this.description);
    }
}
