/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/24
 */
package com.xmcares.platform.seatunnel.connectors.artemis.source;

import com.google.auto.service.AutoService;
import org.apache.seatunnel.api.common.JobContext;
import org.apache.seatunnel.api.common.PrepareFailException;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.source.Boundedness;
import org.apache.seatunnel.api.source.SeaTunnelSource;
import org.apache.seatunnel.api.source.SourceReader;
import org.apache.seatunnel.api.source.SourceSplitEnumerator;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.CatalogTableUtil;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.common.constants.JobMode;
import org.apache.seatunnel.common.constants.PluginType;
import org.apache.seatunnel.shade.com.typesafe.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SeaTunnel Source for Apache Artemis MQ. Supports Artemis Core Client (non-JMS) and configurable
 * message formats (JSON, XML, Text).
 *
 * <AUTHOR>
 * @since 2.1.0
 */
@AutoService(SeaTunnelSource.class)
public class ArtemisSource
        implements SeaTunnelSource<SeaTunnelRow, ArtemisSourceSplit, ArtemisSourceState> {
    private final Logger log = LoggerFactory.getLogger(ArtemisSource.class);
    private ReadonlyConfig config;
    private JobContext jobContext;
    private SeaTunnelRowType producedType; // This will hold the parsed schema

    public ArtemisSource() {
    }

    @Override
    public Boundedness getBoundedness() {
        // 对于像 Artemis 这样的消息队列，源通常是 UNBOUNDED（流式处理）。
        // 但是，如果作业模式是 BATCH，我们应该将其声明为 BOUNDED。
        // 这允许 SeaTunnel 知道在作业以批处理模式运行时何时停止作业。
        // 实际上，对于 MQ，“有界”作业意味着使用所有现有消息达到一定程度然后完成，这通常需要外部触发或具体逻辑。
        // 为简单起见，我们与作业模式保持一致。
        return JobMode.BATCH.equals(jobContext.getJobMode())
                ? Boundedness.BOUNDED
                : Boundedness.UNBOUNDED;
    }

    @SuppressWarnings("deprecation")
    @Override
    public void prepare(Config pluginConfig) throws PrepareFailException {
        // The ReadonlyConfig constructor already validates options based on OptionRule
        // defined in ArtemisSourceFactory. Here we primarily parse the schema.
        log.info("Preparing Artemis Source connector.");
        this.config = ReadonlyConfig.fromConfig(pluginConfig);

        String clientId = config.getOptional(ArtemisSourceFactory.CLIENT_ID).orElse(null);
        String topicName = config.getOptional(ArtemisSourceFactory.TOPIC).orElse(null);
        if (clientId == null || clientId.isEmpty()) {
            throw new PrepareFailException(
                    getPluginName(),
                    PluginType.SOURCE,
                    "Client ID is required for durable topic subscriptions.");
        }
        if (topicName == null || topicName.isEmpty()) {
            throw new PrepareFailException(
                    getPluginName(),
                    PluginType.SOURCE,
                    "Subscription Name is required for durable topic subscriptions.");
        }

        // Parse schema from CatalogTable
        // The SCHEMA option is a CatalogTable object in SeaTunnel
        CatalogTable catalogTable = CatalogTableUtil.buildWithConfig(config);
        this.producedType = catalogTable.getSeaTunnelRowType();
        log.info("Schema parsed successfully: {}", producedType);
        log.info("Artemis Source connector preparation complete.");
    }

    @Override
    public SourceReader<SeaTunnelRow, ArtemisSourceSplit> createReader(
            SourceReader.Context readerContext) throws Exception {
        // The reader context provides information about the current subtask/parallel instance.
        // The ReadonlyConfig holds all the parsed plugin configuration.
        // The producedType is the schema we derived in the prepare() method.
        log.info(
                "Creating Artemis Source Reader for subtask: {}",
                readerContext.getIndexOfSubtask());
        return new ArtemisSourceReader(config, readerContext, producedType);
    }

    @Override
    public SourceSplitEnumerator<ArtemisSourceSplit, ArtemisSourceState> createEnumerator(
            SourceSplitEnumerator.Context<ArtemisSourceSplit> enumeratorContext) throws Exception {
        // 对于像 Artemis 这样的消息队列，一个简单的枚举器通常就足够了。
        // 它通常为每个读取器实例分配一个“拆分”。
        // 在更复杂的方案中，它可能会动态发现队列/主题或监视分区。
        log.info("Creating Artemis Source Split Enumerator.");
        return new ArtemisSourceSplitEnumerator(enumeratorContext, getBoundedness());
    }

    @Override
    public SourceSplitEnumerator<ArtemisSourceSplit, ArtemisSourceState> restoreEnumerator(
            SourceSplitEnumerator.Context<ArtemisSourceSplit> enumeratorContext,
            ArtemisSourceState checkpointState)
            throws Exception {
        // If the enumerator has state (e.g., list of discovered partitions, or assignment status),
        // it would be restored here from the checkpointState.
        // For a simple message queue, the enumerator often doesn't need to restore complex state.
        log.info("Restoring Artemis Source Split Enumerator from checkpoint state.");
        return new ArtemisSourceSplitEnumerator(
                enumeratorContext, checkpointState, getBoundedness());
    }

    @Override
    public void setJobContext(JobContext jobContext) {
        this.jobContext = jobContext;
    }

    @SuppressWarnings("deprecation")
    @Override
    public SeaTunnelDataType<SeaTunnelRow> getProducedType() {
        return this.producedType;
    }

    @Override
    public String getPluginName() {
        return ArtemisSourceFactory.IDENTIFIER;
    }
}
