package com.xmcares.platform.hookbox.integrator.service;

import cn.hutool.core.date.DateUtil;
import com.xmcares.platform.hookbox.common.job.JobContext;
import com.xmcares.platform.hookbox.common.job.JobContextService;
import com.xmcares.platform.hookbox.common.job.context.JobContextImpl;
import com.xmcares.platform.hookbox.common.job.seatunnel.RemoteSeaTunnelJobContextService;
import com.xmcares.platform.hookbox.common.job.seatunnel.SeaTunnelJobInfo;
import com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.concurrent.*;

/**
 * SeaTunnelJobPoller
 *
 * <AUTHOR>
 * @Descriptions SeaTunnelJobPoller
 * @Date 2025/7/28 11:10
 */
@Component
public class SeaTunnelJobPoller {

    private static final Logger logger = LoggerFactory.getLogger(SeaTunnelJobPoller.class);

    private ScheduledExecutorService scheduler;

    // 存储每个jobId对应的轮询任务，方便后续按需停止
    private final ConcurrentHashMap<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();

    @Resource
    private JobContextService jobContextService;

    @Autowired
    private DatasyncJobInstanceService datasyncJobInstanceService;

    @PostConstruct
    public void init() {
        // 创建一个单线程的线程池用于轮询，如果轮询任务多且耗时，可以适当增加线程数
        scheduler = Executors.newSingleThreadScheduledExecutor();
        logger.info("SeaTunnel 作业轮询服务已初始化。");
    }

    /**
     * 为指定的作业实例开始一个轮询任务。
     * @param jobInstance 需要监控的作业实例
     */
    public void startPolling(DatasyncJobInstance jobInstance) {
        String jobId = jobInstance.getId();
        if (scheduledTasks.containsKey(jobId)) {
            logger.warn("作业实例 [{}] 的轮询任务已经存在，无需重复启动。", jobId);
            return;
        }

        logger.info("为流式作业 [{}] 启动状态和指标轮询...", jobId);

        Runnable pollingTask = () -> {
            try {
                JobContext jobContext = new JobContextImpl(jobInstance.getJobId(), jobInstance.getId(), jobInstance.getJobName());
                SeaTunnelJobInfo jobInfo = jobContextService.getJobLogInfo(jobContext);

                // 如果API调用失败或任务未找到，则停止轮询
                if (jobInfo == null) {
                    logger.warn("轮询作业 [{}] 时未能获取到信息，将停止此任务的轮询。", jobId);
                    stopPolling(jobId);
                    return;
                }

                // 更新指标
                updateInstanceMetrics(jobInstance, jobInfo);

                String jobStatus = jobInfo.getJobStatus();
                boolean isTerminal = "FINISHED".equals(jobStatus) || "FAILED".equals(jobStatus) || "CANCELED".equals(jobStatus);

                // 如果任务已进入最终状态，则停止轮询
                if (isTerminal) {
                    logger.info("轮询检测到作业 [{}] 状态已变为最终态: {}，将停止轮询并进行最后的状态同步。", jobId, jobStatus);
                    // 调用最终状态同步方法，确保数据完全一致
                    reconcileFinalJobState(jobInstance);
                } else {
                    // 仅更新数据库中的指标和时间
                    datasyncJobInstanceService.updateById(jobInstance);
                    logger.debug("作业 [{}] 实时指标已更新。", jobId);
                }

            } catch (Exception e) {
                logger.error("轮询作业 [{}] 状态时发生未预期的错误，将停止轮询: {}", jobId, e.getMessage(), e);
                stopPolling(jobId); // 发生异常时也停止，避免无效请求
            }
        };

        // 立即执行第一次，之后每隔10秒执行一次
        ScheduledFuture<?> taskFuture = scheduler.scheduleAtFixedRate(pollingTask, 0, 10, TimeUnit.SECONDS);
        scheduledTasks.put(jobId, taskFuture);
    }

    /**
     * 停止指定JobId的轮询任务。
     * @param jobId 作业实例ID
     */
    public void stopPolling(String jobId) {
        ScheduledFuture<?> taskFuture = scheduledTasks.remove(jobId);
        if (taskFuture != null) {
            // true表示即使任务正在执行，也尝试中断它
            taskFuture.cancel(true);
            logger.info("已停止作业实例 [{}] 的轮询任务。", jobId);
        }
    }

    /**
     * 轮询器内部使用的最终状态同步方法，确保在轮询检测到结束后进行一次权威更新。
     * @param jobInstance
     */
    private void reconcileFinalJobState(DatasyncJobInstance jobInstance) {
        // 确保停止轮询
        stopPolling(jobInstance.getId());

        // 此处可以直接调用外部事件处理器的最终状态同步逻辑，或者在这里实现一个简化版
        // 为了逻辑统一，建议此处只负责停止，最终状态同步由EventHandler统一处理
        // 但如果希望轮询器能独立完成闭环，可以添加API调用和DB更新逻辑
        logger.info("轮询器建议对作业 [{}] 进行最终状态同步。", jobInstance.getId());
    }

    /**
     * 使用从API获取的信息更新JobInstance对象的指标。
     */
    private void updateInstanceMetrics(DatasyncJobInstance jobInstance, SeaTunnelJobInfo jobInfo) {
        if (jobInfo.getMetrics() != null) {
            SeaTunnelJobInfo.Metrics metrics = jobInfo.getMetrics();
            jobInstance.setReceivedCount(metrics.getSourceReceivedCount() != null ? metrics.getSourceReceivedCount() : jobInstance.getReceivedCount());
            jobInstance.setReceviedQps(metrics.getSourceReceivedQPS() != null ? metrics.getSourceReceivedQPS() : jobInstance.getReceviedQps());
            jobInstance.setWritedCount(metrics.getSinkWriteCount() != null ? metrics.getSinkWriteCount() : jobInstance.getWritedCount());
            jobInstance.setWritedQps(metrics.getSinkWriteQPS() != null ? metrics.getSinkWriteQPS() : jobInstance.getWritedQps());
        }
        jobInstance.setUpdateTime(DateUtil.date());
    }

    @PreDestroy
    public void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            logger.info("SeaTunnel 作业轮询服务已关闭。");
        }
    }
}
