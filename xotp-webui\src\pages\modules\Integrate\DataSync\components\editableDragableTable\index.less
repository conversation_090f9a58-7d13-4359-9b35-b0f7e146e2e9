/* DragTable.css */

/* 拖拽时行的样式 */
.row-dragging {
  background: #f5f5f5;
  border: 1px solid #1890ff;
  z-index: 9999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.row-dragging td {
  padding: 16px;
  visibility: visible !important;
}

.row-dragging .drag-visible {
  visibility: visible !important;
}

/* 拖动手柄悬停效果 */
.drag-handle:hover {
  color: #1890ff !important;
}

/* 可编辑行样式 */
.editable-row:hover .drag-handle {
  color: #1890ff;
}

.editable-row td {
  transition: all 0.3s;
}

/* 添加悬停效果 */
.editable-row:hover {
  background-color: #f5f5f5;
}

/* 拖拽指示文字 */
.drag-instruction {
  margin-bottom: 16px;
  color: #999;
  font-size: 14px;
}

/* 确保在Modal中正确显示 */
.ant-modal-body .editable-drag-table {
  margin: -24px;
  padding: 24px;
}

.ant-modal-body .ant-table-wrapper {
  margin: 0 -24px;
}

/* 确保拖拽时行可见 */
.ant-modal-root {
  z-index: 1050;
}

.row-dragging {
  z-index: 1060 !important;
}

/* 确保Modal内容可见 */
.ant-modal-wrap,
.ant-modal,
.ant-modal-content,
.ant-modal-body {
  overflow: visible !important;
}


.modalDragTableContainer{
  :global{
    .ant-pro-card-body{
      padding: 0px !important;
    }
  }
}
