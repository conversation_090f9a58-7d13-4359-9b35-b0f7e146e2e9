/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/20
 */
package com.xmcares.platform.hookbox.common.util;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.turbo.TurboFilter;
import ch.qos.logback.core.spi.FilterReply;
import org.slf4j.MDC;
import org.slf4j.Marker;

/**
 * Logback 过滤器：只允许 MDC 中包含指定 key 的日志通过。
 * <AUTHOR>
 * @since 2.1.0
 */
public class XxlJobMDCFilter extends TurboFilter {

    private String key;
    private String value;

    public void setKey(String key) {
        this.key = key;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public FilterReply decide(Marker marker, ch.qos.logback.classic.Logger logger,
                              Level level, String format, Object[] params, Throwable t) {
        String mdcValue = MDC.get(key);
        if (value.equals(mdcValue)) {
            return FilterReply.ACCEPT;
        } else {
            return FilterReply.DENY;
        }
    }
}
