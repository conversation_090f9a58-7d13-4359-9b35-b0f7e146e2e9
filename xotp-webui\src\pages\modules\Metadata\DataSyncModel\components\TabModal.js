import React, {useRef, useState,useMemo} from 'react';
import {Space, Divider} from 'antd';
import ProForm, {
  ModalForm,
  ProFormText,
  ProFormRadio,
  ProFormSelect,
} from '@ant-design/pro-form';
import ProCard from '@ant-design/pro-card';
import {connect} from 'dva';
import DynamicSettings from './DynamicSettings';
import Editor from '@/pages/modules/Metadata/DataSourceModel/components/Editor';
import {create, update} from '@/services/Metadata/DataSyncModel/DataSyncModel'
import {messageModal} from "@/utils/messageModal";
import {datasyncSourceModelList, datasyncSourceModelAddList} from '@/services/Metadata/DataSyncModel/DataSyncModel';
import * as styles from '../index.less'

const Demo = ({name, title, children, dispatch, selectedRows, mark, queryList, baseSchemas, advSchemas, highSchemas}) => {
  const restFormRef = useRef();
  const formRef = useRef();
  const defaultColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
    },
  ];
  const defaultValue = {
    type: 'object',
    properties: {},
  };
  const [modalVisible, setModalVisible] = useState(false);
  const [selectList, setSelectList] = useState([]);
  const [columns, setColumns] = useState(defaultColumns);
  const [radio, setRadio] = useState('0');
  const [tab, setTab] = useState('base');
  const [options, setOptions] = useState([
    {
      label: '读',
      value: '0',
    },
    {
      label: '写',
      value: '1',
    },
  ]);

  // 判断获取数据源还是插件列表
  const getSelectList = async () => {
    let res;
    if (mark == 'edit') {
      res = await datasyncSourceModelList();
    } else {
      res = await datasyncSourceModelAddList();
    }
    if (res && res.length > 0) {
      const data = res.map(item => {
        return {label: item.type, value: item.id, supportMode: item?.supportMode || '2'}
      });
      setSelectList(data);
    } else {
      return []
    }
  }

  const formItemLayout = {
    labelCol: {span: 8},
    wrapperCol: {span: 16},
  }

  const renderForm =useMemo(()=>{
    return (
      <ProForm
        {...formItemLayout}
        layout="inline"
        submitter={false}
        formRef={formRef}
        grid={true}
        rowProps={{
          gutter: [16, 8],
        }}
        initialValues={{
          integrationWay: '0',
          runSchema: '1',
          integrationType: '0'

        }}
      >
        <ProFormText name="modelName" label="集成模型名称" colProps={{span: 7}} placeholder="请输入名称"/>
        <ProFormRadio.Group
          name="integrationWay"
          label="集成方式"

          colProps={{span: 5}}
          fieldProps={{
            onChange(e) {
              setRadio(e.target.value)
            },
            disabled: mark == 'edit',
          }}
          options={[
            {
              label: '内置',
              value: '0',
            },
            {
              label: '插件',
              value: '1',
            },
          ]}
        />
        <ProFormSelect
          name="datasourceModelId"
          options={selectList}
          showSearch
          hidden={radio == '1'||(mark=='edit'&&selectedRows?.length>0&&selectedRows[0]?.integrationWay=='1')}
          fieldProps={{
            onChange: (v, x) => {
              let options = [
                {
                  label: '读',
                  value: '0',
                },
                {
                  label: '写',
                  value: '1',
                },
              ];
              if(x?.supportMode){
                let {supportMode} = x;
                if (x&&supportMode == '0') {
                  options = [options[0]]
                }
                if (supportMode == '1') {
                  options = [options[1]]
                }
                formRef.current.setFieldsValue({
                  integrationType: supportMode == '2' ? '0' : supportMode,
                });
              }
              setOptions(options);
            },
            disabled: mark == 'edit',

            filterOption: (input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())

          }}
          colProps={{span: 7}}
          label="数据源"
          placeholder="请选择"
          rules={[{required:!(mark=='edit'&&selectedRows?.length>0&&selectedRows[0]?.integrationWay=='1'), message: '不能为空!'}]}
        />
        <ProFormRadio.Group
          colProps={{span: 7}}
          name="runSchema"
          label="运  行  模  式 "

          fieldProps={{
            disabled: true,
          }}
          options={[
            {
              label: '不停循环',
              value: '0',
            },
            {
              label: '手动操作',
              value: '1',
            },
          ]}
        />
        <ProFormRadio.Group
          colProps={{span: 5}}
          name="integrationType"
          label="模型类别"

          fieldProps={{
            disabled: mark == 'edit',
          }}
          options={options}
        />
        <ProFormText colProps={{span: 7}} name="remark" label="描 述" placeholder="请输入描述"/>
      </ProForm>
    )
  },[mark,selectedRows,selectList,radio])
  const setVisible=(flag)=>{
    setModalVisible(flag);
    if(!flag){
      formRef.current.resetFields();
      setColumns(defaultColumns);

      dispatch({
        type: `dataSyncModel/saveBaseSchemas`,
        payload: defaultValue,
      });
      dispatch({
        type: `dataSyncModel/saveAdvSchemas`,
        payload: defaultValue,
      });
      dispatch({
        type: `dataSyncModel/saveHighSchemas`,
        payload: defaultValue,
      });
    }
  }


  return (
    <Space>
      <ModalForm
        title={title}
        formRef={restFormRef}
        visible={modalVisible}
        width={1300}
        layout={'inline'}
        modalProps={{
          bodyStyle: {
            padding: 12
          },
        }}
        trigger={
          <>
            {children}
            <a onClick={async () => {
              await getSelectList();
              dispatch({
                type: `global/setTab`,
                payload: 'base',
              });
              setTab('base');
              if (mark === 'edit') {
                if (selectedRows && selectedRows.length > 0) {
                  setModalVisible(true);
                  let {modelName, integrationWay, datasourceModelId, runSchema, integrationType, remark, advJson, baseJson, highJson, columnJson} = selectedRows[0];
                  formRef.current?.setFieldsValue({
                    modelName,
                    integrationWay,
                    runSchema,
                    integrationType,
                    remark
                  });
                  if(integrationWay=='0'){
                    formRef.current?.setFieldsValue({
                      datasourceModelId,
                    });
                  }
                  if (columnJson == null) {
                    columnJson = '[]';
                  }
                  setColumns(JSON.parse(columnJson));
                  dispatch({
                    type: `dataSyncModel/saveBaseSchemas`,
                    payload: JSON.parse(baseJson),
                  });
                  dispatch({
                    type: `dataSyncModel/saveAdvSchemas`,
                    payload: JSON.parse(advJson),
                  });
                  dispatch({
                    type: `dataSyncModel/saveHighSchemas`,
                    payload: JSON.parse(highJson),
                  });
                } else {
                  messageModal('warning', '请选择一条记录!');
                  return;
                }
              }
              if (mark == 'add') {
                setModalVisible(true);
              }
            }}>{name}</a>
            <Divider type="vertical"/>
          </>
        }
        onVisibleChange={setVisible}
        submitter={{
          searchConfig: {
            resetText: '重置',
          },
          resetButtonProps: {
            onClick: () => {
              restFormRef.current?.resetFields();
            },
          },
        }}
        onFinish={async (values) => {
          return formRef.current?.validateFieldsReturnFormatValue?.().then(async (values) => {
            let data = {
              ...values,
              advJson: JSON.stringify(advSchemas),
              baseJson: JSON.stringify(baseSchemas),
              highJson: JSON.stringify(highSchemas),
              columnJson: JSON.stringify(columns),
            }
            let res;
            if (mark === 'edit') {
              const {id} = selectedRows[0]
              data = {
                ...data,
                id
              }
              res = await update(data);
              if (res) {
                queryList()
                messageModal('success', res?.message || '修改成功');
              } else {
                // messageModal('error', res?.message || '修改失败');
              }
            }
            if (mark === 'add') {
               res = await create(data);
              if (res) {
                queryList();
                messageModal('success', res?.message || '新增成功');
              } else {
                // messageModal('error', '新增失败', res?.message || '新增失败');
              }
            }
            return !!res
          })


        }}
      >
        <ProCard
          tabs={{
            type: 'card',
            activeKey: tab,
            onChange: (key) => {
              if (key !== 'column') {
                dispatch({
                  type: `global/setTab`,
                  payload: key,
                });
              }
              setTab(key)
            },
          }}
        >
          <ProCard.TabPane key="base" tab="基础配置">
            {renderForm}
            <Divider style={{margin: '10px 0px '}}/>
            <Editor syncOrSourceMark={false} markModel={'base'} />
          </ProCard.TabPane>
          <ProCard.TabPane key="adv" tab="进阶配置">
            <Editor syncOrSourceMark={false} markModel={'adv'}/>
          </ProCard.TabPane>
          <ProCard.TabPane key="high" tab="高级配置">
            <Editor syncOrSourceMark={false} markModel={'high'}/>
          </ProCard.TabPane>
          <ProCard.TabPane key="column" tab="列配置">
            <DynamicSettings setColumns={setColumns} columns={columns}/>
          </ProCard.TabPane>
        </ProCard>
      </ModalForm>
    </Space>
  );
};

export default connect(({dataSourceModel, dataSyncModel, global}) => ({
  dataSourceModel,
  dataSyncModel,
  markTab: global.markTab,
  sourceSchemas: dataSourceModel.schemas,
  baseSchemas: dataSyncModel.baseSchemas,
  advSchemas: dataSyncModel.advSchemas,
  highSchemas: dataSyncModel.highSchemas
}))(Demo);
