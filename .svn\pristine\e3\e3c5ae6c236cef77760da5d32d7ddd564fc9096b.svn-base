package com.xmcares.platform.admin.integrator.datasync.web;

import com.alibaba.fastjson.JSONArray;
import com.xmcares.framework.commons.domain.Page;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.vo.ReturnT;
import com.xmcares.platform.admin.integrator.datasync.dto.JobLogPageListDto;
import com.xmcares.platform.admin.integrator.datasync.dto.JobLogPageVo;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncInstance;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobGroup;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobInfo;
import com.xmcares.platform.admin.integrator.datasync.service.DatasyncInstanceService;
import com.xmcares.platform.admin.integrator.datasync.vo.DisplayDataSyncTask;
import com.xmcares.platform.admin.integrator.datasync.vo.UpdateDatasyncTask;
import com.xxl.job.core.biz.model.LogResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/17 9:08
 */
@Validated
@RestController
@RequestMapping("/datasync/instance")
public class DatasyncInstanceController {

    @Autowired
    private DatasyncInstanceService datasyncInstanceService;

    @GetMapping("/pid-list")
    @ResponseBody
    public List<DisplayDataSyncTask> list(@RequestParam(name = "pid") String pid) {
        return datasyncInstanceService.findByParentId(pid);
    }

    @GetMapping("/list")
    @ResponseBody
    public List<DisplayDataSyncTask> list() {
        return datasyncInstanceService.list();
    }


    @GetMapping("/get")
    public DisplayDataSyncTask get(@RequestParam(name = "id") String id) {
        return datasyncInstanceService.get(id);
    }

    @GetMapping("/begin")
    @ResponseBody
    public Boolean begin(@RequestParam(name = "id") String id) {
        datasyncInstanceService.begin(id);
        return true;
    }

    @GetMapping("/stop")
    @ResponseBody
    public Boolean stop(@RequestParam(name = "id") String id) {
        datasyncInstanceService.end(id);
        return true;
    }

    @GetMapping("/trigger")
    @ResponseBody
    public Boolean triggerScheduler(@RequestParam(name = "id") String id) {
        datasyncInstanceService.triggerScheduler(id);
        return true;
    }

    @GetMapping("/delete")
    @ResponseBody
    public Boolean remove(@RequestParam(name = "id") String id) {
        return datasyncInstanceService.transitionToDelete(id);
    }

    @PostMapping("/update")
    @ResponseBody
    public Boolean update(@RequestBody @Validated UpdateDatasyncTask datasyncTask) {
        datasyncInstanceService.update(datasyncTask);
        return true;
    }

    @GetMapping("/displayJson")
    @ResponseBody
    public String displayJson(@RequestParam(name = "id") String id) {
        DatasyncInstance datasync = datasyncInstanceService.checkExit(id);
        return datasync.getTemplateContext();
    }

    @GetMapping("/nextTriggerTime")
    @ResponseBody
    public List<String> nextTriggerTime(@RequestParam(name = "scheduleType") String scheduleType, @RequestParam(name = "scheduleConf") String scheduleConf) {
        return datasyncInstanceService.nextTriggerTime(scheduleType, scheduleConf);
    }

    @GetMapping("/jobLog/clearLog")
    @ResponseBody
    public Boolean clearLog(@RequestParam("jobGroup") int jobGroup, @RequestParam("jobId") int jobId, @RequestParam("type") int type) {
        return datasyncInstanceService.clearLog(jobGroup, jobId, type);
    }

    @GetMapping("/jobLog/logKill")
    @ResponseBody
    public Boolean logKill(@RequestParam("id") long id) {
        return datasyncInstanceService.logKill(id);
    }


    @GetMapping("/jobLog/logDetailCat")
    @ResponseBody
    public LogResult logDetailCat(@RequestParam("logId") long logId, @RequestParam("fromLineNum") int fromLineNum) {
        return datasyncInstanceService.logDetailCat(logId, fromLineNum);
    }

    @GetMapping("/jobLog/getJobsByGroup")
    @ResponseBody
    public List<XxlJobInfo> getJobsByGroup(@RequestParam(name = "jobGroup") int jobGroup) {
        return datasyncInstanceService.getJobsByGroup(jobGroup);
    }

    @GetMapping("/jobLog/jobGroupList")
    @ResponseBody
    public List<XxlJobGroup> jobGroupList() {
        return datasyncInstanceService.jobGroupList();
    }

    @GetMapping("/jobLog/page-query")
    @ResponseBody
    public Page<JobLogPageVo> jobLogPageList(HttpServletRequest request,
                                             @RequestParam(required = false, defaultValue = "1") Integer page,
                                             @RequestParam(required = false, defaultValue = "10") Integer rows,
                                             Integer jobGroup, @RequestParam(required = false) Integer jobId, Integer logStatus, String filterTime, String jobName) {

        JobLogPageListDto jobLogPageListDto = new JobLogPageListDto();
        int start = (page - 1) * rows;
        int length = rows;
        jobLogPageListDto.setStart(start);
        jobLogPageListDto.setLength(length);
        jobLogPageListDto.setJobGroup(jobGroup);
        jobLogPageListDto.setJobId(null != jobId ? jobId : 0);
        jobLogPageListDto.setLogStatus(logStatus);
        jobLogPageListDto.setFilterTime(filterTime);
//        jobLogPageListDto.setJobName(jobName);
        return datasyncInstanceService.jobLogPageList(jobLogPageListDto, page, rows);
    }


    @GetMapping("/logs")
    @ResponseBody
    public List<String> getLogList() {
        return datasyncInstanceService.retrieveLogList();
    }

    @GetMapping("/logs/{logFileName}")
    @ResponseBody
    public String getLogFileContent(@PathVariable String logFileName) {
        return datasyncInstanceService.retrieveLogFileContent(logFileName);
    }


}
