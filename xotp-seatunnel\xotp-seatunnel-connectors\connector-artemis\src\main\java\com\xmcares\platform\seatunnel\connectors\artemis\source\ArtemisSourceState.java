/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/24
 */
package com.xmcares.platform.seatunnel.connectors.artemis.source;

import java.io.Serializable;

/**
 * 表示 Artemis 源拆分枚举器的状态。 对于简单的消息队列连接器，枚举器通常不需要存储 恢复的复杂状态，因为读者可以管理自己的消息消费 偏移/致谢。 这可以是空状态，也可以跟踪
 * 如果动态分配更复杂，则当前分配的拆分。
 *
 * <AUTHOR>
 * @since 2.1.0
 */
public class ArtemisSourceState implements Serializable {

    // 当前设计中，ArtemisSourceState 没有需要持久化的字段。
    // 无参构造函数
    public ArtemisSourceState() {}

    // 静态工厂方法，用于返回一个空状态实例
    public static ArtemisSourceState empty() {
        return new ArtemisSourceState();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        return o != null && getClass() == o.getClass();
    }

    @Override
    public int hashCode() {
        // 因为没有字段，返回一个固定的哈希码
        return 1;
    }
}
