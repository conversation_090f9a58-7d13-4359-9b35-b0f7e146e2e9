/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/29
 */
package com.xmcares.platform.hookbox.integrator.model;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public class DatasyncJobInstance {

    private String id;

    private String jobId;

    private String jobName;

    private String jobType;

    private String jobOptions;

    private String jobParams;

    private long scheduleId;

    private long scheduleLogId;

    private Date scheduleTime;

    private Date triggerTime;

    private String status;

    private String statusMessage;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType;
    }

    public String getJobOptions() {
        return jobOptions;
    }

    public void setJobOptions(String jobOptions) {
        this.jobOptions = jobOptions;
    }

    public String getJobParams() {
        return jobParams;
    }

    public void setJobParams(String jobParams) {
        this.jobParams = jobParams;
    }

    public long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public long getScheduleLogId() {
        return scheduleLogId;
    }

    public void setScheduleLogId(long scheduleLogId) {
        this.scheduleLogId = scheduleLogId;
    }

    public Date getScheduleTime() {
        if (scheduleTime == null) {
            return this.getTriggerTime();
        }
        return scheduleTime;
    }

    public void setScheduleTime(Date scheduleTime) {
        this.scheduleTime = scheduleTime;
    }

    public Date getTriggerTime() {
        return triggerTime;
    }

    public void setTriggerTime(Date triggerTime) {
        this.triggerTime = triggerTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusMessage() {
        return statusMessage;
    }

    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }
}
