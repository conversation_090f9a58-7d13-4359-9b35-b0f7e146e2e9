import {Button, Input, message, Space, Tooltip} from "antd";
import React, {useRef, useState} from "react";
import {ModalForm} from "@ant-design/pro-form";
import {DownloadOutlined} from "@ant-design/icons";
import {findUnSyncTables} from "@/services/Metadata/DataSourceTable/DataSourceTable";
import CommonTable from "@/components/CommonTable";
import {messageModal} from "@/utils/messageModal";
import CustomIcon from "@/components/CustomIcon";

export default ({datasourceId, setTableName}) => {
    const [selectedRows, setSelectRows] = useState([]);
    const [dataSource, setDataSource] = useState({data: [], pageSize: 10000, total: 0});
    const [allTables, setAllTables] = useState([]);  // 存储所有数据
    const [modalVisible, setModalVisible] = useState(false);
    const [loading, setLoading] = useState(false);  // 添加loading状态
    const formRef = useRef();
    const [searchValue, setSearchValue] = useState("");

    // 加载表数据(包括同步和未同步的表)
    const reloadTable = async () => {
        try {
            setLoading(true);  // 开始加载，设置loading为true
            const res = await findUnSyncTables(datasourceId);
            if (res) {
                const syncedTables = (res.syncTables || []).map(item => ({
                    name: item,
                    syncStatus: 'synced'
                }));

                const unSyncedTables = (res.unSyncTables || []).map(item => ({
                    name: item,
                    syncStatus: 'unsynced'
                }));

                // 合并所有表数据
                const allTableData = [...syncedTables, ...unSyncedTables];

                const updatedDataSource = {
                    data: allTableData,
                    pageSize: 10000,
                    total: allTableData.length
                };

                setDataSource(updatedDataSource);
                setAllTables(allTableData);

                if (allTableData.length === 0) {
                    message.info('没有获取到表数据');
                }
            } else {
                setDataSource({data: [], pageSize: 10000, total: 0});
                setAllTables([]);
                message.info('获取表数据失败');
            }
        } catch (error) {
            console.error('加载表数据出错:', error);
            message.error('加载表数据时发生错误');
            setDataSource({data: [], pageSize: 10000, total: 0});
            setAllTables([]);
        } finally {
            setLoading(false);  // 无论成功还是失败，都结束loading状态
        }
    };

    // 选择打开弹框
    const handleOpen = async () => {
        setDataSource({data: [], pageSize: 10000, total: 0}); // 清空数据源
        setSelectRows([]);
        setAllTables([]);
        setSearchValue("");  // 清空搜索值
        if (!datasourceId) {
            messageModal('warning', '请先选择数据源!');
            return;
        }
        setModalVisible(true);
        await reloadTable();  // 加载表数据
    };

    const handleRowSelectChange = (selectedRowKeys, selectedRows) => {
        setSelectRows(selectedRows);
    };

    // 查询函数
    const handleSearch = () => {
        setLoading(true);  // 搜索时也显示loading
        try {
            if (searchValue) {
                const filteredData = allTables.filter(item =>
                    item.name.toLowerCase().includes(searchValue.toLowerCase())
                );
                setDataSource({data: filteredData, pageSize: 10000, total: filteredData.length});  // 更新为过滤后的数据
            } else {
                // 如果搜索框为空，恢复所有表数据
                setDataSource({data: allTables, pageSize: 10000, total: allTables.length});
            }
        } catch (error) {
            console.error('搜索出错:', error);
        } finally {
            setLoading(false);  // 搜索完成后关闭loading
        }
    };

    // 搜索框回车事件处理
    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            handleSearch();
        }
    };

    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            align: 'center',
            ellipsis: {
                showTitle: false,
            }
        },
        {
            title: '状态',
            dataIndex: 'syncStatus',
            align: 'center',
            width: 100,
            render: (text) => (
                text === 'synced'
                    ? <span style={{ color: '#52c41a' }}>已同步</span>
                    : <span style={{ color: '#fa8c16' }}>未同步</span>
            )
        },
        {
            title: '描述',
            dataIndex: 'desc',
            align: 'center',
            ellipsis: {
                showTitle: false,
            }
        }
    ];

    return (
        <>
            <Space>
                <Tooltip title="获取数据源表,选择表可以置入表名称">
                    <div onClick={handleOpen} style={{
                        position: "absolute",
                        right: -25,
                        top: 0,
                        cursor:"pointer"
                    }}>
                        <CustomIcon type={"icon-shujukutongbu"} extraCommonProps={{
                            style: {
                                color: '#40a9ff',
                                fontSize: 30,
                            }
                        }}/>
                    </div>
                </Tooltip>
            </Space>
            <ModalForm
                visible={modalVisible}
                formRef={formRef}
                layout="horizontal"
                title="选择表名称"
                width={600}
                autoFocusFirstInput
                onVisibleChange={setModalVisible}
                modalProps={{
                    destroyOnClose: true,
                    onCancel: () => console.log('run'),
                }}
                onFinish={async () => {
                    try {
                        if (selectedRows && selectedRows.length) {
                            setTableName(selectedRows[0]?.name || "");
                            message.success('保存成功');
                            return true;
                        }
                        messageModal('warning', '请先选择一项!');
                        return false;
                    } catch (e) {
                        console.log(e);
                        message.error('保存失败');
                    }
                }}
            >
                <Space direction="vertical" size="middle" style={{display: 'flex'}}>
                    <Input.Group compact>
                        <Input
                            style={{width: '200px'}}
                            placeholder="请输入表名"
                            value={searchValue}
                            onChange={(e) => {
                                setSearchValue(e.target.value);
                            }}
                            onKeyPress={handleKeyPress}
                        />
                        <Button type="primary" onClick={handleSearch}>查询</Button>
                    </Input.Group>
                    <CommonTable
                        rowKey="name"
                        scrollY={300}
                        columns={columns}
                        data={dataSource}
                        selectType="radio"
                        selectedRows={selectedRows}
                        paginationShow={false}
                        onSelect={handleRowSelectChange}
                        tableLoading={loading}  // 添加tableLoading属性
                        emptyText="暂无表数据"
                    />
                </Space>
            </ModalForm>
        </>
    );
};
