{"content": "{\"jobInstance\":null,\"jobInfo\":null}", "methodName": "updateInstanceMetrics", "className": "com.xmcares.platform.hookbox.integrator.service.SeaTunnelJobEventService", "sync": false, "num": 1, "concurrent": true, "parameterTypes": ["com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance", "com.xmcares.platform.hookbox.common.job.seatunnel.SeaTunnelJobInfo"], "jarPaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\JetBrains\\IntelliJIdea2024.3\\plugins\\any-door-plugin\\lib\\any-door-core-2.2.1.jar", "C:\\Users\\<USER>\\AppData\\Roaming\\JetBrains\\IntelliJIdea2024.3\\plugins\\any-door-plugin\\lib\\any-door-all-dependence.jar", "C:\\Users\\<USER>\\AppData\\Roaming\\JetBrains\\IntelliJIdea2024.3\\plugins\\any-door-plugin\\lib\\any-door-common-2.2.1.jar"], "projectBasePath": "D:/codespace/ideaworkspace/2025-tech/xotp"}