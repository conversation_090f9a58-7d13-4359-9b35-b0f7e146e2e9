<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnyDoorSettingsState">
    <option name="pid" value="15052" />
    <option name="cache">
      <map>
        <entry key="com.xmcares.platform.admin.integrator.datasync.repository.DatasyncTaskRepository#listByIds#java.util.List" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;jobIds\&quot;:[1,2,3,13]}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.admin.integrator.datasync.repository.XxlJobSchedulerRepository#addScheduler#com.xmcares.platform.admin.integrator.datasync.dto.DatasyncDto,java.lang.String,com.xmcares.platform.admin.integrator.datasync.vo.DataxTempVo" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;saveDatasync\&quot;:null,\&quot;filePath\&quot;:null,\&quot;context\&quot;:null}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.admin.integrator.datasync.repository.XxlJobSchedulerRepository#addScheduler#com.xmcares.platform.admin.integrator.datasync.dto.DatasyncDto,java.lang.String,java.lang.String,com.xmcares.platform.admin.integrator.datasync.vo.DataxTempVo" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;saveDatasync\&quot;:1,\&quot;dataSyncTaskId\&quot;:2,\&quot;filePath\&quot;:3,\&quot;context\&quot;:4}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.admin.integrator.datasync.repository.XxlJobSchedulerRepository#datasyncLog#java.lang.String" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;jobInstanceId\&quot;:null}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.admin.integrator.datasync.repository.XxlJobSchedulerRepository#logDetailCat#long,int" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;logId\&quot;:1,\&quot;fromLineNum\&quot;:1}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.admin.integrator.datasync.repository.XxlJobSchedulerRepository#querySchedulerJobs#java.util.List" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;jobIds\&quot;:[1,2,3,4,5,6,7]}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.admin.integrator.datasync.repository.XxlJobSchedulerRepository#updateScheduler#java.lang.String,com.xmcares.platform.admin.integrator.datasync.model.Datasync" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;dispatchId\&quot;:1,\&quot;saveDatasync\&quot;:1}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.admin.integrator.datasync.service.DatasyncService#add#com.xmcares.platform.admin.integrator.datasync.vo.SaveDatasync" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;saveDatasync\&quot;:{\&quot;jobMode\&quot;:\&quot;STREAMING\&quot;,\&quot;fieldMappingJson\&quot;:\&quot;[{\\\&quot;sourceField\\\&quot;: \\\&quot;id\\\&quot;, \\\&quot;sinkField\\\&quot;: \\\&quot;id\\\&quot;},{\\\&quot;sourceField\\\&quot;:\\\&quot;epoch\\\&quot;,\\\&quot;sinkField\\\&quot;:\\\&quot;epoch\\\&quot;},{\\\&quot;sourceField\\\&quot;:\\\&quot;model_name\\\&quot;,\\\&quot;sinkField\\\&quot;:\\\&quot;model_name\\\&quot;}]\&quot;,\&quot;destDatasourceName\&quot;:\&quot;mysql_to测试\&quot;,\&quot;orginDatasourceName\&quot;:\&quot;rabbitmq测试\&quot;,\&quot;destBaseJson\&quot;:\&quot;{\\\&quot;username\\\&quot;:\\\&quot;test\\\&quot;,\\\&quot;writeType\\\&quot;:\\\&quot;rabbitmq\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;test\\\&quot;,\\\&quot;topic\\\&quot;:\\\&quot;orders_quere_2\\\&quot;,\\\&quot;format\\\&quot;:\\\&quot;json\\\&quot;,\\\&quot;autoAck\\\&quot;:\\\&quot;true\\\&quot;,\\\&quot;charset\\\&quot;:\\\&quot;utf-8\\\&quot;}\&quot;,\&quot;destAdvJson\&quot;:\&quot;{}\&quot;,\&quot;destHighJson\&quot;:\&quot;{}\&quot;,\&quot;orginBaseJson\&quot;:\&quot;{\\\&quot;username\\\&quot;:\\\&quot;test\\\&quot;,\\\&quot;readType\\\&quot;:\\\&quot;rabbitmq\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;test\\\&quot;,\\\&quot;topic\\\&quot;:\\\&quot;orders_quere\\\&quot;,\\\&quot;format\\\&quot;:\\\&quot;json\\\&quot;,\\\&quot;autoAck\\\&quot;:\\\&quot;true\\\&quot;,\\\&quot;charset\\\&quot;:\\\&quot;utf-8\\\&quot;}\&quot;,\&quot;orginAdvJson\&quot;:\&quot;{}\&quot;,\&quot;orginHighJson\&quot;:\&quot;{}\&quot;,\&quot;orginColumnJson\&quot;:\&quot;[{\\\&quot;name\\\&quot;:\\\&quot;test1\\\&quot;,\\\&quot;type\\\&quot;:\\\&quot;1\\\&quot;},{\\\&quot;name\\\&quot;:\\\&quot;test\\\&quot;,\\\&quot;type\\\&quot;:\\\&quot;2\\\&quot;}]\&quot;,\&quot;destColumnJson\&quot;:\&quot;[{\\\&quot;title\\\&quot;:\\\&quot;update_time\\\&quot;},{\\\&quot;title\\\&quot;:\\\&quot;model_name\\\&quot;},{\\\&quot;title\\\&quot;:\\\&quot;create_time\\\&quot;},{\\\&quot;title\\\&quot;:\\\&quot;epoch\\\&quot;},{\\\&quot;title\\\&quot;:\\\&quot;id\\\&quot;}]\&quot;,\&quot;childJobid\&quot;:\&quot;\&quot;,\&quot;orginType\&quot;:\&quot;0\&quot;,\&quot;orginDatasourceId\&quot;:\&quot;51605353270624256\&quot;,\&quot;destType\&quot;:\&quot;0\&quot;,\&quot;destDatasourceId\&quot;:\&quot;51605353270624256\&quot;,\&quot;intgName\&quot;:\&quot;Seatunnel-rabbitMQ测试用\&quot;,\&quot;schedulerExpr\&quot;:\&quot;0 0 0 * * ? *\&quot;,\&quot;routeStrategy\&quot;:\&quot;FIRST\&quot;,\&quot;blockStrategy\&quot;:\&quot;SERIAL_EXECUTION\&quot;,\&quot;executorFailRetryCount\&quot;:\&quot;3\&quot;,\&quot;executorTimeout\&quot;:\&quot;30\&quot;}}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.admin.integrator.datasync.web.DatasyncController#displayJson#java.lang.String" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;id\&quot;:62487992621940736}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.admin.integrator.datasync.web.DatasyncInstanceController#jobLogPageList#javax.servlet.http.HttpServletRequest,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;request\&quot;:null,\&quot;page\&quot;:1,\&quot;rows\&quot;:10,\&quot;jobGroup\&quot;:2,\&quot;jobId\&quot;:null,\&quot;logStatus\&quot;:-1,\&quot;filterTime\&quot;:\&quot;2025-05-20 00:00:00 - 2025-05-20 23:59:59\&quot;,\&quot;jobName\&quot;:\&quot;from-训练数据记录表-&gt;to-训练数据记录表\\n\&quot;}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.admin.integrator.datasync.web.DatasyncInstanceController#nextTriggerTime#java.lang.String,java.lang.String" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;scheduleType\&quot;:\&quot;cron\&quot;,\&quot;scheduleConf\&quot;:\&quot;0 0 0 * * ? *\&quot;}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.hookbox.common.job.seatunnel.RemoteSeaTunnelJobContextService#getJobExecutionLog#com.xmcares.platform.hookbox.common.job.JobContext" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;jobContext\&quot;:{\&quot;jobInstanceId\&quot;:\&quot;62465417153208320\&quot;}}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.hookbox.integrator.handler.XxlJobDatasyncLogHandler#getLogsByInstanceId#java.lang.String" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;jobInstanceId\&quot;:\&quot;62474384575160320\&quot;}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.hookbox.integrator.handler.XxlJobDatasyncLogHandler#pageQuery#java.lang.Integer,java.lang.Integer,com.xmcares.platform.hookbox.integrator.model.XxlJobLogQueryDTO" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;current\&quot;:1,\&quot;size\&quot;:20,\&quot;queryDTO\&quot;:{\&quot;jobId\&quot;:63,\&quot;jobGroup\&quot;:2}}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.hookbox.integrator.manager.DatasyncJobManager#startJob#com.xmcares.platform.hookbox.common.job.JobParams" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;jobParams\&quot;:{\&quot;jobId\&quot;:\&quot;61824691209846784\&quot;}}&quot;,&quot;concurrent&quot;:true}" />
        <entry key="com.xmcares.platform.hookbox.integrator.service.SeaTunnelJobEventService#updateInstanceMetrics#com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance,com.xmcares.platform.hookbox.common.job.seatunnel.SeaTunnelJobInfo" value="{&quot;runNum&quot;:1,&quot;content&quot;:&quot;{\&quot;jobInstance\&quot;:null,\&quot;jobInfo\&quot;:null}&quot;,&quot;concurrent&quot;:true}" />
      </map>
    </option>
    <option name="dataFileDir" value="\.idea\any-door-data\" />
  </component>
</project>