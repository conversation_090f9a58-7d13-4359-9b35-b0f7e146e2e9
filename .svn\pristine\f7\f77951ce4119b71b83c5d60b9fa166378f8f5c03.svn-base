<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmcares.platform.hookbox.integrator.mapper.DatasyncJobInstanceMapper">
  <resultMap id="BaseResultMap" type="com.xmcares.platform.hookbox.integrator.model.DatasyncInstance">
    <!--@mbg.generated-->
    <!--@Table bdp_intg_datasync_instance-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="datasync_id" jdbcType="VARCHAR" property="datasyncId" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="publish_user" jdbcType="VARCHAR" property="publishUser" />
    <result column="instance_name" jdbcType="VARCHAR" property="instanceName" />
    <result column="instance_code" jdbcType="VARCHAR" property="instanceCode" />
    <result column="template_context" jdbcType="LONGVARCHAR" property="templateContext" />
    <result column="template_code" jdbcType="VARCHAR" property="templateCode" />
    <result column="orgin_type" jdbcType="CHAR" property="orginType" />
    <result column="orgin_datasource_name" jdbcType="VARCHAR" property="orginDatasourceName" />
    <result column="orgin_plugin_path" jdbcType="VARCHAR" property="orginPluginPath" />
    <result column="dest_type" jdbcType="CHAR" property="destType" />
    <result column="dest_datasource_name" jdbcType="VARCHAR" property="destDatasourceName" />
    <result column="dest_plugin_path" jdbcType="VARCHAR" property="destPluginPath" />
    <result column="dispatch_id" jdbcType="VARCHAR" property="dispatchId" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="has_delete" jdbcType="CHAR" property="hasDelete" />
    <result column="job_group" jdbcType="INTEGER" property="jobGroup" />
    <result column="schedule_type" jdbcType="VARCHAR" property="scheduleType" />
    <result column="schedule_conf" jdbcType="VARCHAR" property="scheduleConf" />
    <result column="misfire_strategy" jdbcType="VARCHAR" property="misfireStrategy" />
    <result column="executor_route_strategy" jdbcType="VARCHAR" property="executorRouteStrategy" />
    <result column="executor_handler" jdbcType="VARCHAR" property="executorHandler" />
    <result column="executor_param" jdbcType="VARCHAR" property="executorParam" />
    <result column="executor_block_strategy" jdbcType="VARCHAR" property="executorBlockStrategy" />
    <result column="executor_timeout" jdbcType="INTEGER" property="executorTimeout" />
    <result column="executor_fail_retry_count" jdbcType="INTEGER" property="executorFailRetryCount" />
    <result column="child_jobid" jdbcType="VARCHAR" property="childJobid" />
    <result column="trigger_status" jdbcType="TINYINT" property="triggerStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, datasync_id, publish_time, update_time, publish_user, instance_name, instance_code,
    template_context, template_code, orgin_type, orgin_datasource_name, orgin_plugin_path,
    dest_type, dest_datasource_name, dest_plugin_path, dispatch_id, file_path, has_delete,
    job_group, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy,
    executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count,
    child_jobid, trigger_status
  </sql>
</mapper>
