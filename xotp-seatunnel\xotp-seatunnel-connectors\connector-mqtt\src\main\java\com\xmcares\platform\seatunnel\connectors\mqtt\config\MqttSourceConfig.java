/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.config;

import com.xmcares.platform.seatunnel.connectors.mqtt.exception.MqttConnectorErrorCode;
import com.xmcares.platform.seatunnel.connectors.mqtt.exception.MqttConnectorException;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.CatalogTableUtil;


import java.io.Serializable;
import java.util.List;

import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.BATCH_INACTIVITY_TIMEOUT;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.AUTO_RECONNECT;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.BROKER_URLS;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.CLEAN_SESSION;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.CLIENT_ID;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.CONNECTION_TIMEOUT;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.KEEP_ALIVE_INTERVAL;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.MAX_RECONNECT_DELAY;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.MESSAGE_FORMAT;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.PASSWORD;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.POLL_TIMEOUT;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.QOS;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.SSL_ENABLED;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.TOPICS;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.USERNAME;

public class MqttSourceConfig implements Serializable, MqttConnectionConfig {

    private static final long serialVersionUID = 1L;

    // MQTT连接配置
    private List<String> brokerUrls;
    private String clientId;
    private String username;
    private String password;
    private int connectionTimeout;
    private int keepAliveInterval;
    private boolean cleanSession;
    private boolean sslEnabled;
    private boolean autoReconnect;
    private int maxReconnectDelay;

    // Source特有配置
    private List<String> topics;
    private int qos;
    private String messageFormat;
    private int pollTimeout;
    private long batchInactivityTimeout;
    // Catalog配置
    private CatalogTable catalogTable;

    public long getBatchInactivityTimeout() {
        return batchInactivityTimeout;
    }

    public void setBatchInactivityTimeout(long batchInactivityTimeout) {
        this.batchInactivityTimeout = batchInactivityTimeout;
    }

    public static MqttSourceConfig buildConfig(ReadonlyConfig config) {
        try {
            // 直接实例化配置对象
            MqttSourceConfig sourceConfig = new MqttSourceConfig();

            // 构建 CatalogTable
            CatalogTable catalogTable = CatalogTableUtil.buildWithConfig(config);

            // 设置 MQTT 连接配置
            sourceConfig.setBrokerUrls(config.get(BROKER_URLS));
            sourceConfig.setClientId(config.getOptional(CLIENT_ID).orElse(null));
            sourceConfig.setUsername(config.getOptional(USERNAME).orElse(null));
            sourceConfig.setPassword(config.getOptional(PASSWORD).orElse(null));
            sourceConfig.setConnectionTimeout(config.get(CONNECTION_TIMEOUT));
            sourceConfig.setKeepAliveInterval(config.get(KEEP_ALIVE_INTERVAL));
            sourceConfig.setCleanSession(config.get(CLEAN_SESSION));
            sourceConfig.setSslEnabled(config.get(SSL_ENABLED));
            sourceConfig.setAutoReconnect(config.get(AUTO_RECONNECT));
            sourceConfig.setMaxReconnectDelay(config.get(MAX_RECONNECT_DELAY));

            // 设置 Source 特有配置
            sourceConfig.setTopics(config.get(TOPICS));
            sourceConfig.setQos(config.get(QOS));
            sourceConfig.setMessageFormat(config.get(MESSAGE_FORMAT));
            sourceConfig.setPollTimeout(config.get(POLL_TIMEOUT));
            sourceConfig.setBatchInactivityTimeout(config.get(BATCH_INACTIVITY_TIMEOUT));

            // 设置 Catalog 配置
            sourceConfig.setCatalogTable(catalogTable);

            return sourceConfig;

        } catch (Exception e) {
            // 保持原有的异常处理逻辑
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.INVALID_CONFIGURATION,
                    "Failed to build MQTT source config: " + e.getMessage(),
                    e);
        }
    }

    public void validate() {
        if (brokerUrls == null || brokerUrls.isEmpty()) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.INVALID_CONFIGURATION, "Broker URLs cannot be empty");
        }

        if (topics == null || topics.isEmpty()) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.INVALID_CONFIGURATION, "Topics cannot be empty");
        }

        if (qos < 0 || qos > 2) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.INVALID_CONFIGURATION, "QoS must be 0, 1, or 2");
        }

        if (username != null && password == null) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.INVALID_CONFIGURATION,
                    "Password is required when username is provided");
        }
    }

    @Override
    public List<String> getBrokerUrls() {
        return brokerUrls;
    }

    public void setBrokerUrls(List<String> brokerUrls) {
        this.brokerUrls = brokerUrls;
    }

    @Override
    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    @Override
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }

    @Override
    public int getKeepAliveInterval() {
        return keepAliveInterval;
    }

    public void setKeepAliveInterval(int keepAliveInterval) {
        this.keepAliveInterval = keepAliveInterval;
    }

    @Override
    public boolean isCleanSession() {
        return cleanSession;
    }

    public void setCleanSession(boolean cleanSession) {
        this.cleanSession = cleanSession;
    }

    @Override
    public boolean isSslEnabled() {
        return sslEnabled;
    }

    public void setSslEnabled(boolean sslEnabled) {
        this.sslEnabled = sslEnabled;
    }

    @Override
    public boolean isAutoReconnect() {
        return autoReconnect;
    }

    public void setAutoReconnect(boolean autoReconnect) {
        this.autoReconnect = autoReconnect;
    }

    @Override
    public int getMaxReconnectDelay() {
        return maxReconnectDelay;
    }

    public void setMaxReconnectDelay(int maxReconnectDelay) {
        this.maxReconnectDelay = maxReconnectDelay;
    }

    public List<String> getTopics() {
        return topics;
    }

    public void setTopics(List<String> topics) {
        this.topics = topics;
    }

    public int getQos() {
        return qos;
    }

    public void setQos(int qos) {
        this.qos = qos;
    }

    public String getMessageFormat() {
        return messageFormat;
    }

    public void setMessageFormat(String messageFormat) {
        this.messageFormat = messageFormat;
    }

    public int getPollTimeout() {
        return pollTimeout;
    }

    public void setPollTimeout(int pollTimeout) {
        this.pollTimeout = pollTimeout;
    }

    public CatalogTable getCatalogTable() {
        return catalogTable;
    }

    public void setCatalogTable(CatalogTable catalogTable) {
        this.catalogTable = catalogTable;
    }

    public MqttSourceConfig() {
    }

    public MqttSourceConfig(List<String> brokerUrls, String clientId, String username, String password, int connectionTimeout, int keepAliveInterval, boolean cleanSession, boolean sslEnabled, boolean autoReconnect, int maxReconnectDelay, List<String> topics, int qos, String messageFormat, int pollTimeout, long batchInactivityTimeout, CatalogTable catalogTable) {
        this.brokerUrls = brokerUrls;
        this.clientId = clientId;
        this.username = username;
        this.password = password;
        this.connectionTimeout = connectionTimeout;
        this.keepAliveInterval = keepAliveInterval;
        this.cleanSession = cleanSession;
        this.sslEnabled = sslEnabled;
        this.autoReconnect = autoReconnect;
        this.maxReconnectDelay = maxReconnectDelay;
        this.topics = topics;
        this.qos = qos;
        this.messageFormat = messageFormat;
        this.pollTimeout = pollTimeout;
        this.batchInactivityTimeout = batchInactivityTimeout;
        this.catalogTable = catalogTable;
    }
}

