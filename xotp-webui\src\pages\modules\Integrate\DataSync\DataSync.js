import React, {PureComponent, forwardRef} from 'react';
import {connect} from 'dva';
import {CopyOutlined, DeleteOutlined, EditOutlined, PlusCircleOutlined, SyncOutlined} from '@ant-design/icons';
import {Form} from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import AutoModal from './components/TestAutoModal'
import {
    Button,
    Card,
    Col,
    Input,
    Modal,
    Row,
    Tooltip,
    Tree,
    Menu,
    Dropdown,
    Table, Space, Select, DatePicker,
} from 'antd';
import CommonTable from '@/components/CommonTable/index';
import PageHeaderWrapper from '@/components/PageHeaderWrapper/index';
import styles from './DataSync.less';
import * as utils from '@/utils/utils';
import {messageModal} from '@/utils/messageModal';
import {DownOutlined} from '@ant-design/icons';
import {
    datasyncDelete,
    getDatasync,
    getNextRunTimer,
    beginRun,
    stopRun,
    runOnce,
    readerJson
} from '@/services/Integrate/DataSync/DataSync';
import router from 'umi/router';
import PublishForm from "@/pages/modules/Integrate/DataSync/components/PublishForm";
import * as dataSyncTask from '@/services/Integrate/DataSync/DatasyncTask';
import UpdateDatasyncTaskForm from "@/pages/modules/Integrate/DataSync/components/UpdateDatasyncTaskForm";
import ModeSelectionModal from "@/pages/modules/Integrate/DataSync/components/ModeSelectionModal";
import moment from "moment/moment";


const {TextArea} = Input;
let markPage = 1, pageSize = 10;
const TreeNode = Tree.TreeNode;
const confirm = Modal.confirm;
const modelsName = 'dataSync';
const FormItem = Form.Item;
const {RangePicker} = DatePicker;
const status = [
    {code: 0, name: "未启动"},
    {code: 1, name: "运行中"},
];

const runSchema = [
    {code: "0", name: "永久运行"},
    {code: "1", name: "手动运行"},
];

const routeStrategy = [
    {code: "FIRST", name: "第一个"},
    {code: "LAST", name: "最后一个"},
    {code: "RANDOM", name: "随机"},
];

const jobModeList = [
    {code: 'BATCH', name: "批模式"},
    {code: 'STREAMING', name: "流模式"},
];
const ForAutoModal = forwardRef(AutoModal)

@connect(({dataSync, tenantManager, deptManager, role, userRole, menu, loading}) => ({
    dataSync,
    tenantManager,
    deptManager,
    role,
    userRole,
    menu,
    loading: loading.models.dataSync,
}))
@Form.create()

export default class DataSync extends PureComponent {
    state = {
        openChildrenRows: [],
        childrenDatasource: {},
        selectedRows: [],
        defaultValue: [],
        modalVisible: false,
        modalTitle: '',
        selectedRowKeys: undefined,
        currentInstance: [],
        modeModalVisible: false,
        jobMode: "BATCH"
    };


    queryList = () => {
        const {dispatch} = this.props;
        dispatch({
            type: `${modelsName}/fetch`,
            callback: () => {
                this.setState({
                    selectedRows: [],
                    childrenDatasource: {},
                });
            },
        });
    };


    /**
     * 用户列表(第一次渲染后调用)
     */
    componentDidMount() {
        markPage = 1;
        this.queryList();
    }

    deleteDataSource = async (record) => {
        const topThis = this;
        // 需要校验是否有实例
        const instanceRes = await dataSyncTask.query(record.id);
        debugger
        if (instanceRes && instanceRes.length > 0) {
            messageModal("warning", "已发布实例，不允许删除！")
            return;
        }
        confirm({
            title: '确定删除?',
            content: '确认删除"' + record.intgName + '"的数据集成',
            okText: '确定',
            cancelText: '取消',
            async onOk() {
                const res = await datasyncDelete(record.id);
                if (res) {
                    const data = {
                        topThis,
                        path: `${modelsName}/fetch`,
                    };
                    utils.search(data);
                }
            },
        });
    };

    deleteDatasyncTask = (record) => {
        console.log("删除")
        const topThis = this;
        confirm({
            title: '确定删除?',
            content: '确认删除"' + record.instanceName + '"的实例',
            okText: '确定',
            cancelText: '取消',
            async onOk() {
                const res = await dataSyncTask.remove(record.id);
                if (res) {
                    topThis.openExpanded(false, true, {
                        id: record.datasyncId
                    });
                }
            },
        });
    };

    onChange = checkedValues => {
        this.setState({
            defaultValue: checkedValues,
        });
    };

    /**
     * 打开新增或者修改或者详情的框
     */
    openAddOrUpdateOrDetailModal = async (type, record) => {
        if (type == 'add') {
            this.formRef.handleMinModalVisible(true, '新增', 0);
        }
        if (type == 'update') {
            const res = await getDatasync(record.id);
            this.formRef?.handleMinModalVisible(true, '修改', 1, res);
        }
    };

    submitForm = (data, mark) => {
        const {dispatch} = this.props;
        const {selectedRowKeys} = this.state;
        if (mark == 0) {
            dispatch({
                type: `${modelsName}/create`,
                payload: {
                    ...data,
                },
                callback: (res) => {
                    if (res.code == 0) {
                        this.formRef.handleMinModalVisible(false);
                        messageModal('success', res.message || '新增成功');
                        this.reloadData();
                    } else {
                        messageModal('error', res.message || '新增失败');
                    }
                }
            });
        }

        if (mark == 1) {
            dispatch({
                type: `${modelsName}/update`,
                payload: {
                    id: selectedRowKeys[0],
                    ...data,
                },
                callback: (res) => {
                    if (res.code == 0) {
                        this.formRef.handleMinModalVisible(false);
                        messageModal('success', '编辑成功');
                        this.reloadData();
                    } else {
                        messageModal('error', res.message || '编辑失败');
                    }
                }
            });
        }
    }

    /**
     * 发布流程3. 系统提交表单数据
     * @param data 需要提交的数据
     * @param callback 提交后的操作
     */
    submitPublishForm = (data, callback) => {
        const {dispatch} = this.props;
        dispatch({
            type: `${modelsName}/publish`,
            payload: {
                ...data,
            },
            callback: {
                success: () => {
                    if (callback.success) {
                        callback.success();
                    }
                    // 发布流程4. 显示发布的信息
                    this.openExpanded(false, true, {
                        id: data.id
                    });
                },
                fault: () => {
                    if (callback.fault) {
                        callback.fault();
                    }
                },
                end: () => {
                    if (callback.end) {
                        callback.end();
                    }
                }
            }
        });
    }

    /**
     * 同步任务更新流程3. 系统提交表单数据
     * @param data 需要提交的数据
     * @param callback 提交后的操作
     */
    submitUpdateDatasyncTaskForm = (data, callback) => {
        dataSyncTask.update(data).then((result) => {
            if (result === true) {
                messageModal("success", "更新成功")
                if (callback.success) {
                    callback.success();
                }
                // 同步任务更新流程4. 更新发布的信息
                this.openExpanded(false, true, {
                    id: data.datasyncId
                });
            } else {
                messageModal("error", "更新失败")
            }
        }).finally(() => {
            if (callback.end) {
                callback.end();
            }
        })
    }

    renderTreeNodes = data => {
        if (!!data && data.length > 0) {
            return data.map(item => {
                if (utils.isNull(item.children)) {
                    item.children = [];
                }
                if (item.children) {
                    return (
                        <TreeNode title={item.text} key={item.id} dataRef={item}>
                            {this.renderTreeNodes(item.children)}
                        </TreeNode>
                    );
                }
                return <TreeNode {...item} />;
            });
        }
    };

    handleModalVisible = flag => {
        this.setState({
            modalVisible: flag,
        });
    };

    /**
     * CommonTable 分页 排序 触发事件
     * @param pagination 分页
     * @param filtersArg
     * @param sorter 排序
     */
    handleCommonTableChange = (pagination, filtersArg, sorter) => {
        const {form} = this.props;
        markPage = pagination.current;
        pageSize = pagination.pageSize;
        form.validateFields((err, fieldsValue) => {
            let data = {
                page: pagination.current,
                rows: pagination.pageSize,
                ...fieldsValue
            };
            const {dispatch} = this.props;
            dispatch({
                payload: data,
                type: `${modelsName}/fetch`,
                callback: () => {
                    this.setState({
                        selectedRows: [],
                    });
                },
            });
        });
    };

    reloadData = () => {
        const {form, dispatch} = this.props;
        form.validateFields((err, values) => {
            let ranTimer;
            for (let key in values) {
                if (values[key] === '') {
                    delete values[key]
                } else if (key === 'createTime') {
                    ranTimer = values[key];
                    delete values[key];
                }
            }
            console.log(values)
            if (!err) {
                markPage = 1;

                // 准备基本参数
                const params = {
                    page: markPage,
                    rows: pageSize,
                    ...values,
                };

                // 只有当ranTimer是数组且长度为2时才添加日期范围参数
                if (ranTimer && Array.isArray(ranTimer) && ranTimer.length === 2) {
                    // 确保两个日期对象都有效并且有format方法
                    if (ranTimer[0] && typeof ranTimer[0].format === 'function') {
                        params.createTimeStart = ranTimer[0].format('YYYY-MM-DD HH:mm:ss');
                    }
                    if (ranTimer[1] && typeof ranTimer[1].format === 'function') {
                        params.createTimeEnd = ranTimer[1].format('YYYY-MM-DD HH:mm:ss');
                    }
                }

                dispatch({
                    type: `${modelsName}/fetch`,
                    payload: params,
                    callback: () => {
                        this.setState({
                            selectedRows: [],
                            // childrenDatasource: {},
                            openChildrenRows: []
                        });
                    },
                });
            }
        });
    }

    /**
     * 查询
     */
    handleSearch = (e) => {
        if (e) e.preventDefault();
        this.reloadData();
    };

    /**
     * 重置
     */
    handleFormReset = () => {
        const data = {
            topThis: this,
        };
        utils.reset(data);
    };

    getMapValue = (key, items) => {
        for (let item of items) {
            if (key === item.code) {
                return item.name;
            }
        }
    }

    handleMenuClick = (e, menus, record) => {
        const {key} = e;
        menus.forEach(menuObj => {
            if (menuObj.key === key && menuObj.target) {
                menuObj.target(e, record);
            }
        });
    }

    selectMenus = (record, menus, selectMenusKey) => {
        return (
            <Menu onClick={e => {
                this.handleMenuClick(e, menus, record)
            }}>
                {
                    selectMenusKey.map(menuKey => {
                        return menus.map(menuObj => {
                            if (menuObj.key === menuKey) {
                                if (typeof menuObj.html === 'function') {
                                    return menuObj.html(record);
                                } else {
                                    return menuObj.html;
                                }
                            }
                        })
                    })
                }
            </Menu>
        )
    }

    menus = [
        {
            key: 'runOne',
            html: (
                <Menu.Item key="runOne">运行一次</Menu.Item>
            ),
            target: (e, record) => {
                confirm({
                    title: '确认框?',
                    content: '是否确定需要运行一次',
                    okText: '确定',
                    cancelText: '取消',
                    async onOk() {
                        const res = await dataSyncTask.runOnce(record.id);
                        if (res === true) {
                            messageModal("success", "运行成功");
                        }
                    },
                });
            }
        },
        {
            key: 'beginRun',
            html: (
                <Menu.Item key="beginRun">开始运行</Menu.Item>
            ),
            target: (e, record) => {
                const topThis = this;
                confirm({
                    title: '确认框?',
                    content: '确认开始运行',
                    okText: '确定',
                    cancelText: '取消',
                    async onOk() {
                        const res = await dataSyncTask.beginRun(record.id);
                        if (res === true) {
                            topThis.openExpanded(false, true, {
                                id: record.datasyncId
                            });
                        }
                    },
                });
            }
        },
        {
            key: 'stopRun',
            html: (
                <Menu.Item key="stopRun">停止运行</Menu.Item>
            ),
            target: (e, record) => {
                const topThis = this;
                confirm({
                    title: '确认框?',
                    content: '确认停止运行',
                    okText: '确定',
                    cancelText: '取消',
                    async onOk() {
                        const res = await dataSyncTask.stopRun(record.id);
                        if (res === true) {
                            topThis.openExpanded(false, true, {
                                id: record.datasyncId
                            });
                        }
                    },
                });
            }
        },
        {
            key: 'runLog',
            html: (
                <Menu.Item key="runLog">运行记录</Menu.Item>
            ),
            target: (e, record) => {
                console.log(record);
                router.push({
                    pathname: '/integrate/dataSyncLog',
                    query: {
                        jobGroup: record?.jobGroup,
                        instanceName: record?.instanceName,
                        instanceId: record?.dispatchId
                    },
                });
            }
        },
        {
            key: 'nextRunTimer',
            html: (
                <Menu.Item key="nextRunTimer">下次调度时间</Menu.Item>
            ),
            target: (e, record) => {
                this.getNextRunTime(e, record)
            }
        },
        {
            key: 'openDatasyncEdit',
            html: (
                <Menu.Item key="openDatasyncEdit">编辑</Menu.Item>
            ),
            target: (e, record) => {
                this.openAddOrUpdateOrDetailModal('update', record);
            }
        },
        {
            key: 'openDatasyncTaskEdit',
            html: (
                <Menu.Item key="openDatasyncTaskEdit">编辑</Menu.Item>
            ),
            target: (e, record) => {
                this.updateDatasyncTaskForm.openModal(record);
            }
        },
        {
            key: 'deleteDatasync',
            html: (
                <Menu.Item key="deleteDatasync">删除</Menu.Item>
            ),
            target: (e, record) => {
                this.deleteDataSource(record);
            }
        },
        {
            key: 'deleteDatasyncTask',
            html: (
                <Menu.Item key="deleteDatasyncTask">删除</Menu.Item>
            ),
            target: (e, record) => {
                this.deleteDatasyncTask(record);
            }
        },
        {
            key: 'showDatasyncJson',
            html: (
                <Menu.Item key="showDatasyncJson">查看Json</Menu.Item>
            ),
            target: (e, record) => {
                readerJson(record.id).then((res) => {
                    const body = JSON.stringify(res, null, 2);
                    console.log(res);
                    Modal.success({
                        centered: true,
                        title: "原始文件内容",
                        width: 800,
                        content: (
                            <TextArea autoSize={true} value={body}/>
                        ),
                        okButtonProps: {style: {display: 'none'}},
                        maskClosable: true,
                        mask: false,
                    });
                });
            }
        },
        {
            key: 'showDatasyncTaskJson',
            html: (
                <Menu.Item key="showDatasyncTaskJson">查看Json</Menu.Item>
            ),
            target: (e, record) => {
                dataSyncTask.readerJson(record.id).then((res) => {
                    const body = JSON.stringify(res, null, 2);
                    console.log(res);
                    Modal.success({
                        centered: true,
                        title: "原始文件内容",
                        width: 800,
                        content: (
                            <TextArea autoSize={true} value={body}/>
                        ),
                        okButtonProps: {style: {display: 'none'}},
                        maskClosable: true,
                        mask: false,
                    });
                });
            }
        },
        {
            key: 'divider',
            html: (
                <Menu.Divider/>
            )
        },
        {
            key: 'publish',
            html: (
                <Menu.Item key="publish">发布</Menu.Item>
            ),
            target: (e, record) => {
                // 发布流程 1. 打开发布界面
                this.publishForm.openModal(record);
            }
        }
    ];

    checkRunningInstance = () => {
        const {currentInstance} = this.state;
        // 使用 Array.some() 方法检查是否存在 triggerStatus 为 1 的实例
        const isRunning = currentInstance.some(instance => instance.triggerStatus === 1);

        if (isRunning) {
            // 提示用户
            messageModal("warning", "只能运行一个实例，当前已有一个实例在运行");
            return true; // 返回 true 表示存在正在运行的实例
        }

        return false; // 返回 false 表示没有正在运行的实例
    };

    columns = [
        {
            title: '数据同步名称',
            dataIndex: 'intgName',
            render: (text) => {
                return (
                    <Tooltip title={text}>
                        <div className={styles.resultColumnsDiv}>{text}</div>
                    </Tooltip>
                );
            },
        },
        {
            title: '处理模式',
            dataIndex: 'jobMode',
            width: '10%',
            render: (text) => {
                 // 使用对象映射替代switch语句
                const modeMap = {
                    BATCH: "批模式",
                    STREAMING: "流模式",
                };

                // 获取显示文本，如果没有匹配则显示"未知模式"
                const displayText = modeMap[text] || "未知模式";

                // 统一的渲染模式
                return (
                    <Tooltip title={displayText}>
                        <div className={styles.resultColumnsDiv}>{displayText}</div>
                    </Tooltip>
                );
            },
        },
        {
            title: '数据来源',
            dataIndex: 'orginDatasourceName',
            width: '10%'
        },
        {
            title: '数据去向',
            dataIndex: 'destDatasourceName',
            width: '10%'
        },
        {
            title: '调度规则',
            dataIndex: 'schedulerExpr',
            width: '10%',
            render: (text) => {
                return (
                    <Tooltip title={text}>
                        <div className={styles.resultColumnsDiv}>{text}</div>
                    </Tooltip>
                );
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            width: '12%',
            render: (text) => {
                return (
                    <Tooltip title={text}>
                        <div className={styles.resultColumnsDiv}>{text}</div>
                    </Tooltip>
                );
            },
        },
        {
            title: '更新时间',
            dataIndex: 'updateTime',
            width: '12%',
            render: (text) => {
                return (
                    <Tooltip title={text}>
                        <div className={styles.resultColumnsDiv}>{text}</div>
                    </Tooltip>
                );
            },
        },
        {
            title: '维护人',
            dataIndex: 'createUser',
            width: '8%',
            render: (text) => {
                return (
                    <Tooltip title={text}>
                        <div className={styles.resultColumnsDiv}>{text}</div>
                    </Tooltip>
                );
            },
        },
        {
            title: '操作',
            dataIndex: 'options',
            width: '200px',
            render: (text, record) => {

                const menu = (
                    <Menu>
                        <Space direction="vertical">
                            <Button type="text" onClick={() => {
                                this.deleteDataSource(record);
                            }}>
                                <span>删除</span>
                            </Button>
                            <Button type="text" onClick={() => {
                                readerJson(record.id).then((res) => {
                                    const body = JSON.stringify(res, null, 2);
                                    console.log(res);
                                    Modal.success({
                                        centered: true,
                                        title: "原始文件内容",
                                        width: 800,
                                        content: (
                                            <TextArea autoSize={true} value={body}/>
                                        ),
                                        okButtonProps: {style: {display: 'none'}},
                                        maskClosable: true,
                                        mask: false,
                                    });
                                });
                            }}>
                                <span>查看Json</span>
                            </Button>
                        </Space>
                    </Menu>
                );
                const operation = (
                    <Space>
                        <Button size="small" type="primary" onClick={() => {
                            this.publishForm.openModal(record);
                        }}>发布</Button>
                        <Button size="small" type="primary" onClick={() => {
                            this.openAddOrUpdateOrDetailModal('update', record);
                        }}>编辑</Button>
                        <Dropdown overlay={menu} trigger={['click']}>
                            <Button size="small">
                                <Space>
                                    更多
                                    <DownOutlined/>
                                </Space>
                            </Button>
                        </Dropdown>
                    </Space>
                );
                return (
                    <Tooltip>
                        <div>{operation}</div>
                    </Tooltip>
                );
            },
        },
    ];

    childrenColumns = [
        // {
        //     title: '实例编号',
        //     dataIndex: 'instanceCode',
        //     width: '10%',
        //     render: (text) => {
        //         return (
        //             <Tooltip title={text}>
        //                 <div className={styles.resultColumnsDiv}>{text}</div>
        //             </Tooltip>
        //         );
        //     },
        // },
        {
            title: '实例名称',
            dataIndex: 'instanceName',
            width: '20%',
            render: (text) => {
                return (
                    <Tooltip title={text}>
                        <div className={styles.resultColumnsDiv}>{text}</div>
                    </Tooltip>
                );
            },
        },
        {
            title: '发布时间',
            dataIndex: 'publishTime',
            width: '15%',
            render: (text) => {
                return (
                    <Tooltip title={text}>
                        <div className={styles.resultColumnsDiv}>{text}</div>
                    </Tooltip>
                );
            },
        },
        {
            title: '更新时间',
            dataIndex: 'updateTime',
            width: '15%',
            render: (text) => {
                return (
                    <Tooltip title={text}>
                        <div className={styles.resultColumnsDiv}>{text}</div>
                    </Tooltip>
                );
            },
        },
        {
            title: '发布人',
            dataIndex: 'publishUser',
            width: '10%',
            render: (text) => {
                return (
                    <Tooltip title={text}>
                        <div className={styles.resultColumnsDiv}>{text}</div>
                    </Tooltip>
                );
            },
        },
        {
            title: '调度规则',
            dataIndex: 'schedulerExpr',
            width: '10%',
            render: (text) => {
                return (
                    <Tooltip title={text}>
                        <div className={styles.resultColumnsDiv}>{text}</div>
                    </Tooltip>
                );
            },
        },
        {
            title: '调度状态',
            dataIndex: 'triggerStatus',
            width: '10%',
            render: (text) => {
                let name = this.getMapValue(text, status);
                return (
                    <Tooltip title={name === null ? text : name}>
                        <div className={styles.resultColumnsDiv}>{name === null ? text : name}</div>
                    </Tooltip>
                );
            },
        },
        {
            title: '操作',
            dataIndex: 'options',
            width: '200px',
            render: (text, record) => {
                if (record.triggerStatus === 0) {
                    const menu = (
                        <Menu>
                            <Space direction="vertical">
                                <Button type="text" onClick={() => {
                                    const topThis = this;
                                    const hasRunningInstance = topThis.checkRunningInstance();

                                    if (hasRunningInstance) {
                                        return; // 直接返回，避免后续代码执行
                                    }
                                    confirm({
                                        title: '确认框?',
                                        content: '确认开始运行',
                                        okText: '确定',
                                        cancelText: '取消',
                                        async onOk() {
                                            const res = await dataSyncTask.beginRun(record.id);
                                            if (res === true) {
                                                topThis.openExpanded(false, true, {
                                                    id: record.datasyncId
                                                });
                                            }
                                        },
                                    });
                                }}>
                                    <span>开始运行</span>
                                </Button>

                                <Button type="text" onClick={(e) => this.getNextRunTime(e, record)}>
                                    <span>下次调度时间</span>
                                </Button>
                                <Button type="text" onClick={() => {
                                    dataSyncTask.readerJson(record.id).then((res) => {
                                        const body = JSON.stringify(res, null, 2);
                                        console.log(res);
                                        Modal.success({
                                            centered: true,
                                            title: "原始文件内容",
                                            width: 800,
                                            content: (
                                                <TextArea autoSize={true} value={body}/>
                                            ),
                                            okButtonProps: {style: {display: 'none'}},
                                            maskClosable: true,
                                            mask: false,
                                        });
                                    });
                                }}>
                                    <span>查看Json</span>
                                </Button>
                                <Button type="text" onClick={() => {
                                    this.updateDatasyncTaskForm.openModal(record);
                                }}>
                                    <span>编辑</span>
                                </Button>
                                <Button type="text" onClick={() => {
                                    this.deleteDatasyncTask(record);
                                }}>
                                    <span>删除</span>
                                </Button>
                            </Space>
                        </Menu>
                    );
                    const operation = (
                        <Space>
                            {record.schedulerExpr !== "STREAMING" && <Button size="small" onClick={() => {
                                confirm({
                                    title: '确认框?',
                                    content: '是否确定需要运行一次',
                                    okText: '确定',
                                    cancelText: '取消',
                                    async onOk() {
                                        const res = await dataSyncTask.runOnce(record.id);
                                        if (res === true) {
                                            messageModal("success", "运行成功");
                                        }
                                    },
                                });
                            }}>运行一次</Button>}
                            <Button size="small" onClick={() => {
                                const topThis = this;
                                const hasRunningInstance = topThis.checkRunningInstance();

                                if (hasRunningInstance) {
                                    return; // 直接返回，避免后续代码执行
                                }
                                confirm({
                                    title: '确认框?',
                                    content: '确认开始运行',
                                    okText: '确定',
                                    cancelText: '取消',
                                    async onOk() {
                                        const res = await dataSyncTask.beginRun(record.id);
                                        if (res === true) {
                                            topThis.openExpanded(false, true, {
                                                id: record.datasyncId
                                            });
                                        }
                                    },
                                });
                            }}>开始运行</Button>
                            <Button size="small" onClick={() => {
                                router.push({
                                    pathname: '/integrate/dataSyncLog',
                                    query: {
                                        jobGroup: record?.jobGroup,
                                        instanceName: record?.instanceName,
                                        instanceId: record?.dispatchId
                                    },
                                });
                            }}>运行记录</Button>
                            <Dropdown overlay={menu} trigger={['click']}>
                                <Button size="small">
                                    <Space>
                                        更多
                                        <DownOutlined/>
                                    </Space>
                                </Button>
                            </Dropdown>
                        </Space>
                    );
                    return (
                        <Tooltip>
                            <div>{operation}</div>
                        </Tooltip>
                    );
                }

                if (record.triggerStatus === 1) {
                    const menu = (
                        <Menu>
                            <Space direction="vertical">
                                <Button type="text" onClick={(e) => this.getNextRunTime(e, record)}>
                                    <span>下次调度时间</span>
                                </Button>
                                <Button type="text" onClick={() => {
                                    dataSyncTask.readerJson(record.id).then((res) => {
                                        const body = JSON.stringify(res, null, 2);
                                        console.log(res);
                                        Modal.success({
                                            centered: true,
                                            title: "原始文件内容",
                                            width: 800,
                                            content: (
                                                <TextArea autoSize={true} value={body}/>
                                            ),
                                            okButtonProps: {style: {display: 'none'}},
                                            maskClosable: true,
                                            mask: false,
                                        });
                                    });
                                }}>
                                    <span>查看Json</span>
                                </Button>
                            </Space>
                        </Menu>
                    );
                    const operation = (
                        <Space>
                            {record.schedulerExpr !== "STREAMING" && <Button size="small" onClick={() => {
                                confirm({
                                    title: '确认框?',
                                    content: '是否确定需要运行一次',
                                    okText: '确定',
                                    cancelText: '取消',
                                    async onOk() {
                                        const res = await dataSyncTask.runOnce(record.id);
                                        if (res === true) {
                                            messageModal("success", "运行成功");
                                        }
                                    },
                                });
                            }}>运行一次</Button>}
                            <Button size="small" onClick={() => {
                                const topThis = this;
                                confirm({
                                    title: '确认框?',
                                    content: '确认停止运行',
                                    okText: '确定',
                                    cancelText: '取消',
                                    async onOk() {
                                        const res = await dataSyncTask.stopRun(record.id);
                                        if (res === true) {
                                            topThis.openExpanded(false, true, {
                                                id: record.datasyncId
                                            });
                                        }
                                    },
                                });
                            }}>停止运行</Button>
                            <Button size="small" onClick={() => {
                                router.push({
                                    pathname: '/integrate/dataSyncLog',
                                    query: {
                                        jobGroup: record?.jobGroup,
                                        instanceName: record?.instanceName,
                                        instanceId: record?.dispatchId
                                    },
                                });
                            }}>运行记录</Button>
                            <Dropdown overlay={menu} trigger={['click']}>
                                <Button size="small">
                                    <Space>
                                        更多
                                        <DownOutlined/>
                                    </Space>
                                </Button>
                            </Dropdown>
                        </Space>
                    );
                    return (
                        <Tooltip>
                            <div>{operation}</div>
                        </Tooltip>
                    );
                }

                if (record.triggerStatus === -1) {
                    const operation = (
                        <Space>
                            <Button size="small" onClick={() => {
                                dataSyncTask.readerJson(record.id).then((res) => {
                                    const body = JSON.stringify(res, null, 2);
                                    console.log(res);
                                    Modal.success({
                                        centered: true,
                                        title: "原始文件内容",
                                        width: 800,
                                        content: (
                                            <TextArea autoSize={true} value={body}/>
                                        ),
                                        okButtonProps: {style: {display: 'none'}},
                                        maskClosable: true,
                                        mask: false,
                                    });
                                });
                            }}>查看Json</Button>
                            <Button size="small" onClick={() => {
                                this.deleteDatasyncTask(record);
                            }}>删除</Button>
                        </Space>
                    );
                    return (
                        <Tooltip>
                            <div>{operation}</div>
                        </Tooltip>
                    );
                }
            },
        }
    ];

    getNextRunTime = (e, record) => {
        getNextRunTimer({
            scheduleType: "CRON",
            scheduleConf: record.schedulerExpr
        }).then((data) => {
            if (data && data.length) {
                Modal.success({
                    centered: true,
                    title: "近5次运行时间",
                    content: data.map((body) => {
                        return body + "\n"
                    }),
                    okButtonProps: {style: {display: 'none'}},
                    maskClosable: true,
                    mask: false,
                });
            } else {
                messageModal("error", "获取异常", data);
            }
        })
    }

    openExpanded = (hasSystem, expanded, record) => {
        const {childrenDatasource, openChildrenRows} = this.state;
        if (expanded) {
            this.setState({
                childrenDatasource: {
                    ...childrenDatasource, [record.id]: {
                        loading: true,
                        data: [],
                    }
                }
            })
            dataSyncTask.query(record.id).then((result) => {
                this.setState({
                    childrenDatasource: {
                        ...childrenDatasource, [record.id]: {
                            loading: false,
                            data: result
                        }
                    }
                })
            }).catch(() => {
                this.setState({
                    childrenDatasource: {
                        ...childrenDatasource, [record.id]: {
                            loading: false,
                            data: [],
                        }
                    }
                })
            })
            if (hasSystem) {
                openChildrenRows.push(record.id);
            }
        } else {
            let index = 0;
            for (let i = 0; i < openChildrenRows.length; i++) {
                if (openChildrenRows[i] === record.id) {
                    index = i;
                    break;
                }
            }
            openChildrenRows.splice(index, 1);
        }
    }

    /**
     * 查询框
     */
    renderForm() {
        const {form} = this.props;
        const {getFieldDecorator} = this.props.form;
        return (
            <Form onSubmit={this.handleSearch}>
                <Row gutter={{md: 4, lg: 12, xl: 24}}>
                    <Col md={5} sm={24}>
                        <FormItem label="数据同步名称">
                            {getFieldDecorator('intgName', {
                                rules: [
                                    {max: 20, message: '最多可输入20字'},
                                ],
                            })(<Input placeholder="请输入数据同步名称"
                                      onBlur={utils.valToTrim.bind(this, 'intgName', form)}
                                      allowClear={true}/>)}
                        </FormItem>
                    </Col>
                    <Col md={5} sm={24}>
                        <FormItem label="处理模式">
                            {getFieldDecorator('jobMode', {
                                initialValue: ''
                            })(<Select style={{width: '100%'}} allowClear={true}>
                                {jobModeList && jobModeList.map(item => (
                                    <Select.Option key={item.code}>{item.name}</Select.Option>
                                ))}
                            </Select>)}
                        </FormItem>
                    </Col>
                    <Col xl={5} md={12} xs={24}>
                        <FormItem label="创建时间：">
                            {getFieldDecorator('createTime', {
                                rules: [],
                            })(<RangePicker showTime allowClear={true} format={'YYYY/MM/DD HH:mm:ss'}
                                            style={{width: '100%'}}
                                            ranges={{
                                                "最近一小时": [moment().subtract(1, 'hours'), moment()],
                                                "今天": [moment().startOf('day'), moment().endOf('day')],
                                                '昨天': [moment(moment().subtract(1, 'day')).startOf('day'), moment(moment().subtract(1, 'day')).endOf('day')],
                                                '本月': [moment().startOf('month'), moment().endOf('month')],
                                                '上个月': [moment(moment().subtract(1, 'month')).startOf('month'), moment(moment().subtract(1, 'month')).endOf('month')],
                                                '最近一周': [moment().subtract(1, 'week'), moment()],
                                                '最近一个月': [moment().subtract(1, 'month'), moment()],
                                            }}
                            />)}
                        </FormItem>
                    </Col>
                    <Col md={5} sm={24} style={{paddingTop: '4px'}}>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        <Button style={{marginLeft: 8}} type="primary" onClick={this.handleFormReset}>重置</Button>
                    </Col>
                </Row>
            </Form>
        );
    }

    handleModeSelect = (mode) => {

        this.setState({
            jobMode: mode
        })
        console.log('已选择处理模式:', mode);
        // 这里可以添加后续处理逻辑
    };

    handleModeClose = (mode) => {
        this.setState({modeModalVisible: false, jobMode: mode}, () => {
            this.formRef.handleMinModalVisible(true, '新增', 0, {jobMode: mode});
        });
    }

    render() {

        const {
            openChildrenRows,
            selectedRows,
            childrenDatasource,
            modeModalVisible,
            jobMode
        } = this.state;

        const {dataSync: {data, loading, dataSourceList}} = this.props;

        const methods = {
            submitForm: this.submitForm,
            submitPublishForm: this.submitPublishForm,
            submitUpdateDatasyncTaskForm: this.submitUpdateDatasyncTaskForm,
            dataSourceList,
            selectedRows,
            jobMode
        }

        const expandedRowRender = (record) => {
            const {loading = false, data = []} = childrenDatasource?.[record.id] || {};
            this.setState({
                currentInstance: data
            })
            return (
                <Table
                    rowClassName={styles.datasyncTaskTableLine}
                    loading={loading}
                    key={record.id}
                    columns={this.childrenColumns}
                    dataSource={data}
                    pagination={false}
                />
            );
        };


        return (
            <PageHeaderWrapper>
                <Card bordered={false}>
                    <div>
                        <div className={styles.tableListForm}>{this.renderForm()}</div>
                        <Space style={{marginBottom: 16}}>
                            <Button type="primary" onClick={() => this.openAddOrUpdateOrDetailModal('add')}>
                                <PlusCircleOutlined style={{fontSize: 16}}/>
                                <span>新增</span>
                            </Button>
                            <Button onClick={() => this.queryList()} icon={<SyncOutlined style={{fontSize: 16}}/>}/>
                        </Space>
                        <CommonTable
                            loading={loading}
                            data={data}
                            expandable={true}
                            expandedRowKeys={openChildrenRows}
                            onExpand={(expanded, record) => {
                                this.openExpanded(true, expanded, record);
                            }}
                            expandedRowRender={expandedRowRender}
                            rowSelectionShow={false}
                            columns={this.columns}
                            current={markPage}
                            onChange={this.handleCommonTableChange}
                        />
                        <ForAutoModal wrappedComponentRef={node => (this.formRef = node)} queryList={this.queryList}
                                      handleSearch={this.handleSearch}/>
                    </div>
                </Card>
                <UpdateDatasyncTaskForm
                    wrappedComponentRef={(inst) => this.updateDatasyncTaskForm = inst} {...methods}/>

                <ModeSelectionModal
                    visible={modeModalVisible}
                    onClose={(mode) => this.handleModeClose(mode)}
                    onModeSelect={this.handleModeSelect}
                />
                <PublishForm wrappedComponentRef={(inst) => this.publishForm = inst} {...methods}/>
            </PageHeaderWrapper>
        );
    }
}
