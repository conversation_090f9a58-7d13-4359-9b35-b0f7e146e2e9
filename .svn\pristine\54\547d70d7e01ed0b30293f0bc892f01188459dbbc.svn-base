<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xmcares.framework</groupId>
        <artifactId>xcnf-dependencies</artifactId>
        <version>1.3.1-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <groupId>com.xmcares.platform</groupId>
    <artifactId>xotp</artifactId>
    <version>*******-SNAPSHOT</version>
    <description>XMCares One-data Technology Platform</description>
    <packaging>pom</packaging>

    <modules>
        <module>xotp-admin</module>
        <module>xotp-release</module>
        <module>xotp-hookbox</module>
        <module>xotp-datax</module>
        <module>xotp-flinkx</module>
        <module>xotp-openapi</module>
        <module>xotp-seatunnel</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <xcnf.version>*******-SNAPSHOT</xcnf.version>
        <datax.version>2023.09</datax.version>
        <xxl-job.version>2.5.0</xxl-job.version>

        <mybatis-spring-boot-starter.version>2.1.4</mybatis-spring-boot-starter.version>

        <jna.version>4.1.0</jna.version>

        <commons-cli.version>1.3.1</commons-cli.version>
        <commons-io.version>2.14.0</commons-io.version>
        <commmons-net.version>3.9.0</commmons-net.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <commons-dbutils.version>1.7</commons-dbutils.version>

        <hessian.version>4.0.63</hessian.version>

        <hutool.version>5.8.24</hutool.version>

        <pagehelper-springboot.version>1.4.1</pagehelper-springboot.version>

        <mysql-jdbc.version>8.0.33</mysql-jdbc.version>
        <oracle-jdbc.version>23.8.0.25.04</oracle-jdbc.version>
        <mssql-jdbc.version>12.8.1.jre8</mssql-jdbc.version>
        <dm-jdbc.version>1.8</dm-jdbc.version>
        <opengauss-jdbc.version>6.0.0</opengauss-jdbc.version>
        <mongodb-jdbc.version>5.5.0</mongodb-jdbc.version>

        <rocketmq-client.version>4.5.2</rocketmq-client.version>
        <rabbitmq-client.version>5.14.3</rabbitmq-client.version>
        <kafka-clinet.version>2.8.1</kafka-clinet.version>
        <pulsar-client.version>4.0.3</pulsar-client.version>
        <artemis-client.version>2.19.1</artemis-client.version>
        <mqtt-client.version>1.2.5</mqtt-client.version>
        <ximc-client.version>2.1.1.6</ximc-client.version>

        <flink.version>1.14.6</flink.version>
        <scala.binary.version>2.11</scala.binary.version>
        <cdh.version>cdh6.0.1</cdh.version>
        <!--        <hadoop.version>3.0.0-${cdh.version}</hadoop.version>-->
        <!--        <hbase.version>2.0.0-${cdh.version}</hbase.version>-->
        <!--        <hive.version>2.1.1-${cdh.version}</hive.version>-->
        <hadoop.version>2.7.3</hadoop.version>
        <hbase.version>2.0.0</hbase.version>
        <hive.version>1.1.0</hive.version>



        <!--    <project.basedir>${project.basedir}< /project.basedir>-->
    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- apache commons -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commmons-net.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-dbutils</groupId>
                <artifactId>commons-dbutils</artifactId>
                <version>${commons-dbutils.version}</version>
            </dependency>

            <!-- Spring boot stater: xcnf -->
            <dependency>
                <groupId>com.xmcares.framework</groupId>
                <artifactId>spring-boot-starter-xcnf-web</artifactId>
                <version>${xcnf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmcares.framework</groupId>
                <artifactId>spring-boot-starter-xcnf-rbac-service</artifactId>
                <version>${xcnf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmcares.framework</groupId>
                <artifactId>spring-boot-starter-xcnf-sharing-service</artifactId>
                <version>${xcnf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmcares.framework</groupId>
                <artifactId>xcnf-security-oauth2</artifactId>
                <version>${xcnf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmcares.framework</groupId>
                <artifactId>xcnf-oauth2-service</artifactId>
                <version>${xcnf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmcares.framework</groupId>
                <artifactId>xcnf-security-rbac</artifactId>
                <version>${xcnf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmcares.framework</groupId>
                <artifactId>xcnf-rbac-service</artifactId>
                <version>${xcnf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot-starter.version}</version>
            </dependency>
            <!-- Spring cloud starter: xcnf -->
            <dependency>
                <groupId>com.xmcares.framework</groupId>
                <artifactId>spring-cloud-starter-xcnf-cloud</artifactId>
                <version>${xcnf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmcares.framework</groupId>
                <artifactId>spring-cloud-starter-xcnf-security-rbac</artifactId>
                <version>${xcnf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmcares.framework</groupId>
                <artifactId>spring-cloud-starter-xcnf-security-sharing</artifactId>
                <version>${xcnf.version}</version>
            </dependency>

            <!-- Spring cloud starter: auto gateway zuul -->
            <dependency>
                <groupId>com.xmcares.framework</groupId>
                <artifactId>xcnf-cloud-gateway-zuul</artifactId>
                <version>${xcnf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmcares.framework</groupId>
                <artifactId>xcnf-data-fsclient</artifactId>
                <version>${xcnf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmcares.framework</groupId>
                <artifactId>xcnf-commons-lang</artifactId>
                <version>${xcnf.version}</version>
                <exclusions>
                    <!-- SpringBoot 辅助 -->
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-autoconfigure</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-autoconfigure-processor</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- com.alibaba.datax -->
            <dependency>
                <groupId>com.alibaba.datax</groupId>
                <artifactId>datax-common</artifactId>
                <version>${datax.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.datax</groupId>
                <artifactId>datax-transformer</artifactId>
                <version>${datax.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.datax</groupId>
                <artifactId>datax-core</artifactId>
                <version>${datax.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.datax</groupId>
                <artifactId>plugin-rdbms-util</artifactId>
                <version>${datax.version}</version>
            </dependency>
            <!-- XXL-JOB 组件 -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-admin</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <dependency>
                <groupId>com.caucho</groupId>
                <artifactId>hessian</artifactId>
                <version>${hessian.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-http</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-json</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- :::: 各种消息中间件支持 :::: 开始！-->
            <!-- rocketmq依赖 -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${rocketmq-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-acl</artifactId>
                <version>${rocketmq-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-tools</artifactId>
                <version>${rocketmq-client.version}</version>
            </dependency>
            <!-- rabbitmq -->
            <dependency>
                <groupId>com.rabbitmq</groupId>
                <artifactId>amqp-client</artifactId>
                <version>${rabbitmq-client.version}</version>
            </dependency>
            <!-- pulsar, pulsar token, pulsar oauth2-->
            <dependency>
                <groupId>org.apache.pulsar</groupId>
                <artifactId>pulsar-client</artifactId>
                <version>${pulsar-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pulsar</groupId>
                <artifactId>pulsar-client-admin-original</artifactId>
                <version>${pulsar-client.version}</version>
            </dependency>
            <!-- activemq artemis 队列-->
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>artemis-core-client</artifactId>
                <version>${artemis-client.version}</version>
            </dependency>
            <!-- mqtt 数据源客户端 -->
            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
                <version>${mqtt-client.version}</version>
            </dependency>
            <!-- ximc -->
            <dependency>
                <groupId>com.xmcares.framework.imc</groupId>
                <artifactId>imc-client</artifactId>
                <version>${ximc-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmcares.framework.imc</groupId>
                <artifactId>imc-client-adapter</artifactId>
                <version>${ximc-client.version}</version>
            </dependency>

            <!-- :::: 各种消息中间件支持 :::: 结束！-->

            <!-- :::: 各种数据源支持 :::: 开始！-->
            <!--mysql or doris-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-jdbc.version}</version>
            </dependency>
            <!--oracle-->
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle-jdbc.version}</version>
            </dependency>
            <!--sqlserver-->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${mssql-jdbc.version}</version>
            </dependency>
            <!--dm-->
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>dm-jdbc</artifactId>
                <version>${dm-jdbc.version}</version>
            </dependency>
            <!--open gauss-->
            <dependency>
                <groupId>org.opengauss</groupId>
                <artifactId>opengauss-jdbc</artifactId>
                <version>${opengauss-jdbc.version}</version>
            </dependency>
            <!-- mongodb -->
            <!--<dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-sync</artifactId>
                <version>${mongodb-jdbc.version}</version>
            </dependency>-->
            <!--es-->
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>7.17.15</version>
            </dependency>

            <!-- Apache Hadoop, HBase dependencies -->
            <!-- Hadoop Common -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-common</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core-asl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-mapper-asl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-configuration</artifactId>
                        <groupId>commons-configuration</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-net</artifactId>
                        <groupId>commons-net</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-jaxrs</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-xc</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jsr305</artifactId>
                        <groupId>com.google.code.findbugs</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-hdfs</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-cli</artifactId>
                        <groupId>commons-cli</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core-asl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-mapper-asl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jetty</artifactId>
                        <groupId>org.mortbay.jetty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jetty-util</artifactId>
                        <groupId>org.mortbay.jetty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-client</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-auth</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-yarn</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-mapreduce</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-mapreduce-client</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-hdfs-client</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-yarn-common</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-yarn-api</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-yarn-client</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jdk.tools</artifactId>
                        <groupId>jdk.tools</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-yarn-registry</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-yarn-server</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- HBase -->
            <dependency>
                <groupId>org.apache.hbase</groupId>
                <artifactId>hbase-common</artifactId>
                <version>${hbase.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hbase</groupId>
                <artifactId>hbase-client</artifactId>
                <version>${hbase.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hbase</groupId>
                <artifactId>hbase-mapreduce</artifactId>
                <version>${hbase.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--hive-->
            <dependency>
                <groupId>org.apache.hive</groupId>
                <artifactId>hive-jdbc</artifactId>
                <version>${hive.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>zookeeper</artifactId>
                        <groupId>org.apache.zookeeper</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty.aggregate</groupId>
                        <artifactId>jetty-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>servlet-api-2.5</artifactId>
                        <groupId>org.mortbay.jetty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>javax.servlet</artifactId>
                        <groupId>org.eclipse.jetty.orbit</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j-1.2-api</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j-core</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j-slf4j-impl</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j-web</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hadoop-mapreduce-client-core</artifactId>
                        <groupId>org.apache.hadoop</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jdk.tools</artifactId>
                        <groupId>jdk.tools</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>javax.servlet.jsp</artifactId>
                        <groupId>org.glassfish.web</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-runner</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>apache-log4j-extras</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>avro</artifactId>
                        <groupId>org.apache.avro</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hadoop-annotations</artifactId>
                        <groupId>org.apache.hadoop</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-jaxrs</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-xc</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-collections</artifactId>
                        <groupId>commons-collections</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- :::: 各种数据源支持 :::: 结束！-->
        </dependencies>
    </dependencyManagement>


    <repositories>
        <repository>
            <id>nexus3</id>
            <name>nexus3</name>
            <!--仓库地址-->
            <url>http://10.83.100.200:8181/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <!--        <repository>-->
        <!--            <id>cloudera.repo</id>-->
        <!--            <url>https://repository.cloudera.com/artifactory/cloudera-repos/</url>-->
        <!--        </repository>-->
    </repositories>


    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-help-plugin</artifactId>
                    <version>3.4.0</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>appassembler-maven-plugin</artifactId>
                    <version>2.0.0</version>
                </plugin>

                <!-- We use the maven-shade plugin to create a fat jar that contains all necessary dependencies. -->
                <!-- Change the value of <mainClass>...</mainClass> if your program entry point changes. -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.2.4</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <parameters>true</parameters>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <configuration>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <configuration>
                        <archive>
                            <manifest>
                                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            </manifest>
                            <manifestEntries>
                                <Built-By>X-Tech @ XmCares Co., LTD</Built-By>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>

            <!--用于格式化代码，mvn spotless:check mvn spotless:apply -->
            <!--<plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>2.22.1</version>
                <configuration>
                    <java>
                        <eclipse>
                            <file>dev-support/dailymart_spotless_formatter.xml</file>
                        </eclipse>
                        <licenseHeader>
                            <file>dev-support/license-header</file>
                        </licenseHeader>
                    </java>
                </configuration>
            </plugin>-->

        </plugins>

    </build>

</project>
