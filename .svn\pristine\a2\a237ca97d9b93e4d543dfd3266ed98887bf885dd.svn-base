package com.xmcares.platform.hookbox.integrator.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * XXL-Job日志与任务实例分页查询条件DTO
 *
 * <AUTHOR> Assistant
 * @Date 2025/1/24
 */
@ApiModel(value = "XXL-Job日志分页查询条件", description = "用于分页查询XXL-Job日志与任务实例关联数据的查询条件")
public class XxlJobLogQueryDTO {

    @ApiModelProperty(value = "调度ID，支持精确匹配")
    private long scheduleId;

    @ApiModelProperty(value = "调度日志ID，支持精确匹配")
    private long scheduleLogId;

    @ApiModelProperty(value = "调度过滤事件参数")
    private String filterTime;

    @ApiModelProperty(value = "任务实例状态，支持精确匹配")
    private Byte status;

    @ApiModelProperty(value = "触发码，支持精确匹配")
    private Integer triggerCode;

    @ApiModelProperty(value = "执行码，支持精确匹配")
    private Integer handleCode;

    @ApiModelProperty(value = "触发时间开始，格式：yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date triggerTimeStart;

    @ApiModelProperty(value = "触发时间结束，格式：yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date triggerTimeEnd;

    @ApiModelProperty(value = "执行器地址，支持模糊匹配")
    private String executorAddress;

    @ApiModelProperty(value = "执行器处理器，支持模糊匹配")
    private String executorHandler;

    @ApiModelProperty(value = "执行器ID，支持精确匹配")
    private long jobGroup;

    @ApiModelProperty(value = "任务ID，支持精确匹配")
    private long jobId;

    @ApiModelProperty(value = "任务实例ID，支持精确匹配")
    private long jobInstanceId;

    // 构造方法
    public XxlJobLogQueryDTO() {
    }

    // Getter 和 Setter 方法
    public long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public long getScheduleLogId() {
        return scheduleLogId;
    }

    public void setScheduleLogId(long scheduleLogId) {
        this.scheduleLogId = scheduleLogId;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Integer getTriggerCode() {
        return triggerCode;
    }

    public void setTriggerCode(Integer triggerCode) {
        this.triggerCode = triggerCode;
    }

    public Integer getHandleCode() {
        return handleCode;
    }

    public void setHandleCode(Integer handleCode) {
        this.handleCode = handleCode;
    }

    public Date getTriggerTimeStart() {
        return triggerTimeStart;
    }

    public void setTriggerTimeStart(Date triggerTimeStart) {
        this.triggerTimeStart = triggerTimeStart;
    }

    public Date getTriggerTimeEnd() {
        return triggerTimeEnd;
    }

    public void setTriggerTimeEnd(Date triggerTimeEnd) {
        this.triggerTimeEnd = triggerTimeEnd;
    }

    public String getExecutorAddress() {
        return executorAddress;
    }

    public void setExecutorAddress(String executorAddress) {
        this.executorAddress = executorAddress;
    }

    public String getExecutorHandler() {
        return executorHandler;
    }

    public void setExecutorHandler(String executorHandler) {
        this.executorHandler = executorHandler;
    }

    public long getJobGroup() {
        return jobGroup;
    }

    public void setJobGroup(long jobGroup) {
        this.jobGroup = jobGroup;
    }

    public long getJobId() {
        return jobId;
    }

    public void setJobId(long jobId) {
        this.jobId = jobId;
    }

    public long getJobInstanceId() {
        return jobInstanceId;
    }

    public void setJobInstanceId(long jobInstanceId) {
        this.jobInstanceId = jobInstanceId;
    }

    public String getFilterTime() {
        return filterTime;
    }

    public void setFilterTime(String filterTime) {
        this.filterTime = filterTime;
    }

    @Override
    public String toString() {
        return "XxlJobLogQueryDTO{" +
                "scheduleId='" + scheduleId + '\'' +
                ", scheduleLogId='" + scheduleLogId + '\'' +
                ", status='" + status + '\'' +
                ", triggerCode=" + triggerCode +
                ", handleCode=" + handleCode +
                ", triggerTimeStart=" + triggerTimeStart +
                ", triggerTimeEnd=" + triggerTimeEnd +
                ", executorAddress='" + executorAddress + '\'' +
                ", executorHandler='" + executorHandler + '\'' +
                ", jobGroup='" + jobGroup + '\'' +
                ", jobId='" + jobId + '\'' +
                ", jobInstanceId='" + jobInstanceId + '\'' +
                ", filterTime='" + filterTime + '\'' +
                '}';
    }
}
