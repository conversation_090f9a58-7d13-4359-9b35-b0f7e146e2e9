<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmcares.platform.admin.integrator.datasync.repository.mapper.DatasyncJobInstanceMapper">
  <resultMap id="BaseResultMap" type="com.xmcares.platform.admin.integrator.datasync.model.DatasyncJobInstance">
    <!--@mbg.generated-->
    <!--@Table xotp.bdp_intg_datasync_job_instance-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="job_id" jdbcType="VARCHAR" property="jobId" />
    <result column="job_mode" jdbcType="VARCHAR" property="jobMode" />
    <result column="job_name" jdbcType="VARCHAR" property="jobName" />
    <result column="job_params" jdbcType="LONGVARCHAR" property="jobParams" />
    <result column="schedule_id" jdbcType="VARCHAR" property="scheduleId" />
    <result column="schedule_time" jdbcType="TIMESTAMP" property="scheduleTime" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="status_message" jdbcType="LONGVARCHAR" property="statusMessage" />
    <result column="received_count" jdbcType="INTEGER" property="receivedCount" />
    <result column="recevied_qps" jdbcType="DOUBLE" property="receviedQps" />
    <result column="writed_count" jdbcType="INTEGER" property="writedCount" />
    <result column="writed_qps" jdbcType="DOUBLE" property="writedQps" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, job_id, job_mode, job_name, job_params, schedule_id, schedule_time, finish_time, `status`, status_message,
    received_count, recevied_qps, writed_count, writed_qps, create_time, create_user,
    update_time, update_user
  </sql>
</mapper>
