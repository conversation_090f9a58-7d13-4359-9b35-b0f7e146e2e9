import React, {useEffect, useImperative<PERSON>andle, useMemo, useRef, useState} from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>ton,
    Col,
    Divider,
    Dropdown,
    Modal,
    Row,
    Space,
    Tag,
    Tooltip,
    Typography,
    Select,
    message,
    Input
} from 'antd';

import ProCard from '@ant-design/pro-card';
import <PERSON>Form, {ProFormRadio, ProFormSelect, ProFormText, StepsForm} from '@ant-design/pro-form';
import {EditableProTable} from '@ant-design/pro-table';
import {
    DownOutlined,
    ThunderboltOutlined,
    CloudSyncOutlined,
    InfoCircleOutlined,
    QuestionCircleOutlined, EditOutlined, SyncOutlined, ArrowRightOutlined, PlusOutlined, DeleteOutlined, CopyOutlined
} from '@ant-design/icons';
import {CheckCard} from '@ant-design/pro-components';
import CronSelect from "./CronSelect";
import * as styles from "@/pages/modules/Integrate/DataSync/DataSync.less";
import {cronRegex} from './cron/cron-regex';
import {
    datasChildJobIdList,
    datasyncAdd,
    datasyncPluginGet,
    datasyncPluginList,
    datasyncSourceGet,
    datasyncSourceList,
    datasyncSourceTableList,
    datasyncUpdate
} from '@/services/Integrate/DataSync/DataSync';
import {getDataSource} from "@/services/Metadata/DataSource/DataSource"
import {messageModal} from "@/utils/messageModal";
import * as utils from "@/utils/utils";
import AutoFromRender from './AutoFormRender'
import EditableDragableTable from "@/pages/modules/Integrate/DataSync/components/editableDragableTable";
import AsyncTable from "@/pages/modules/Integrate/DataSync/components/AsyncTable";


const {Paragraph, Title} = Typography;

export default ({wrappedComponentRef, handleSearch}) => {

    const formRef = useRef();
    const childRef1 = useRef();
    const childRef2 = useRef();
    const childRef3 = useRef();
    const childRef4 = useRef();
    const childRef5 = useRef();
    const childRef6 = useRef();

    const [sourceFormLoading, setSourceFormLoading] = useState(false);
    const [gotoFormLoading, setGotoFormLoading] = useState(false);
    const [isModalVisible, setIsModalVisible] = useState(false);  // 弹窗是否打开
    //

    const [dataSource, setDataSource] = useState([]);


    const [childJobId, setChildJobId] = useState([])
    const [childJobIdKeys, setchildJobIdKeys] = useState(() =>
        childJobId.map((item) => item.ownId));
    const [gotoDataSource, setGotoDataSource] = useState([]);

    const [editableSourceKeys, setEditableSourceRowKeys] = useState(() =>
        dataSource.map((item) => item.ownId));

    const [editableGotoKeys, setEditableGotoRowKeys] = useState(() =>
        gotoDataSource.map((item) => item.ownId));

    const [sourceTab, setSourceTab] = useState('tab1'); // 设置数据源tab
    const [gotoTab, setGotoTab] = useState('t1');  // 设置数据去向tab

    const [defaultSourceRadio, setSourceRadio] = useState('0'); // 设置数据源同步组件Radio
    const [defaultGotoRadio, setGotoRadio] = useState('0');   // 设置数据去向同步组件Radio

    const [selectList, setSelectList] = useState([]);   // 选择当前数据源列表
    const [selectGotoList, setSelectGotoList] = useState([]);  // 选择当前数据去向列表


    const [current, setCurrent] = useState(0); // 设置当前第几步

    const [sourceModel, setSourceModel] = useState(null);   // 数据源设置下拉列表选择获取的数据


    const [gotoModel, setGotoModel] = useState(null);    // 数据去向设置下拉列表选择获取的数据


    const [destDatasourceName, setDestDatasourceName] = useState('');  // 数据源设置当前下拉列表选择后的名称
    const [orginDatasourceName, setOrginDatasourceName] = useState(''); // 数据去向设置当前下拉列表选择后的名称

    const [allData, setAllData] = useState({}); // 表单所有数据
    const [markAdd, setMarkAdd] = useState(0); // 判断是编辑还是新增
    const [cronExpression, setCron] = useState('0 0 0 * * ? *');

    const [sourceUseWay, setSourceUseWay] = useState('数据源'); // 设置来源数据方式名称
    const [destUseWay, setDestUseWay] = useState('数据源'); // 设置去向数据方式名称
    const [jobSource, setJobSource] = useState([]);
    const [originDatasourceId, setOriginDatasourceId] = useState(null);
    const [destDatasourceId, setDestDatasourceId] = useState(null);
    const [jobMode, setJobMode] = useState("BATCH")  // 模式

    useImperativeHandle(wrappedComponentRef, () => ({
        handleMinModalVisible: async (flag, title, mark, data) => {
            // 打开弹窗请求
            if (flag) {
                getChildJobIdList();
                setChildJobId([])
                setchildJobIdKeys([])
            }

            setIsModalVisible(flag);
            setMarkAdd(mark);
            // 新增
            if (mark == 0) {
                await getSelectList(defaultSourceRadio, 0, mark);
            }
            // 编辑
            if (mark == 1) {
                setCurrent(1)
                setJobMode(data?.jobMode || "BATCH")
                setSourceRadio(data?.orginType);
                await setAllData(data);
                setGotoRadio(data?.destType);
                await getSelectList(data?.orginType, 0, mark)
                formRef?.current?.setFieldsValue({
                    ...data,
                });
                setSourceTab('tab1')
                setOriginDatasourceId(data?.orginDatasourceId)
                setDestDatasourceId(data?.destDatasourceId)
                await getModels(data?.orginDatasourceId, 1, data?.orginType);
                await getModels(data?.destDatasourceId, 2, data?.destType);
                if (data?.orginColumnJson) {
                    const {orginColumnJson} = data;
                    if (typeof orginColumnJson === "string") {
                        const ids = []
                        const d = JSON.parse(orginColumnJson)?.map(item => {
                            const ownId = (Math.random() * 1000000000).toFixed(0);
                            ids.push(ownId);
                            return {ownId, ...item}
                        });

                        setEditableSourceRowKeys(ids);
                        setDataSource(d)
                    }
                }

                if (data?.childJobid) {
                    let childJobIdArrs = []
                    const ids = []
                    const dataArr = data.childJobid.split(",");
                    childJobIdArrs = dataArr.map((item, i) => {
                        const ownId = (Math.random() * 1000000000).toFixed(0);
                        ids.push(ownId)
                        const newItem = {
                            ownId,
                            index: i,
                            task: item,
                        }
                        return newItem
                    })
                    setChildJobId(childJobIdArrs)
                    setchildJobIdKeys(ids)
                }

                if (data?.destColumnJson) {
                    const {destColumnJson} = data;
                    if (typeof destColumnJson === "string") {
                        const ids = []
                        const d = JSON.parse(destColumnJson)?.map(item => {
                            const ownId = (Math.random() * 1000000000).toFixed(0);
                            ids.push(ownId);
                            return {ownId, ...item}
                        });

                        setEditableGotoRowKeys(ids);
                        setGotoDataSource(d)
                    }
                }
            }
        }
    }), [formRef])
    const save = (data) => {
        formRef.current.setFieldsValue({
            schedulerExpr: data
        });
        setCron(data);
    };

    /**

     获取数据源或插件列表并根据当前操作模式进行筛选

     @param {number} mark - 数据类型：0-内置数据源，1-插件

     @param {number} current - 当前步骤：0-源数据配置步骤，1-目标数据配置步骤

     @param {number} [isAdd] - 操作类型：0-新增，1-编辑。如果未提供，则使用组件状态中的markAdd值

     */
    const getSelectList = async (mark, current, isAdd) => {
        // 如果isAdd有定义，那就是说明传入了参数，没有参数就使用markAdd
        if (!(isAdd == 0 || isAdd == 1)) {
            isAdd = markAdd;
        }
        let res;
        if (mark == 0) {
            res = await getSourceList();
        } else {
            res = await getPluginList();
        }
        if (res && res.length > 0) {
            const data = res.map(item => {
                if (mark == 0) {
                    return {label: item.name, value: item.id, type: item.type, category: item?.category}
                }
                return {label: item.modelName, value: item.id, type: item.type}
            });

            // 根据jobMode筛选数据
            const filteredData = jobMode ? data.filter(item => {
                // 批模式只需要category为jdbc的数据
                if (jobMode === 'BATCH') {
                    return item.category === "JDBC";
                }
                // 流模式只需要category为mq的数据
                else if (jobMode === 'STREAMING') {
                    return item.category !== "BIGDATA"&&item.category !== "JDBC";
                }
                // 如果没有指定jobMode或其他情况，返回所有数据
                return true;
            }) : data;

            // todo 过滤大数据
            const filteredBigData = data.filter(item => {
                return item.category !== "BIGDATA";
            })

            console.log(data, markAdd,filteredBigData)
            if (current == 0) {
                // 如果是编辑就用全部数据,因为禁用了
                setSelectList(isAdd == 1 ? data : filteredData);
            };

            if (current == 1) {
                // 如果是编辑就用全部数据,因为禁用了
                setSelectGotoList(isAdd == 1 ? data : filteredBigData)
            }
        }
    }

    // 获取数据源列表
    const getSourceList = async () => {
        const res = await datasyncSourceList();
        return res;
    }

    // 获取数据表字段
    const getTableColumns = async (data) => {
        const res = await datasyncSourceTableList(data);
        return res
    }

    const getChildJobIdList = async () => {
        const res = await datasChildJobIdList();
        if (res && res.length > 0) {
            const data = res.map(item => ({
                label: item.instanceName, value: item.dispatchId
            }));
            setJobSource(data)
        }
    }

    // 获取插件列表
    const getPluginList = async () => {
        const res = await datasyncPluginList({
            integrationWay: 1,
            integrationType: current
        });
        return res;
    }

    // 判断是查询数据源还是插件模板信息，mark：0代表数据来源，1代表数据去向
    const getModels = async (id, mark, radio) => {
        let res;
        if (mark == 1) {
            setSourceFormLoading(true);
        }
        if (mark == 2) {
            setGotoFormLoading(true);
        }

        if (radio == '0') {
            res = await getSourceModel({
                datasourceId: id,
                type: mark - 1
            });
        } else {
            res = await getPluginModel({
                id,
            })
        }
        if (res) {
            if (mark === 1) {
                // const {columnJson,...rest}=res;
                setSourceModel({...res});
                setSourceFormLoading(false);
            }
            if (mark === 2) {
                setGotoModel({...res});
                setGotoFormLoading(false);
            }
        } else {
            // 没有需要清除
            if (mark === 1) {
                // const {columnJson,...rest}=res;
                setSourceModel(null);
                setSourceFormLoading(false);
            }
            if (mark === 2) {
                setGotoModel(null);
                setGotoFormLoading(false);
            }
        }
        if (res === undefined) {
            if (mark === 0) {
                setSourceFormLoading(false);
            }
            if (mark === 1) {
                setGotoFormLoading(false);
            }
        }
    }


    // 数据源选择获取模板信息
    const getSourceModel = async (data) => {
        const res = await datasyncSourceGet({
            ...data
        });
        return res
    }

    // 插件选择获取模板信息
    const getPluginModel = async (data) => {
        const res = await datasyncPluginGet({
            ...data
        });
        return res
    }

    const returnJson = (str) => {
        if (typeof str === 'string') {
            return str ? JSON.parse(str) : undefined
        }
        if (typeof str === "object") {
            return str
        }
        return undefined
    }

    const onClose = () => {
        setIsModalVisible(false);
        setSourceModel(null);
        setGotoModel(null);
        setCurrent(0);
        setSourceTab('tab1');
        setGotoTab('t1');
        setSourceRadio('0');
        setGotoRadio('0')
        setAllData({});
        setDestDatasourceName('');
        setOrginDatasourceName('');
        setDataSource([]);
        setGotoDataSource([]);
        setSourceFormLoading(false);
        setGotoFormLoading(false);
        setDestDatasourceId(null);
        setOriginDatasourceId(null);
        setJobMode("BATCH"); // 重置处理模式状态
        setOrginColumnJsonProps([]);
        setDestColumnJsonProps([])
    }

    const handleCronItemMenuClick = (e) => {
        console.log('click', e);
        setCron(e.key);
        formRef.current.setFieldsValue({
            schedulerExpr: e.key
        });
    };

    const cronItems = [
        {
            label: '每天凌晨执行一次',
            key: '0 0 0 * * ? *',
        },
        {
            label: '每小时执行一次',
            key: '0 0 * * * ? *',
        },
        {
            label: '每分钟执行一次',
            key: '0 * * * * ? *',
        },
        {
            label: '每月1号凌晨执行一次',
            key: '0 0 0 1 * ? *',
        },
    ];

    const cronItemMenuProps = {
        items: cronItems,
        onClick: handleCronItemMenuClick,
    };

    // 设置表名称
    const setTableName = (name, mark) => {
        console.log(name, mark)
        if (mark === "origin") {
            const oldV = childRef1?.current?.getValues();
            childRef1?.current?.setValues({...oldV, table: name})
        }
        if (mark === "dest") {
            const oldV = childRef4?.current?.getValues();
            childRef4?.current?.setValues({...oldV, table: name})
        }

    }


    const renderModeSelection = () => (
        <StepsForm.StepForm
            name="selectMode"
            title="处理模式"
            onFinish={async () => true}
        >
            <Typography>
                <Title level={5}>请选择数据处理模式：</Title>
                <Paragraph>根据您的业务需求，选择合适的数据处理方式。</Paragraph>
            </Typography>

            <CheckCard.Group
                onChange={(value) => {
                    setJobMode(value);
                    if (value === "STREAMING") {
                        formRef.current.setFieldsValue({
                            schedulerExpr: "STREAMING",
                            routeStrategy: "FIRST",
                            blockStrategy: "SERIAL_EXECUTION",
                            executorFailRetryCount: 0,
                            executorTimeout: 0
                        });
                        setCron("STREAMING")
                    } else {
                        formRef.current.setFieldsValue({
                            schedulerExpr: "0 0 0 * * ? *",
                            routeStrategy: "FIRST",
                            blockStrategy: "SERIAL_EXECUTION",
                            executorFailRetryCount: 3,
                            executorTimeout: 30
                        });
                        setCron("0 0 0 * * ? *");
                    }
                }}
                value={jobMode}
                style={{width: '100%'}}
                disabled={markAdd == 1}
            >
                <CheckCard
                    title="批处理模式 (Batch)"
                    description="一次性处理大量数据，适合定时任务和历史数据处理。批处理提供更高的吞吐量和资源利用率。"
                    value="BATCH"
                    style={{width: '48%', marginRight: '2%'}}
                    avatar={<ThunderboltOutlined style={{fontSize: '28px', color: '#1890ff'}}/>}
                />
                <CheckCard
                    title="流处理模式 (Streaming)"
                    description="实时处理数据流，适合需要即时反馈和低延迟场景。流处理提供更快的响应时间和连续性处理能力。"
                    value="STREAMING"
                    style={{width: '48%'}}
                    avatar={<CloudSyncOutlined style={{fontSize: '28px', color: '#52c41a'}}/>}
                />
            </CheckCard.Group>

            <div style={{margin: "20px 0px"}}>
                {jobMode === 'BATCH' && (
                    <div
                        className="mode-details"
                        style={{backgroundColor: '#f0f5ff', padding: 16, borderRadius: 8}}
                    >
                        <Typography>
                            <Paragraph strong>批处理模式特点：</Paragraph>
                            <ul>
                                <li>高吞吐量，适合大规模数据处理</li>
                                <li>资源利用效率高，计算成本较低</li>
                                <li>适合有明确时间窗口的处理任务</li>
                                <li>通常用于ETL、报表生成、数据分析等场景</li>
                            </ul>
                        </Typography>
                    </div>
                )}

                {jobMode === 'STREAMING' && (
                    <div
                        className="mode-details"
                        style={{backgroundColor: '#f6ffed', padding: 16, borderRadius: 8}}
                    >
                        <Typography>
                            <Paragraph strong>流处理模式特点：</Paragraph>
                            <ul>
                                <li>低延迟，实时数据处理和响应</li>
                                <li>连续性处理，支持实时监控和预警</li>
                                <li>适合需要即时反馈的业务场景</li>
                                <li>通常用于实时分析、监控仪表盘、异常检测等场景</li>
                            </ul>
                        </Typography>
                    </div>
                )}
            </div>
        </StepsForm.StepForm>
    );

    // 优化后的动态表达式指南组件 - 使用弹窗模式
    const DynamicExpressionGuide = () => {
        const [visible, setVisible] = useState(false);

        const showModal = () => setVisible(true);
        const hideModal = () => setVisible(false);

        return (
            <>
                <Button
                    type="primary"
                    ghost
                    icon={<QuestionCircleOutlined/>}
                    onClick={showModal}
                    style={{marginBottom: 16}}
                >
                    查看动态表达式使用说明
                </Button>

                <Modal
                    title={
                        <Space>
                            <InfoCircleOutlined/>
                            <span>动态表达式使用说明</span>
                        </Space>
                    }
                    open={visible}
                    width={700}
                    footer={[
                        <Button key="close" type="primary" onClick={hideModal}>
                            我知道了
                        </Button>
                    ]}
                    onCancel={hideModal}
                    bodyStyle={{maxHeight: '70vh', overflow: 'auto'}}
                >
                    <div style={{padding: '12px 0'}}>
                        <Typography.Title level={5}>1. 动态参数</Typography.Title>
                        <Typography.Paragraph>
                            <Typography.Text strong>使用格式：</Typography.Text>{' '}
                            <Typography.Text code style={{backgroundColor: '#f5f5f5'}}>${'{参数名}'}</Typography.Text>
                        </Typography.Paragraph>
                        <Typography.Paragraph>
                            <Typography.Text strong>当前系统支持内置参数：</Typography.Text>
                            <ul style={{marginBottom: '8px'}}>
                                <li>
                                    <Typography.Text
                                        code
                                        style={{backgroundColor: '#f5f5f5'}}
                                    >${'{triggerTime}'}
                                    </Typography.Text>：任务触发时间
                                </li>
                            </ul>
                        </Typography.Paragraph>
                        {/*<Typography.Paragraph>*/}
                        {/*    <Typography.Text strong>用户自定义参数：</Typography.Text>*/}
                        {/*    <ul style={{marginBottom: '8px'}}>*/}
                        {/*        <li>*/}
                        {/*            例如：<Typography.Text*/}
                        {/*            code*/}
                        {/*            style={{backgroundColor: '#f5f5f5'}}*/}
                        {/*        >${'{startDate}'}*/}
                        {/*        </Typography.Text>、*/}
                        {/*            <Typography.Text*/}
                        {/*                code*/}
                        {/*                style={{backgroundColor: '#f5f5f5'}}*/}
                        {/*            >${'{bizType}'}*/}
                        {/*            </Typography.Text>*/}
                        {/*            等（需在任务参数中定义）*/}
                        {/*        </li>*/}
                        {/*    </ul>*/}
                        {/*</Typography.Paragraph>*/}

                        <Divider style={{margin: '16px 0'}}/>

                        <Typography.Title level={5}>2. 动态函数</Typography.Title>
                        <Typography.Paragraph>
                            <Typography.Text strong>使用格式：</Typography.Text>{' '}
                            <Typography.Text
                                code
                                style={{backgroundColor: '#f5f5f5'}}
                            >$函数名{'{参数1, 参数2, ...}'}
                            </Typography.Text>
                        </Typography.Paragraph>
                        <Typography.Paragraph>
                            <Typography.Text strong>目前支持函数：</Typography.Text>
                            <ul style={{marginBottom: '8px'}}>
                                <li>
                                    <Typography.Text
                                        code
                                        style={{backgroundColor: '#f5f5f5'}}
                                    >$FMT_DT{'{时间参数名, 格式化字符串, 偏移量}'}
                                    </Typography.Text>：
                                    用于对日期时间参数进行格式化和偏移计算
                                </li>
                            </ul>
                        </Typography.Paragraph>
                        <Typography.Paragraph>
                            <Typography.Text strong>示例说明：</Typography.Text>
                            <ul style={{marginBottom: '8px'}}>
                                <li>
                                    <Typography.Text
                                        code
                                        style={{backgroundColor: '#f5f5f5'}}
                                    >$FMT_DT{'{triggerTime, yyyy-MM-dd, -1d}'}
                                    </Typography.Text>，
                                    表示触发时间前一天的日期，格式为 yyyy-MM-dd
                                </li>
                                <li>
                                    <Typography.Text
                                        code
                                        style={{backgroundColor: '#f5f5f5'}}
                                    >$FMT_DT{'{triggerTime, yyyyMMddHHmm, -15m}'}
                                    </Typography.Text>，
                                    表示触发时间前15分钟，格式为 yyyyMMddHHmm
                                </li>
                            </ul>
                        </Typography.Paragraph>
                        <Typography.Paragraph>
                            <Typography.Text strong>偏移单位说明：</Typography.Text>
                            <ul style={{marginBottom: '8px'}}>
                                <li>1）支持单位：y 年、M 月、d 天、h 小时、m 分钟、s 秒</li>
                                <li>2）正数表示向后推，负数表示向前推</li>
                            </ul>
                        </Typography.Paragraph>
                    </div>
                </Modal>
            </>
        );
    };


    const renderStepFirst = useMemo(() => (
        <StepsForm.StepForm
            name="source"
            initialValues={{
                orginType: defaultSourceRadio,
            }}
            title="数据来源"
            onFinish={async () => true}
            onValuesChange={async (_, values) => {
                // 判断radio变化
                if (values.orginType !== undefined) {
                    if (values.orginType == '0') {
                        setSourceUseWay('数据源');
                    } else {
                        setSourceUseWay('插件');
                    }
                    if (values.orginType != defaultSourceRadio) {
                        formRef.current.setFieldsValue({
                            orginDatasourceId: null
                        });
                        setSourceModel(null);
                        await getSelectList(values.orginType, 0);
                    }
                    await setSourceRadio(values.orginType);
                }
            }}
        >
            {/* 使用Card包装表单，提供更好的视觉层次 */}
            <ProCard
                style={{
                    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                    borderRadius: '8px',
                    marginBottom: 24
                }}
                bodyStyle={{padding: '24px'}}
            >
                <ProForm.Group>
                    <ProFormRadio.Group
                        name="orginType"
                        width="md"
                        layout="horizontal"
                        label={
                            <Tooltip title="选择数据源同步组件类型">
                                <span>同步组件</span>
                            </Tooltip>
                        }
                        fieldProps={{
                            disabled: markAdd == 1,
                            optionType: "button",
                            buttonStyle: "solid"
                        }}
                        options={[
                            {
                                label: '内置',
                                value: '0',
                            },
                            {
                                label: '插件',
                                value: '1',
                            },
                        ]}
                    />
                    <ProFormSelect
                        name="orginDatasourceId"
                        options={selectList}
                        showSearch
                        fieldProps={{
                            disabled: markAdd == 1,
                            filterOption: (input, option) =>
                                (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
                            onSelect: async (x, option) => {
                                console.log(option);
                                if (markAdd == 0) {
                                    formRef.current.setFieldsValue({
                                        intgName: `${option.label}->${destDatasourceName}`
                                    });
                                }

                                setOriginDatasourceId(option?.key);
                                setOrginDatasourceName(option.label);

                                // ximc、rabbitMQ 新增选择时自动带入账号、密码
                                if (["ximc", "rabbitmq"].includes(option?.type?.toLowerCase())) {
                                    const res = await getDataSource({id: option.value});
                                    setAllData({
                                        orginBaseJson: res?.options || {}
                                    });
                                }
                                await getModels(option.value, current, defaultSourceRadio);
                            },
                            onClear: () => {
                                setOriginDatasourceId(null);
                            },
                            optionItemRender: (option) => (
                                <Space align="center">
                                    <span>{option.label || ""}</span>
                                    <Tag
                                        color="blue"
                                        style={{
                                            backgroundColor: 'rgba(22, 119, 255, 0.1)',
                                            color: '#1677FF',
                                            marginLeft: 8
                                        }}
                                    >
                                        {option.type}
                                    </Tag>
                                </Space>
                            )
                        }}
                        width="md"
                        label={
                            <Tooltip title="选择需要连接的数据源">
                                <span>{sourceUseWay}</span>
                            </Tooltip>
                        }
                        placeholder="请选择"
                        rules={[{required: true, message: '不能为空!'}]}
                    />
                </ProForm.Group>
            </ProCard>

            {/* 表单信息部分 */}
            <ProCard
                tabs={{
                    activeKey: sourceTab,  // 使用activeKey替代value
                    type: 'card',
                    size: 'default',
                    onChange: async (key) => {
                        setSourceTab(key);
                    },
                    tabBarGutter: 16,
                    tabBarStyle: {fontWeight: 500}
                }}
                style={{
                    marginTop: 8,
                    width: "100%",
                    maxWidth: "900px",
                    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                    borderRadius: '8px'
                }}
                headerBordered={false}
            >
                <ProCard.TabPane key="tab1" tab="基础信息">
                    <AutoFromRender
                        ref={childRef1}
                        formLoading={sourceFormLoading}
                        tab={sourceTab}
                        id="1"
                        schema={returnJson(sourceModel?.baseJson)}
                        data={{
                            ...JSON.parse(allData?.orginBaseJson || '{}')
                        }}
                    />
                    <AsyncTable
                        datasourceId={originDatasourceId || null}
                        setTableName={(name) => setTableName(name, "origin")}
                    />
                </ProCard.TabPane>
                <ProCard.TabPane key="tab2" tab="进阶信息">
                    <AutoFromRender
                        ref={childRef2}
                        formLoading={sourceFormLoading}
                        tab={sourceTab}
                        id="2"
                        schema={returnJson(sourceModel?.advJson)}
                        data={{
                            ...JSON.parse(allData?.orginAdvJson || '{}')
                        }}
                    />
                </ProCard.TabPane>
                <ProCard.TabPane key="tab3" tab="高级信息">
                    <AutoFromRender
                        ref={childRef3}
                        formLoading={sourceFormLoading}
                        tab={sourceTab}
                        id="3"
                        schema={returnJson(sourceModel?.highJson)}
                        data={{
                            ...JSON.parse(allData?.orginHighJson || '{}')
                        }}
                    />
                </ProCard.TabPane>
            </ProCard>

            {/* 添加动态表达式指南按钮 */}
            <div style={{marginTop: 24}}>
                <DynamicExpressionGuide/>
                <Typography.Text type="secondary" style={{marginLeft: 8}}>
                    <InfoCircleOutlined style={{marginRight: 4}}/>
                    在表单中可以使用动态表达式来引用参数和函数
                </Typography.Text>
            </div>
        </StepsForm.StepForm>
    ), [defaultSourceRadio, markAdd, selectList, sourceModel, sourceFormLoading, sourceTab, originDatasourceId]);


    const renderStepSecond = useMemo(() => (
        <StepsForm.StepForm
            name="goto"
            initialValues={{
                destType: defaultGotoRadio,
            }}
            onValuesChange={async (_, values) => {
                if (values.destType == '0') {
                    setDestUseWay('数据源');
                } else {
                    setDestUseWay('插件');
                }
                if (values.destType !== undefined) {
                    if (values.destType != defaultGotoRadio) {
                        formRef.current.setFieldsValue({
                            destDatasourceId: null
                        });
                        setGotoModel(null);

                        await getSelectList(values.destType, 1);
                    }
                    await setGotoRadio(values.destType);
                }
            }}
            title="数据去向"
            onFinish={async () => true}
        >
            {/* 使用Card包装表单，提供更好的视觉层次 */}
            <ProCard
                style={{
                    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                    borderRadius: '8px',
                    marginBottom: 24
                }}
                bodyStyle={{padding: '24px'}}
            >
                <ProForm.Group>
                    <ProFormRadio.Group
                        name="destType"
                        width="md"
                        layout="horizontal"
                        label={
                            <Tooltip title="选择数据目标同步组件类型">
                                <span>同步组件</span>
                            </Tooltip>
                        }
                        fieldProps={{
                            disabled: markAdd == 1,
                            optionType: "button",
                            buttonStyle: "solid"
                        }}
                        options={[
                            {
                                label: '内置',
                                value: '0',
                            },
                            {
                                label: '插件',
                                value: '1',
                            },
                        ]}
                    />
                    <ProFormSelect
                        name="destDatasourceId"
                        width="md"
                        label={
                            <Tooltip title="选择数据写入的目标">
                                <span>{destUseWay}</span>
                            </Tooltip>
                        }
                        showSearch
                        fieldProps={{
                            disabled: markAdd == 1,
                            onSelect: async (x, option) => {
                                setDestDatasourceName(option.label);
                                setDestDatasourceId(option?.key);
                                if (markAdd == 0) {
                                    formRef.current.setFieldsValue({
                                        intgName: `${orginDatasourceName}->${option.label}`
                                    });
                                }
                                // 如果是jdbc类型，带入基本相关信息
                                if (["jdbc"].includes(option?.category?.toLowerCase())) {
                                    const res = await getDataSource({id: option.value});
                                    setAllData({
                                        destBaseJson: res?.options || {}
                                    });
                                }
                                ;

                                getModels(option.value, current, defaultGotoRadio);
                            },
                            onClear: () => {
                                setDestDatasourceId(null);
                            },
                            filterOption: (input, option) =>
                                (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
                            optionItemRender: (option) => (
                                <Space align="center">
                                    <span>{option.label || ""}</span>
                                    <Tag
                                        color="blue"
                                        style={{
                                            backgroundColor: 'rgba(22, 119, 255, 0.1)',
                                            color: '#1677FF',
                                            marginLeft: 8
                                        }}
                                    >
                                        {option.type}
                                    </Tag>
                                </Space>
                            )
                        }}
                        options={selectGotoList}
                        placeholder="请选择"
                        rules={[{required: true, message: '不能为空!'}]}
                    />
                </ProForm.Group>
            </ProCard>

            {/* 表单信息部分 */}
            <ProCard
                tabs={{
                    activeKey: gotoTab,  // 使用activeKey替代value
                    type: 'card',
                    size: 'default',
                    onChange: (key) => {
                        setGotoTab(key);
                    },
                    tabBarGutter: 16,
                    tabBarStyle: {fontWeight: 500}
                }}
                style={{
                    marginTop: 8,
                    width: "100%",
                    maxWidth: "900px",
                    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                    borderRadius: '8px'
                }}
                headerBordered={false}
            >
                <ProCard.TabPane key="t1" tab="基础信息">
                    <AutoFromRender
                        ref={childRef4}
                        formLoading={gotoFormLoading}
                        id="4"
                        tab={gotoTab}
                        schema={returnJson(gotoModel?.baseJson)}
                        data={{
                            ...JSON.parse(allData?.destBaseJson || '{}')
                        }}
                    />
                    <AsyncTable
                        datasourceId={destDatasourceId || null}
                        setTableName={(name) => setTableName(name, "dest")}
                    />
                </ProCard.TabPane>
                <ProCard.TabPane key="t2" tab="进阶信息">
                    <AutoFromRender
                        ref={childRef5}
                        formLoading={gotoFormLoading}
                        id="5"
                        tab={gotoTab}
                        schema={returnJson(gotoModel?.advJson)}
                        data={{
                            ...JSON.parse(allData?.destAdvJson || '{}')
                        }}
                    />
                </ProCard.TabPane>
                <ProCard.TabPane key="t3" tab="高级信息">
                    <AutoFromRender
                        ref={childRef6}
                        formLoading={gotoFormLoading}
                        id="6"
                        tab={gotoTab}
                        schema={returnJson(gotoModel?.highJson)}
                        data={{
                            ...JSON.parse(allData?.destHighJson || '{}')
                        }}
                    />
                </ProCard.TabPane>
            </ProCard>

        </StepsForm.StepForm>
    ), [defaultGotoRadio, markAdd, selectGotoList, gotoModel, gotoFormLoading, gotoTab, destDatasourceId, orginDatasourceName]);

    const renderStepThird = () => {
        // 映射数据
        const [mappingData, setMappingData] = useState([]);
        const [sourceOptions, setSourceOptions] = useState([]);
        const [targetOptions, setTargetOptions] = useState([]);
        // 添加刷新标记
        const [refreshKey, setRefreshKey] = useState(0);
        // 可编辑行keys
        const [editableKeys, setEditableKeys] = useState([]);
        // 表达式编辑弹窗
        const [expressionModalVisible, setExpressionModalVisible] = useState(false);
        const [currentField, setCurrentField] = useState(null);
        const [currentExpression, setCurrentExpression] = useState('');

        // 源和目标字段的列定义
        const sourceColumns = useMemo(() => sourceModel?.columnJson ? JSON.parse(sourceModel.columnJson) : [], [sourceModel]);

        const targetColumns = useMemo(() => gotoModel?.columnJson ? JSON.parse(gotoModel.columnJson) : [], [gotoModel]);
        // 在useEffect中处理选项数据
        // useEffect(() => {
        //     // 处理源字段选项
        //     if (dataSource && dataSource.length > 0) {
        //         const options = {};
        //         dataSource.forEach(item => {
        //             if (item.title) {
        //                 options[item.title] = {
        //                     text: item.title,
        //                     label: item.title, // 用于搜索功能
        //                     value: item.title
        //                 };
        //             }
        //         });
        //         setSourceOptions(options);
        //     }
        //
        //     // 处理目标字段选项
        //     if (gotoDataSource && gotoDataSource.length > 0) {
        //         const options = {};
        //         gotoDataSource.forEach(item => {
        //             if (item.title) {
        //                 options[item.title] = {
        //                     text: item.title,
        //                     label: item.title, // 用于搜索功能
        //                     value: item.title
        //                 };
        //             }
        //         });
        //         setTargetOptions(options);
        //     }
        // }, [dataSource, gotoDataSource]);
        // 初始化合并映射数据
        useEffect(() => {
            if (dataSource?.length > 0 || gotoDataSource?.length > 0) {
                const mergedData = createMappingData();

                handleMappingChange(mergedData);
                setEditableKeys(mergedData.map(item => item.ownId));
            }
        }, [dataSource, gotoDataSource, sourceColumns, targetColumns]);


        // 创建映射数据结构
        const createMappingData = () => {
            const mappings = [];
            const maxLength = Math.max(
                dataSource ? dataSource.length : 0,
                gotoDataSource ? gotoDataSource.length : 0
            );

            // 按顺序创建映射关系
            for (let i = 0; i < maxLength; i++) {
                const sourceItem = dataSource && i < dataSource.length ? {sourceType: "string", ...dataSource[i]} : {};
                const targetItem = gotoDataSource && i < gotoDataSource.length ? {targetType: "string", ...gotoDataSource[i]} : {};

                mappings.push({
                    ownId: `map-${Date.now()}-${i}`,
                    sourceData: sourceItem,
                    targetData: targetItem,
                    sourceTitle: sourceItem?.title || "",
                    sourceType: sourceItem?.type || "string",
                    targetTitle: targetItem?.title || "",
                    targetType: targetItem?.type || "string",
                    expression: '', // 转换表达式，默认为空
                    mappingType: 'direct' // 映射类型，默认为直接映射
                });
            }

            return mappings;
        };


        // 处理映射数据变化
        const handleMappingChange = (newMappingData) => {
            setMappingData([...newMappingData]);

            // 从映射数据中提取源数据和目标数据
            const newSourceData = [];
            const newTargetData = [];

            newMappingData.forEach(mapping => {
                if (Object.keys(mapping.sourceData).length > 0 || mapping?.sourceTitle) {
                    // 更新源数据，保存表达式到一个特殊字段
                    newSourceData.push({
                        ...mapping.sourceData,
                        _mappingExpression: mapping.expression,
                        _mappingTarget: mapping.targetData.title || '',
                        title: mapping?.sourceTitle || "",
                        type: mapping?.sourceType || "string",
                    });
                }

                if (Object.keys(mapping.targetData).length > 0 || mapping?.targetTitle) {
                    // 更新目标数据，保存表达式到一个特殊字段
                    newTargetData.push({
                        ...mapping.targetData,
                        _mappingExpression: mapping.expression,
                        _mappingSource: mapping.sourceData.title || '',
                        title: mapping?.targetTitle || "",
                        type: mapping?.targetType || "string",
                    });
                }
            });

            // 更新原始数据
            setOrginColumnJsonProps(newSourceData);
            setDestColumnJsonProps(newTargetData);
        };


        // 自动映射同名字段（简化版）
        const autoMapFields = () => {
            if (!mappingData) return;

            // 复制当前映射数据
            const newMappingData = [...mappingData];
            let matchCount = 0;

            // 创建源字段和目标字段的映射
            const sourceFieldMap = {};
            const targetFieldMap = {};

            // 记录所有源字段和目标字段
            newMappingData.forEach((item, index) => {
                if (item.sourceTitle) {
                    const key = item.sourceTitle.toLowerCase();
                    sourceFieldMap[key] = {index, value: item.sourceTitle};
                }

                if (item.targetTitle) {
                    const key = item.targetTitle.toLowerCase();
                    targetFieldMap[key] = {index, value: item.targetTitle};
                }
            });

            // 找到所有同名字段的匹配
            const matches = [];
            Object.keys(sourceFieldMap).forEach(key => {
                if (targetFieldMap[key]) {
                    matches.push({
                        sourceIndex: sourceFieldMap[key].index,
                        targetIndex: targetFieldMap[key].index,
                        sourceValue: sourceFieldMap[key].value,
                        targetValue: targetFieldMap[key].value
                    });
                }
            });

            // 处理交换逻辑
            if (matches.length > 0) {
                // 保存原始目标字段的值
                const originalTargetValues = matches.map(match => {
                    const item = newMappingData[match.sourceIndex];
                    return {
                        index: match.sourceIndex,
                        targetTitle: item.targetTitle,
                        targetType: item.targetType,
                        targetData: {...item.targetData}
                    };
                });

                // 先将同名字段匹配上
                matches.forEach(match => {
                    newMappingData[match.sourceIndex] = {
                        ...newMappingData[match.sourceIndex],
                        targetTitle: match.targetValue,
                        targetType: newMappingData[match.targetIndex].targetType,
                        targetData: {...newMappingData[match.targetIndex].targetData},
                        expression: '',
                        mappingType: 'direct'
                    };
                });

                // 处理被替换的目标字段的交换
                // 找出所有原来有目标字段但现在被替换的行
                const remainingSourceRows = newMappingData
                    .map((item, index) => ({item, index}))
                    .filter(({item, index}) =>
                        item.sourceTitle &&
                        matches.some(match => match.sourceIndex === index) &&
                        originalTargetValues.find(v => v.index === index)?.targetTitle
                    );

                // 找出所有被用作匹配的目标字段行
                const targetFieldRows = matches.map(match => ({
                    index: match.targetIndex,
                    item: newMappingData[match.targetIndex]
                }));

                // 为每个被替换的行重新分配目标字段
                for (let i = 0; i < remainingSourceRows.length; i++) {
                    if (i < targetFieldRows.length) {
                        const sourceRow = remainingSourceRows[i];
                        const targetRow = targetFieldRows[i];
                        const originalValue = originalTargetValues.find(v => v.index === sourceRow.index);

                        if (originalValue && originalValue.targetTitle) {
                            // 将原始目标字段值分配给匹配行
                            newMappingData[targetRow.index] = {
                                ...newMappingData[targetRow.index],
                                targetTitle: originalValue.targetTitle,
                                targetType: originalValue.targetType,
                                targetData: {...originalValue.targetData},
                                expression: '',
                                mappingType: 'direct'
                            };
                        }
                    }
                }

                matchCount = matches.length;
            }

            // 更新映射数据
            setMappingData([...newMappingData]);
            // 强制刷新表格
            if (typeof setRefreshKey === 'function') {
                setRefreshKey(prev => prev + 1);
            }
            handleMappingChange(newMappingData);

            if (matchCount > 0) {
                message.success(`自动映射完成，匹配了 ${matchCount} 个字段`);
            } else {
                message.info('未找到匹配的字段');
            }
        };

        // 添加新映射行
        const handleAddMapping = () => {
            const newMapping = {
                ownId: `map-${Date.now()}-${mappingData.length}`,
                sourceData: {sourceType: "string"},
                targetData: {targetType: "string"},
                sourceType: "string",
                targetType: "string",
                expression: '',
                mappingType: 'direct'
            };

            const newMappingData = [...mappingData, newMapping];
            setMappingData(newMappingData);
            // 设置新行为可编辑状态
            setEditableKeys([...editableKeys, newMapping.ownId]);
        };

        // 打开表达式编辑弹窗
        const openExpressionEditor = (record) => {
            setCurrentField(record);
            setCurrentExpression(record.expression || '');
            setExpressionModalVisible(true);
        };

        // 保存表达式
        const saveExpression = () => {
            const newMappingData = mappingData.map(item => {
                if (item.ownId === currentField.ownId) {
                    return {
                        ...item,
                        expression: currentExpression,
                        mappingType: currentExpression ? 'expression' : 'direct'
                    };
                }
                return item;
            });

            handleMappingChange(newMappingData);
            setExpressionModalVisible(false);
        };


        // 一键复制字段的处理函数（修改）
        const handleOneClickCopy = (direction) => {
            console.log(mappingData);
            const newMappingData = mappingData.map(item => {
                if (direction === 'toTarget') {
                    // 只有当源字段有值时才复制到目标字段
                    if (item.sourceTitle && item.sourceTitle.trim() !== '') {
                        return {
                            ...item,
                            targetTitle: item.sourceTitle,
                            targetType: item.sourceType,
                            targetData: {
                                ...item.targetData,
                                title: item.sourceTitle,
                                type: item.sourceType,
                            }
                        };
                    }
                } else if (direction === 'toSource') {
                    // 只有当目标字段有值时才复制到源字段
                    if (item.targetTitle && item.targetTitle.trim() !== '') {
                        return {
                            ...item,
                            sourceTitle: item.targetTitle,
                            sourceType: item.targetType,
                            sourceData: {
                                ...item.sourceData,
                                title: item.targetTitle,
                                type: item.targetType,
                            }
                        };
                    }
                }
                return item; // 如果不满足条件则返回原项
            });

            // 更新数据
            setMappingData([...newMappingData]);
            // 强制刷新表格
            setRefreshKey(prev => prev + 1);
            // 调用映射变化处理器
            handleMappingChange(newMappingData);
        };

        // 删除映射行
        const handleDeleteMapping = (key, record) => {
            Modal.confirm({
                title: '确认删除',
                content: '确定要删除这个字段映射吗？',
                okText: '确认',
                cancelText: '取消',
                onOk: () => {
                    // 从映射数据中移除该行
                    const newMappingData = mappingData.filter(item => item.ownId !== record.ownId);
                    handleMappingChange(newMappingData);
                    message.success('删除成功');
                }
            });
        };

        const typeOptions = {
            string: {text: '字符串', status: 'string'},
            number: {text: '数字', status: 'number'},
            boolean: {text: '布尔值', status: 'boolean'},
            int: {text: '整数', status: 'int'},
            double: {text: '双精度浮点数', status: 'double'},
            date: {text: '日期', status: 'date'},
            timestamp: {text: '时间戳', status: 'timestamp'},
        }


        // 构建映射表格的列
        const constructMappingColumns = () => [
            // 源数据列
            {
                title: '源字段',
                dataIndex: ['sourceTitle'],
                key: 'sourceTitle',
                editable: true,
                width: 150,
                // valueType: 'select',
                // valueEnum: typeOptions,
                // fieldProps: {
                //     showSearch: true,
                //     filterOption: (input, option) =>
                //         option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
                //     placeholder: '请选择或搜索',
                //     allowClear: true
                // }
            },
            {
                title: '源类型',
                dataIndex: ['sourceType'],
                key: 'sourceType',
                editable: true,
                valueType: 'select',
                width: 100,
                valueEnum: typeOptions,
                fieldProps: {
                    showSearch: true,
                    filterOption: (input, option) =>
                        option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
                    placeholder: '请选择或搜索',
                    allowClear: true
                }
            },

            // 映射列
            {
                title: '映射关系',
                dataIndex: 'expression',
                key: 'mapping',
                editable: false,
                align: "center",
                width: 100,
                render: (_, record) => (
                    <Button
                        type="link"
                        // onClick={() => openExpressionEditor(record)}
                        icon={<ArrowRightOutlined/>}
                    >
                        {/*{record.expression ? '表达式' : '直接映射'}*/}
                    </Button>
                )
            },

            // 目标数据列
            {
                title: '目标字段',
                dataIndex: ['targetTitle'],
                key: 'targetTitle',
                editable: true,
                width: 150,
            },
            {
                title: '目标类型',
                dataIndex: ['targetType'],
                key: 'targetType',
                editable: true,
                valueType: 'select',
                width: 100,
                valueEnum: typeOptions
            }

        ];

        // 渲染组件
        return (
            <StepsForm.StepForm
                name="shine"
                title="数据映射"
            >
                <ProCard
                    title={
                        <Space>
                            <Typography.Text strong>字段映射配置</Typography.Text>
                            <Tooltip title="同时展示来源字段和目标字段，便于配置它们之间的映射关系">
                                <InfoCircleOutlined style={{color: '#8c8c8c'}}/>
                            </Tooltip>
                        </Space>
                    }
                    extra={
                        <Space>
                            <Button
                                type="primary"
                                icon={<PlusOutlined/>}
                                onClick={handleAddMapping}
                            >
                                添加映射
                            </Button>
                            <Button
                                type="primary"
                                icon={<SyncOutlined/>}
                                onClick={autoMapFields}
                            >
                                自动映射同名字段
                            </Button>
                            <Button
                                type="primary"
                                icon={<CopyOutlined/>} // 使用“复制”图标
                                onClick={() => handleOneClickCopy('toTarget')}
                            >
                                一键复制到目标
                            </Button>
                            <Button
                                type="primary"
                                icon={<CopyOutlined/>} // 使用“复制”图标
                                onClick={() => handleOneClickCopy('toSource')}
                            >
                                一键复制到源
                            </Button>
                        </Space>
                    }
                    style={{
                        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                        borderRadius: '8px',
                        marginBottom: 24
                    }}
                >
                    <Alert
                        message="字段映射说明"
                        description="配置源表字段和目标表字段之间的映射关系，您可以拖拽行调整顺序，点击映射关系列可以设置转换表达式。"
                        type="info"
                        showIcon
                        style={{marginBottom: 16}}
                    />
                    <ProForm.Item
                        name="orginColumnJson"
                        hidden
                        initialValue={dataSource}
                        trigger="onValuesChange"
                    />
                    <ProForm.Item
                        hidden
                        name="destColumnJson"
                        initialValue={gotoDataSource}
                        trigger="onValuesChange"
                    />
                    <EditableDragableTable
                        refreshKey={refreshKey} // 添加key，强制重新渲染
                        id="ownId"
                        dataSource={[...mappingData]}
                        formColumns={constructMappingColumns()}
                        onChange={setEditableKeys}
                        editableKeys={editableKeys}
                        onValuesChange={handleMappingChange}
                    />
                </ProCard>

                {/* 表达式编辑弹窗 */}
                <Modal
                    title="设置字段转换表达式"
                    open={expressionModalVisible}
                    onOk={saveExpression}
                    onCancel={() => setExpressionModalVisible(false)}
                    width={600}
                >
                    <div style={{marginBottom: 16}}>
                        <Space align="center">
                            <Typography.Text
                                strong>{currentField?.sourceData?.title || '(未选择源字段)'}</Typography.Text>
                            <ArrowRightOutlined style={{color: '#1890ff'}}/>
                            <Typography.Text
                                strong>{currentField?.targetData?.title || '(未选择目标字段)'}</Typography.Text>
                        </Space>
                    </div>

                    <Input.TextArea
                        value={currentExpression}
                        onChange={(e) => setCurrentExpression(e.target.value)}
                        placeholder="输入转换表达式，例如: CONCAT(firstName, ' ', lastName) 或使用 ${变量名} 引用变量"
                        autoSize={{minRows: 3, maxRows: 6}}
                    />

                    <Divider style={{margin: '16px 0 8px'}}/>

                    <Typography.Text type="secondary">
                        支持SQL函数、条件表达式和动态参数引用。留空表示直接映射不做转换。
                    </Typography.Text>
                </Modal>
            </StepsForm.StepForm>
        );
    };


    const renderStepEnd = () => {
        const columns = [
            {
                title: '序列',
                textAlign: 'center',
                dataIndex: 'index',
                tooltip: '只读，作为排序',
                editable: false,
                render: (_, record, index) => index + 1,
                width: '15%',
            },
            {
                title: '任务项',
                textAlign: 'center',
                dataIndex: 'task',
                editable: true,
                width: '65%',
                valueType: 'select',
                request: async () => jobSource,
                fieldProps: (_, {rowIndex}) => ({
                    onSelect: () => {
                        console.log(rowIndex)
                    },
                    filterOption: (input, option) =>
                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
                }),
            },
            {
                title: '操作',
                textAlign: 'center',
                valueType: 'option',
            }
        ];

        return (
            <StepsForm.StepForm
                name="dispatch"
                title="调度信息"
            >
                <Row gutter={12}>
                    <Col span={10}>
                        <ProFormText
                            name="intgName"
                            label="数据同步名称"
                            initialValue=""
                            rules={[
                                {
                                    required: true, message: '请输入数据同步名称'
                                },
                            ]}
                        />
                    </Col>

                    {/* 当jobMode不是STREAMING时才显示以下调度相关字段 */}
                    {jobMode !== 'STREAMING' ?
                        <Col span={10}>
                            <ProFormText
                                name="schedulerExpr"
                                label="cron规则"
                                initialValue={cronExpression}
                                fieldProps={{
                                    suffix: <Space>
                                        <Dropdown menu={cronItemMenuProps} trigger={['click']}>
                                            <Button size="small">
                                                <Space>
                                                    常用
                                                    <DownOutlined/>
                                                </Space>
                                            </Button>
                                        </Dropdown>
                                        <CronSelect onSave={save} cronExpression={cronExpression}/>
                                    </Space>,
                                    onChange: (event) => setCron(event.target.value)
                                }}
                                rules={[
                                    {required: true, message: '请输入cron规则'},
                                    {
                                        pattern: cronRegex,
                                        message: 'cron表达式不匹配'
                                    }
                                ]}
                            />
                        </Col>
                        :
                        <Col span={10}>
                            <ProFormText
                                name="schedulerExpr"
                                label="调度规则"
                                initialValue={cronExpression}
                                disabled
                            />
                        </Col>
                    }
                </Row>

                {/* 当jobMode不是STREAMING时才显示以下调度相关字段 */}

                <>
                    <Row gutter={12}>
                        <Col span={10}>
                            <ProFormSelect
                                initialValue="FIRST"
                                options={[
                                    {
                                        value: 'FIRST',
                                        label: '第一个',
                                    },
                                    {
                                        value: 'LAST',
                                        label: '最后一个',
                                    },
                                    {
                                        value: 'RANDOM',
                                        label: '任意一个',
                                    },
                                ]}
                                name="routeStrategy"
                                label="路由策略"
                                hidden={jobMode === 'STREAMING'}
                            />
                        </Col>
                        <Col span={10}>
                            <ProFormSelect
                                initialValue="SERIAL_EXECUTION"
                                options={[
                                    {
                                        value: 'SERIAL_EXECUTION',
                                        label: '单机串行',
                                    },
                                    {
                                        value: 'DISCARD_LATER',
                                        label: '丢弃后续调度',
                                    },
                                    {
                                        value: 'COVER_EARLY',
                                        label: '覆盖之前调度',
                                    },
                                ]}
                                hidden={jobMode === 'STREAMING'}
                                name="blockStrategy"
                                label="阻塞处理"
                            />
                        </Col>
                    </Row>

                    <Row gutter={12}>
                        <Col span={10}>
                            <ProFormText
                                name="executorFailRetryCount"
                                label="失败重试次数"
                                initialValue={jobMode === "STREAMING" ? "0" : "3"}
                                hidden={jobMode === 'STREAMING'}
                                rules={[
                                    {
                                        required: true, message: '请输入失败重试次数'
                                    },
                                ]}
                            />
                        </Col>
                        <Col span={10}>
                            <ProFormText
                                name="executorTimeout"
                                label="超时时间(秒)"
                                hidden={jobMode === 'STREAMING'}
                                initialValue={jobMode === "STREAMING" ? "0" : "30"}
                                rules={[
                                    {
                                        required: true, message: '请选择时间'
                                    },
                                ]}
                            />
                        </Col>
                    </Row>
                </>


                <Row gutter={12}>
                    <ProForm.Item
                        label="子任务"
                        name="childJobId"
                        initialValue={childJobId}
                        trigger="onValuesChange"
                    >
                        <EditableProTable
                            rowKey="ownId"
                            toolBarRender={false}
                            scroll={{y: 200}}
                            columns={columns}
                            recordCreatorProps={{
                                newRecordType: 'dataSource',
                                position: 'bottom',
                                record: () => ({
                                    ownId: (Math.random() * 1000000000).toFixed(0),
                                    title: ''
                                }),
                            }}
                            editable={{
                                type: 'multiple',
                                editableKeys: [...childJobIdKeys],
                                onChange: setchildJobIdKeys,
                                actionRender: (row, _, dom) => {
                                    console.log(row)
                                    return [dom.delete];
                                },
                                onValuesChange: (record, recordList) => {
                                    setChildJobId(recordList);
                                },
                            }}
                        />
                    </ProForm.Item>
                </Row>
            </StepsForm.StepForm>
        );
    };

    const handleSubmit = async (data) => {

        let destBaseJson = childRef4?.current?.getValues() || {
            ...returnJson(allData?.destBaseJson)
        };
        let destAdvJson = childRef5?.current?.getValues() || {
            ...returnJson(allData?.destAdvJson)
        };
        let destHighJson = childRef6?.current?.getValues() || {
            ...returnJson(allData?.destHighJson)
        };
        let orginBaseJson = childRef1?.current?.getValues() || {
            ...returnJson(allData?.orginBaseJson)
        };
        let orginAdvJson = childRef2?.current?.getValues() || {
            ...returnJson(allData?.orginAdvJson)
        };
        let orginHighJson = childRef3?.current?.getValues() || {
            ...returnJson(allData?.orginHighJson)
        };


        let {advJson, baseJson, highJson, columnJson} = sourceModel;

        let {advJson: advJson1, baseJson: baseJson1, highJson: highJson1, columnJson: columnJson1} = gotoModel;
        baseJson1 = returnJson(baseJson1);
        advJson1 = returnJson(advJson1);
        highJson1 = returnJson(highJson1);
        columnJson1 = returnJson(columnJson1)
        baseJson = returnJson(baseJson);
        advJson = returnJson(advJson);
        highJson = returnJson(highJson);
        columnJson = returnJson(columnJson)

        if (baseJson1.properties) {
            destBaseJson = utils.changeFormData(baseJson1.properties, destBaseJson);
        }

        if (advJson1.properties) {
            destAdvJson = utils.changeFormData(advJson1.properties, destAdvJson);
        }

        if (highJson1.properties) {
            destHighJson = utils.changeFormData(highJson1.properties, destHighJson);
        }

        if (baseJson.properties) {
            orginBaseJson = utils.changeFormData(baseJson.properties, orginBaseJson);
        }

        if (advJson.properties) {
            orginAdvJson = utils.changeFormData(advJson.properties, orginAdvJson);
        }

        if (highJson.properties) {
            orginHighJson = utils.changeFormData(highJson.properties, orginHighJson);
        }


        let {orginColumnJson, destColumnJson, childJobId, ...destData} = data;
        console.log(orginColumnJson, destColumnJson, formRef?.current?.getFieldValue("orginColumnJson"), formRef?.current?.getFieldsValue())
        let fun;
        let ids = {};

        if (markAdd == 0) {
            fun = datasyncAdd;
        } else {
            fun = datasyncUpdate;
            ids = {id: allData.id}
        }
        if (orginColumnJson && orginColumnJson.length > 0) {
            orginColumnJson = orginColumnJson.map(item =>
                // const {ownId, ...rest} = item;
                utils.changeColumnData(columnJson, item)
            )
        }
        if (destColumnJson && destColumnJson.length > 0) {
            destColumnJson = destColumnJson.map(item => utils.changeColumnData(columnJson1, item))
        }

        // 创建字段映射 JSON
        const fieldMappingJson = [];
        if (orginColumnJson && destColumnJson && orginColumnJson.length > 0 && destColumnJson.length > 0) {
            // 取两个数组的较小长度，确保不会超出范围
            const minLength = Math.min(orginColumnJson.length, destColumnJson.length);

            for (let i = 0; i < minLength; i++) {
                fieldMappingJson.push({
                    sourceField: orginColumnJson[i].title,
                    sinkField: destColumnJson[i].title
                });
            }
        }

        // 流水线任务id
        let childJobIdStr = ""
        const childJobIdArr = []
        if (childJobId && childJobId.length > 0) {
            childJobId.map((item, index) => {
                childJobIdArr.push(item.task)
            })
            childJobIdStr = childJobIdArr.join(",")
        }
        const res = await fun({
            ...ids,
            destDatasourceName,
            orginDatasourceName,
            destBaseJson: JSON.stringify(destBaseJson),
            destAdvJson: JSON.stringify(destAdvJson),
            destHighJson: JSON.stringify(destHighJson),
            orginBaseJson: JSON.stringify(orginBaseJson),
            orginAdvJson: JSON.stringify(orginAdvJson),
            orginHighJson: JSON.stringify(orginHighJson),
            orginColumnJson: JSON.stringify(orginColumnJson),
            destColumnJson: JSON.stringify(destColumnJson),
            childJobid: childJobIdStr,
            fieldMappingJson: JSON.stringify(fieldMappingJson),
            jobMode,
            ...destData,
        });

        if (res) {
            handleSearch();
            onClose();
            setIsModalVisible(false);
            messageModal('success', markAdd == 0 ? res.message || '新增成功' : res.message || '修改成功');
        }
    };

    const setOrginColumnJsonProps = (data) => {
        formRef?.current?.setFieldsValue({
            orginColumnJson: data,
        });
    }

    const setDestColumnJsonProps = (data) => {
        formRef?.current?.setFieldsValue({
            destColumnJson: data,
        });
    }

    return (
        <>
            <Modal
                title={`${markAdd == 0 ? '新增' : '修改'}数据集成`}
                width={970}
                footer={null}
                destroyOnClose
                onCancel={() => onClose()}
                maskClosable={false}
                visible={isModalVisible}
                className={styles.modalBox}
            >
                <StepsForm
                    current={current}
                    formRef={formRef}
                    onFinish={(data) => handleSubmit(data)}
                    onCurrentChange={async (index) => {
                        // 记录上一次步骤索引
                        const prevCurrent = current;
                        await setCurrent(index);

                        // 调整索引逻辑
                        if (index == 1) {
                            // 原来的第一步 (数据来源)
                            await getSelectList(defaultSourceRadio, 0);
                        }

                        if (index == 2) {
                            // 原来的第二步 (数据去向)
                            await getSelectList(defaultGotoRadio, 1);
                        }

                        if (index == 3 && markAdd == 1) {
                            // 原来的第三步 (数据映射)
                            formRef?.current?.setFieldsValue({
                                orginColumnJson: dataSource,
                                destColumnJson: gotoDataSource,
                            });
                        }

                        if (index == 3 && markAdd == 0 && prevCurrent < index) {
                            // 同步字段逻辑保持不变，只是索引调整
                            const destBaseJson = childRef4?.current?.getValues()
                            const orginBaseJson = childRef1?.current?.getValues();

                            // 同步字段
                            const oriRes = await getTableColumns({
                                tableName: orginBaseJson.table || "",
                                dataSourceId: originDatasourceId
                            });
                            const destRes = await getTableColumns({
                                tableName: destBaseJson.table || "",
                                dataSourceId: destDatasourceId
                            });

                            const orginColumnJson = (oriRes.datasourceColumns || []).map(item => ({
                                id: item.id,
                                ownId: item.id,
                                title: item.name,
                            }));
                            const destColumnJson = (destRes.datasourceColumns || []).map(item => ({
                                id: item.id,
                                ownId: item.id,
                                title: item.name
                            }));
                            formRef?.current?.setFieldsValue({
                                orginColumnJson,
                                destColumnJson,
                            });
                            const orginIds = orginColumnJson.map(item => item.id);
                            const destIds = destColumnJson.map(item => item.id);
                            setEditableGotoRowKeys(destIds);
                            setEditableSourceRowKeys(orginIds);
                            setDataSource([...orginColumnJson]);
                            setGotoDataSource([...destColumnJson]);
                        }

                        if (index == 4 && markAdd == 0) {
                            // 原来的第四步 (调度信息)
                            formRef?.current?.setFieldsValue({
                                intgName: `${orginDatasourceName}->${destDatasourceName}`,
                                schedulerExpr: cronExpression
                            })
                        }

                        if (index != 3 && markAdd == 1) {
                            formRef?.current?.setFieldsValue({
                                ...allData,
                                childJobId
                            })
                        }
                    }}
                    stepsProps={{
                        validateMessages: {
                            required: '此项为必填项',
                        },
                    }}
                    submitter={{
                        render: (props) => {
                            // 处理模式选择步骤 (新的第一步)
                            if (props.step === 0) {
                                return [
                                    <Button type="primary" key="next" onClick={() => props.onSubmit?.()}>
                                        下一步
                                    </Button>
                                ];
                            }

                            // 数据来源步骤 (原第一步，现第二步)
                            if (props.step === 1) {
                                return [
                                    <Button key="pre" onClick={() => props.onPre?.()}>
                                        上一步
                                    </Button>,
                                    <Button
                                        type="primary"
                                        onClick={async () => {
                                            const res1 = await childRef1?.current?.submit();
                                            if (res1?.errors && res1.errors?.length > 0) {
                                                return
                                            }
                                            const res2 = childRef2?.current?.submit();
                                            if (res2?.errors && res2.errors?.length > 0) {
                                                return
                                            }
                                            const res3 = childRef3?.current?.submit();
                                            if (res3?.errors && res3.errors?.length > 0) {
                                                return
                                            }
                                            props.onSubmit?.();
                                        }}
                                    >
                                        下一步
                                    </Button>
                                ];
                            }

                            // 数据去向步骤 (原第二步，现第三步)
                            if (props.step === 2) {
                                return [
                                    <Button key="pre" onClick={() => props.onPre?.()}>
                                        上一步
                                    </Button>,
                                    <Button
                                        type="primary"
                                        key="goToTree"
                                        onClick={async () => {
                                            const res1 = await childRef4?.current?.submit();
                                            if (res1?.errors && res1.errors?.length > 0) {
                                                return
                                            }
                                            const res2 = childRef5?.current?.submit();
                                            if (res2?.errors && res2.errors?.length > 0) {
                                                return
                                            }
                                            const res3 = childRef6?.current?.submit();
                                            if (res3?.errors && res3.errors?.length > 0) {
                                                return
                                            }
                                            props.onSubmit?.();
                                        }}
                                    >
                                        下一步
                                    </Button>
                                ];
                            }

                            // 数据映射步骤 (原第三步，现第四步)
                            if (props.step === 3) {
                                return [
                                    <Button key="pre" onClick={() => props.onPre?.()}>
                                        上一步
                                    </Button>,
                                    <Button type="primary" key="goToTree" onClick={() => props.onSubmit?.()}>
                                        下一步
                                    </Button>
                                ];
                            }

                            // 调度信息步骤 (原第四步，现第五步)
                            return [
                                <Button key="pre" onClick={() => props.onPre?.()}>
                                    上一步
                                </Button>,
                                <Button type="primary" key="goToTree" onClick={() => props.onSubmit?.()}>
                                    提交
                                </Button>
                            ];
                        },
                    }}
                >
                    {renderModeSelection()}
                    {renderStepFirst}
                    {renderStepSecond}
                    {renderStepThird()}
                    {renderStepEnd()}
                </StepsForm>
            </Modal>
        </>
    );

};
