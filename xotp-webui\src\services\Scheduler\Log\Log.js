import request from '@/utils/request';
import {integratorUrl, metadataUrl, schedulerUrl} from '@/utils/baseUrl';
import * as utils from '@/utils/utils';

export function query(data) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-job/instance/jobLog/page-query`,data);
  return request(path, {
    method: 'GET',
  });
}

export function killJob(data) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-job/instance/jobLog/logKill`,data);
  return request(path, {
    method: 'GET',
  });
}

export function clearLog(data) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-job/instance/jobLog/clearLog`,data);
  return request(path, {
    method: 'GET',
  });
}

export function logDetailCat(data) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-job/instance/jobLog/logDetailCat`,data);
  return request(path, {
    method: 'GET',
  });
  // return request(`${schedulerUrl}/xbdp/jobLog/logDetailCat`, {
  //   method: 'POST',
  //   body: data
  // });
}

export function logDetail(data) {
  const path=utils.getQueryPath(`${schedulerUrl}/xbdp/jobLog/detail`,data);
  return request(path, {
    method: 'GET',
  });
}

export function jobGroups() {
  return request(`${integratorUrl}/datasync-job/instance/jobLog/jobGroupList`, {
    method: 'GET',
  });
}

export function getJobsByGroup(data) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-job/instance/jobLog/getJobsByGroup`,data);
  return request(path, {
    method: 'GET',
  });
}



