/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.client;

import com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttConnectionConfig;
import com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttSinkConfig;
import com.xmcares.platform.seatunnel.connectors.mqtt.exception.MqttConnectorErrorCode;
import com.xmcares.platform.seatunnel.connectors.mqtt.exception.MqttConnectorException;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;

public class MqttClientManager {

    private static final Logger log = LoggerFactory.getLogger(MqttClientManager.class);


    private final MqttConnectionConfig config;
    private MqttClient mqttClient;
    private final MqttConnectOptions connectionOptions;
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private final String clientId;
    private final List<String> brokerUrls;

    public List<String> getBrokerUrls() {
        return brokerUrls;
    }

    public MqttClientManager(MqttConnectionConfig config) {
        this.config = config;
        this.brokerUrls = config.getBrokerUrls();
        this.clientId = generateClientId(config);
        this.connectionOptions = buildConnectionOptions();
        initClient();
    }

    private String generateClientId(MqttConnectionConfig config) {
        if (config.getClientId() != null) {
            return config.getClientId() + UUID.randomUUID().toString().substring(0, 8);
        }
        return "seatunnel-mqtt-" + UUID.randomUUID().toString().substring(0, 8);
    }

    private void initClient() {
        try {
            // 使用第一个broker URL创建客户端，支持failover
            String primaryBrokerUrl = brokerUrls.get(0);
            this.mqttClient = new MqttClient(primaryBrokerUrl, clientId, new MemoryPersistence());

            log.info(
                    "MQTT client initialized with clientId: {}, broker: {}",
                    clientId,
                    primaryBrokerUrl);
        } catch (MqttException e) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.CLIENT_ERROR, "Failed to initialize MQTT client", e);
        }
    }

    private MqttConnectOptions buildConnectionOptions() {
        MqttConnectOptions options = new MqttConnectOptions();

        // 基础连接配置
        options.setConnectionTimeout(config.getConnectionTimeout());
        options.setKeepAliveInterval(config.getKeepAliveInterval());
        options.setCleanSession(config.isCleanSession());  // v3使用setCleanSession而不是setCleanStart
        options.setAutomaticReconnect(config.isAutoReconnect());
        if (config instanceof MqttSinkConfig) {
            options.setMaxInflight(((MqttSinkConfig) config).getMaxInflightMessages());
        }
        // 认证配置
        if (config.getUsername() != null) {
            options.setUserName(config.getUsername());
            if (config.getPassword() != null) {
                options.setPassword(config.getPassword().toCharArray());  // v3使用char[]而不是byte[]
            }
        }

        // SSL配置
        if (config.isSslEnabled()) {
            // TODO: 添加SSL配置支持
            log.info("SSL is enabled, using default SSL configuration");
        }

        // 注意：MQTT v3没有setMaxReconnectDelay方法，移除此配置
        // options.setMaxReconnectDelay(config.getMaxReconnectDelay());

        // Failover支持
        if (brokerUrls.size() > 1) {
            options.setServerURIs(brokerUrls.toArray(new String[0]));
            log.info("MQTT failover enabled with {} brokers", brokerUrls.size());
        }

        return options;
    }

    public synchronized void connect() throws MqttConnectorException {
        if (isConnected.get()) {
            return;
        }

        try {
            log.info("Connecting to MQTT broker...");
            mqttClient.connect(connectionOptions);
            isConnected.set(true);
            log.info("Successfully connected to MQTT broker with clientId: {}", clientId);
        } catch (MqttException e) {
            log.error("Failed to connect to MQTT broker", e);
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.CONNECTION_FAILED,
                    "Failed to connect to MQTT broker",
                    e);
        }
    }

    public synchronized void disconnect() {
        if (!isConnected.get()) {
            return;
        }

        try {
            if (mqttClient != null && mqttClient.isConnected()) {
                mqttClient.disconnect();
                log.info("Disconnected from MQTT broker");
            }
        } catch (MqttException e) {
            log.warn("Error during MQTT disconnect", e);
        } finally {
            isConnected.set(false);
        }
    }

    public void close() {
        disconnect();
        try {
            if (mqttClient != null) {
                mqttClient.close();
                log.info("MQTT client closed");
            }
        } catch (MqttException e) {
            log.warn("Error closing MQTT client", e);
        }
    }

    public MqttClient getClient() {
        if (!isConnected.get()) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.CLIENT_ERROR, "MQTT client is not connected");
        }
        return mqttClient;
    }

    public boolean isConnected() {
        return isConnected.get() && mqttClient != null && mqttClient.isConnected();
    }

    public String getClientId() {
        return clientId;
    }
}
