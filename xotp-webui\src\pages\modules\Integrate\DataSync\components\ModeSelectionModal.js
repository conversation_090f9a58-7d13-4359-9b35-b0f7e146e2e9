/**
 * @Description 模式选择框
 * <AUTHOR>
 * @Date   2025/7/17
 * @functionOrClass
 */
import React, { useState } from 'react';
import { Modal, Button, Space, Typography } from 'antd';
import { CheckCard } from '@ant-design/pro-components';
import { ThunderboltOutlined, CloudSyncOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const ModeSelectionModal = ({ visible, onClose, onModeSelect }) => {
    const [selectedMode, setSelectedMode] = useState('BATCH');

    const handleOk = () => {
        onModeSelect(selectedMode);
        onClose(selectedMode);
    };

    return (
        <Modal
            title="选择处理模式"
            open={visible}
            onCancel={onClose}
            width={700}
            footer={[
                <Button key="cancel" onClick={onClose}>
                    取消
                </Button>,
                <Button key="submit" type="primary" onClick={handleOk}>
                    确认
                </Button>,
            ]}
        >
            <Typography>
                <Title level={5}>请选择数据处理模式：</Title>
                <Paragraph>根据您的业务需求，选择合适的数据处理方式。</Paragraph>
            </Typography>

            <CheckCard.Group
                onChange={(value) => {
                    setSelectedMode(value);
                    console.log('已选择模式:', value);
                }}
                defaultValue="BATCH"
                style={{ width: '100%' }}
            >
                <CheckCard
                    title="批处理模式 (Batch)"
                    description="一次性处理大量数据，适合定时任务和历史数据处理。批处理提供更高的吞吐量和资源利用率。"
                    value="BATCH"
                    style={{ width: '48%', marginRight: '2%' }}
                    avatar={<ThunderboltOutlined style={{ fontSize: '28px', color: '#1890ff' }} />}
                />
                <CheckCard
                    title="流处理模式 (Streaming)"
                    description="实时处理数据流，适合需要即时反馈和低延迟场景。流处理提供更快的响应时间和连续性处理能力。"
                    value="STREAMING"
                    style={{ width: '48%' }}
                    avatar={<CloudSyncOutlined style={{ fontSize: '28px', color: '#52c41a' }} />}
                />
            </CheckCard.Group>

            <div style={{ marginTop: 20 }}>
                {selectedMode === 'BATCH' && (
                    <div className="mode-details" style={{ backgroundColor: '#f0f5ff', padding: 16, borderRadius: 8 }}>
                        <Typography>
                            <Paragraph strong>批处理模式特点：</Paragraph>
                            <ul>
                                <li>高吞吐量，适合大规模数据处理</li>
                                <li>资源利用效率高，计算成本较低</li>
                                <li>适合有明确时间窗口的处理任务</li>
                                <li>通常用于ETL、报表生成、数据分析等场景</li>
                            </ul>
                        </Typography>
                    </div>
                )}

                {selectedMode === 'STREAMING' && (
                    <div className="mode-details" style={{ backgroundColor: '#f6ffed', padding: 16, borderRadius: 8 }}>
                        <Typography>
                            <Paragraph strong>流处理模式特点：</Paragraph>
                            <ul>
                                <li>低延迟，实时数据处理和响应</li>
                                <li>连续性处理，支持实时监控和预警</li>
                                <li>适合需要即时反馈的业务场景</li>
                                <li>通常用于实时分析、监控仪表盘、异常检测等场景</li>
                            </ul>
                        </Typography>
                    </div>
                )}
            </div>
        </Modal>
    );
};


export default ModeSelectionModal;
