/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.config;

import com.xmcares.platform.seatunnel.connectors.mqtt.exception.MqttConnectorErrorCode;
import com.xmcares.platform.seatunnel.connectors.mqtt.exception.MqttConnectorException;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;



import java.io.Serializable;
import java.util.List;
import java.util.UUID;

import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.*;

/** MQTT Sink配置类 */

public class MqttSinkConfig implements Serializable, MqttConnectionConfig {

    private static final long serialVersionUID = 1L;

    // MQTT连接配置
    private List<String> brokerUrls;
    private String clientId;
    private String username;
    private String password;
    private int connectionTimeout;
    private int keepAliveInterval;
    private boolean cleanSession;
    private boolean sslEnabled;
    private boolean autoReconnect;
    private int maxReconnectDelay;

    // MQTT消息配置
    private String topic;
    private String topicPattern;
    private int qos;
    private boolean retained;
    private String messageFormat;
    private int maxInflightMessages;
    private boolean includeMetadata;

    // 关联的表信息
    private CatalogTable catalogTable;

    public static MqttSinkConfig buildConfig(ReadonlyConfig config, CatalogTable catalogTable) {
        MqttSinkConfig sinkConfig = new MqttSinkConfig();

        // 必填配置
        sinkConfig.setBrokerUrls(config.get(BROKER_URLS));

        // Topic配置 - 二选一
        // Set topic configuration (one of two options).
        // Using Optional.ifPresent for a concise check.
        config.getOptional(TOPIC).ifPresent(sinkConfig::setTopic);
        config.getOptional(TOPIC_PATTERN).ifPresent(sinkConfig::setTopicPattern);

        // Set optional connection configurations, providing default values where necessary.
        sinkConfig.setClientId(config.getOptional(CLIENT_ID).orElse(null));
        sinkConfig.setUsername(config.getOptional(USERNAME).orElse(null));
        sinkConfig.setPassword(config.getOptional(PASSWORD).orElse(null));
        sinkConfig.setConnectionTimeout(config.getOptional(CONNECTION_TIMEOUT).orElse(30));
        sinkConfig.setKeepAliveInterval(config.getOptional(KEEP_ALIVE_INTERVAL).orElse(60));
        sinkConfig.setCleanSession(config.getOptional(CLEAN_SESSION).orElse(true));
        sinkConfig.setSslEnabled(config.getOptional(SSL_ENABLED).orElse(false));
        sinkConfig.setAutoReconnect(config.getOptional(AUTO_RECONNECT).orElse(true));
        sinkConfig.setMaxReconnectDelay(config.getOptional(MAX_RECONNECT_DELAY).orElse(32000));

        // Set optional message configurations.
        sinkConfig.setQos(config.getOptional(QOS).orElse(1));
        sinkConfig.setRetained(config.getOptional(RETAINED).orElse(false));
        sinkConfig.setMessageFormat(config.getOptional(MESSAGE_FORMAT).orElse("json"));
        sinkConfig.setMaxInflightMessages(config.getOptional(MAX_INFLIGHT_MESSAGES).orElse(10));
        sinkConfig.setIncludeMetadata(config.getOptional(INCLUDE_METADATA).orElse(false));

        // Set table information.
        sinkConfig.setCatalogTable(catalogTable);

        return sinkConfig;
    }

    /** 验证配置合法性 */
    public void validate() {
        // 验证必填参数
        if (brokerUrls == null || brokerUrls.isEmpty()) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.INVALID_CONFIGURATION, "Broker URLs cannot be empty");
        }

        // 验证Topic配置
        if (topic == null && topicPattern == null) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.INVALID_CONFIGURATION,
                    "Either 'topic' or 'topic.pattern' must be specified");
        }

        if (topic != null && topicPattern != null) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.INVALID_CONFIGURATION,
                    "Cannot specify both 'topic' and 'topic.pattern'");
        }

        // 验证QoS范围
        if (qos < 0 || qos > 2) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.INVALID_CONFIGURATION,
                    "QoS must be 0, 1, or 2, but got: " + qos);
        }

        // 验证认证配置一致性
        if ((username == null) != (password == null)) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.INVALID_CONFIGURATION,
                    "Username and password must both be specified or both be null");
        }

        // 验证超时配置
        if (connectionTimeout <= 0) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.INVALID_CONFIGURATION,
                    "Connection timeout must be positive, but got: " + connectionTimeout);
        }

        if (keepAliveInterval <= 0) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.INVALID_CONFIGURATION,
                    "Keep alive interval must be positive, but got: " + keepAliveInterval);
        }

        // 验证消息格式
        if (!"json".equalsIgnoreCase(messageFormat) && !"text".equalsIgnoreCase(messageFormat)) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.INVALID_CONFIGURATION,
                    "Message format must be 'json' or 'text', but got: " + messageFormat);
        }
    }

    /** 生成默认客户端ID */
    private static String generateClientId() {
        return "new-seatunnel-mqtt-sink-"
                + System.currentTimeMillis()
                + "-"
                + Thread.currentThread().getId() + UUID.randomUUID().toString().substring(0, 8);
    }

    /** 判断是否使用动态Topic */
    public boolean isDynamicTopic() {
        return topicPattern != null;
    }

    /** 获取实际的Topic名称（支持动态替换） */
    public String resolveTopicName(String tableId, String... placeholders) {
        if (!isDynamicTopic()) {
            return topic;
        }

        String resolvedTopic = topicPattern;

        // 替换表名占位符
        if (tableId != null) {
            resolvedTopic = resolvedTopic.replace("${table}", tableId);
        }

        // 支持其他动态占位符扩展
        for (int i = 0; i < placeholders.length; i += 2) {
            if (i + 1 < placeholders.length) {
                resolvedTopic = resolvedTopic.replace(placeholders[i], placeholders[i + 1]);
            }
        }

        return resolvedTopic;
    }

    @Override
    public List<String> getBrokerUrls() {
        return brokerUrls;
    }

    public void setBrokerUrls(List<String> brokerUrls) {
        this.brokerUrls = brokerUrls;
    }

    @Override
    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    @Override
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }

    @Override
    public int getKeepAliveInterval() {
        return keepAliveInterval;
    }

    public void setKeepAliveInterval(int keepAliveInterval) {
        this.keepAliveInterval = keepAliveInterval;
    }

    @Override
    public boolean isCleanSession() {
        return cleanSession;
    }

    public void setCleanSession(boolean cleanSession) {
        this.cleanSession = cleanSession;
    }

    @Override
    public boolean isSslEnabled() {
        return sslEnabled;
    }

    public void setSslEnabled(boolean sslEnabled) {
        this.sslEnabled = sslEnabled;
    }

    @Override
    public boolean isAutoReconnect() {
        return autoReconnect;
    }

    public void setAutoReconnect(boolean autoReconnect) {
        this.autoReconnect = autoReconnect;
    }

    @Override
    public int getMaxReconnectDelay() {
        return maxReconnectDelay;
    }

    public void setMaxReconnectDelay(int maxReconnectDelay) {
        this.maxReconnectDelay = maxReconnectDelay;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getTopicPattern() {
        return topicPattern;
    }

    public void setTopicPattern(String topicPattern) {
        this.topicPattern = topicPattern;
    }

    public int getQos() {
        return qos;
    }

    public void setQos(int qos) {
        this.qos = qos;
    }

    public boolean isRetained() {
        return retained;
    }

    public void setRetained(boolean retained) {
        this.retained = retained;
    }

    public String getMessageFormat() {
        return messageFormat;
    }

    public void setMessageFormat(String messageFormat) {
        this.messageFormat = messageFormat;
    }

    public int getMaxInflightMessages() {
        return maxInflightMessages;
    }

    public void setMaxInflightMessages(int maxInflightMessages) {
        this.maxInflightMessages = maxInflightMessages;
    }

    public boolean isIncludeMetadata() {
        return includeMetadata;
    }

    public void setIncludeMetadata(boolean includeMetadata) {
        this.includeMetadata = includeMetadata;
    }

    public CatalogTable getCatalogTable() {
        return catalogTable;
    }

    public void setCatalogTable(CatalogTable catalogTable) {
        this.catalogTable = catalogTable;
    }

    public MqttSinkConfig(List<String> brokerUrls, String clientId, String username, String password, int connectionTimeout, int keepAliveInterval, boolean cleanSession, boolean sslEnabled, boolean autoReconnect, int maxReconnectDelay, String topic, String topicPattern, int qos, boolean retained, String messageFormat, int maxInflightMessages, boolean includeMetadata, CatalogTable catalogTable) {
        this.brokerUrls = brokerUrls;
        this.clientId = clientId;
        this.username = username;
        this.password = password;
        this.connectionTimeout = connectionTimeout;
        this.keepAliveInterval = keepAliveInterval;
        this.cleanSession = cleanSession;
        this.sslEnabled = sslEnabled;
        this.autoReconnect = autoReconnect;
        this.maxReconnectDelay = maxReconnectDelay;
        this.topic = topic;
        this.topicPattern = topicPattern;
        this.qos = qos;
        this.retained = retained;
        this.messageFormat = messageFormat;
        this.maxInflightMessages = maxInflightMessages;
        this.includeMetadata = includeMetadata;
        this.catalogTable = catalogTable;
    }

    public MqttSinkConfig() {
    }
}

