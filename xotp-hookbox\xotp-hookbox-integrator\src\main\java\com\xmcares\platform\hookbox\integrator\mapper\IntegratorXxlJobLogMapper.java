package com.xmcares.platform.hookbox.integrator.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmcares.platform.hookbox.integrator.model.IntegratorXxlJobLog;
import com.xmcares.platform.hookbox.integrator.model.XxlJobLogWithInstanceVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IntegratorXxlJobLogMapper extends BaseMapper<IntegratorXxlJobLog> {


    /**
     * 分页联表查询，使用正确的连接条件xl.id = dji.schedule_log_id
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页查询结果
     */
    IPage<XxlJobLogWithInstanceVO> selectJobLogWithInstancePageByLogId(
            Page<XxlJobLogWithInstanceVO> page,
            @Param(Constants.WRAPPER) Wrapper<XxlJobLogWithInstanceVO> queryWrapper
    );

//     @Autowired
//     private XxlJobLogMapper xxlJobLogMapper;
//
//     /**
//      * 根据schedule_id查询
//     */
//    public List<XxlJobLogWithInstanceVO> getByScheduleId(String scheduleId) {
//        QueryWrapper<XxlJobLogWithInstanceVO> wrapper = new QueryWrapper<>();
//        wrapper.eq("dji.schedule_id", scheduleId)
//                .orderByDesc("xl.trigger_time");
//        return xxlJobLogMapper.selectJobLogWithInstance(wrapper);
//    }
//
//    /**
//     * 根据DatasyncJobInstance id查询
//     */
//    public List<XxlJobLogWithInstanceVO> getByInstanceId(String instanceId) {
//        QueryWrapper<XxlJobLogWithInstanceVO> wrapper = new QueryWrapper<>();
//        wrapper.eq("dji.id", instanceId)
//                .orderByDesc("xl.trigger_time");
//        return xxlJobLogMapper.selectJobLogWithInstance(wrapper);
//    }
//
//    /**
//     * 复杂查询示例
//     */
//    public List<XxlJobLogWithInstanceVO> getWithComplexConditions(String scheduleId, Byte status, String startTime, String endTime) {
//        QueryWrapper<XxlJobLogWithInstanceVO> wrapper = new QueryWrapper<>();
//
//        // 根据schedule_id查询
//        // 根据状态查询
//        if (status != null) {
//            wrapper.eq("dji.status", status);
//        }
//
//        // 根据时间范围查询
//        if (startTime != null) {
//            wrapper.ge("xl.trigger_time", startTime);
//        }
//        if (endTime != null) {
//            wrapper.le("xl.trigger_time", endTime);
//        }
//
//        // 排序
//        wrapper.orderByDesc("xl.trigger_time");
//
//        return xxlJobLogMapper.selectJobLogWithInstance(wrapper);
//    }
//
//    /**
//     * 查询失败的任务
//     */
//    public List<XxlJobLogWithInstanceVO> getFailedJobs() {
//        QueryWrapper<XxlJobLogWithInstanceVO> wrapper = new QueryWrapper<>();
//        wrapper.eq("dji.status", 3) // 3表示失败
//                .or()
//                .ne("xl.handle_code", 200) // 执行状态不是成功
//                .orderByDesc("xl.trigger_time");
//        return xxlJobLogMapper.selectJobLogWithInstance(wrapper);
//    }
//
//    /**
//     * 查询正在运行的任务
//     */
//    public List<XxlJobLogWithInstanceVO> getRunningJobs() {
//        QueryWrapper<XxlJobLogWithInstanceVO> wrapper = new QueryWrapper<>();
//        wrapper.eq("dji.status", 1) // 1表示运行中
//                .orderByDesc("xl.trigger_time");
//        return xxlJobLogMapper.selectJobLogWithInstance(wrapper);
//    }
//
//    /**
//     * 根据任务ID查询
//     */
//    public List<XxlJobLogWithInstanceVO> getByJobId(String jobId) {
//        QueryWrapper<XxlJobLogWithInstanceVO> wrapper = new QueryWrapper<>();
//        wrapper.eq("dji.job_id", jobId)
//                .orderByDesc("xl.trigger_time");
//        return xxlJobLogMapper.selectJobLogWithInstance(wrapper);
//    }
//
//    /**
//     * 分页查询示例（需要配合PageHelper或MyBatis-Plus分页插件）
//     */
//    public List<XxlJobLogWithInstanceVO> getPagedResults(int pageNum, int pageSize, String scheduleId) {
//        QueryWrapper<XxlJobLogWithInstanceVO> wrapper = new QueryWrapper<>();
//
//        if (scheduleId != null && !scheduleId.trim().isEmpty()) {
//            wrapper.eq("dji.schedule_id", scheduleId);
//        }
//
//        wrapper.orderByDesc("xl.trigger_time");
//
//        // 如果使用PageHelper，在这里添加分页
//        // PageHelper.startPage(pageNum, pageSize);
//
//        return xxlJobLogMapper.selectJobLogWithInstance(wrapper);
//    }
//}

}
