package com.xmcares.platform.admin.metadata.database.service;

import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.metadata.MetadataProperties;
import com.xmcares.platform.admin.metadata.database.model.DatasourceResource;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceResourceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/22 10:08
 */
@Service
public class DatasourceResourceService {
    private static final Logger LOG = LoggerFactory.getLogger(DatasourceResourceService.class);

    @Autowired
    DatasourceResourceRepository datasourceResourceRepository;

    @Autowired
    FSTemplate fsTemplate;

    @Autowired
    private MetadataProperties properties;


    public Page<DatasourceResource> fileList(String name, Pagination pagination) {
        DatasourceResource query = new DatasourceResource();
        query.setName(name);
        List<DatasourceResource> datasourceResources = datasourceResourceRepository
                .queryPage(query, new Page<>(pagination.getPageNo() - 1, pagination.getPageSize()));
        int total = datasourceResourceRepository.count(query);
        Page<DatasourceResource> page = new Page<>();
        page.setData(datasourceResources);
        page.setTotal(total);
        page.setPageNo(pagination.getPageNo());
        page.setPageSize(pagination.getPageSize());
        return page;
    }

    /**
     * 上传资源文件到文件服务器
     *
     * @param name
     * @param remark
     * @param uploadFile
     * @return
     */
    public String uploadFile(String name, String remark, MultipartFile uploadFile) throws Exception {

        if (datasourceResourceRepository.uniqueResourceName(name) == 0) {
            String fileName = uploadFile.getOriginalFilename();

            String suffix = "";
            String[] split = fileName.split("\\.");
            if (split.length > 1) {
                suffix = "." + split[split.length - 1];
            }
            String absResourcePath = properties.getFileServerDir() + "/datasource/" + name + suffix;
            try {
                fsTemplate.saveFile(new FileDesc.FileDescImpl(null, absResourcePath), uploadFile.getInputStream());
                String id = SnowflakeGenerator.getNextStringId();
                DatasourceResource datasourceResource = new DatasourceResource();
                datasourceResource.setId(id);
                datasourceResource.setName(name);
                datasourceResource.setPath(absResourcePath);
                datasourceResource.setRemark(remark == null ? "" : remark);
                datasourceResource.setUpdateTime(new Date());
                datasourceResource.setUploadUser(UserContextHolder.getUserContext().getUsername());
                datasourceResource.setUseCount(0);
                if (datasourceResourceRepository.createResource(datasourceResource) == 1) {
                    return absResourcePath;
                } else {
                    return "";
                }

            } catch (Exception e) {
                LOG.warn(e.toString());
                return "";
            }
        } else {
            throw new Exception("资源名称重复（不包含后缀）");
        }
    }

    /**
     * 从文件服务器删除资源文件
     *
     * @param resourceId
     * @return
     */
    public Boolean deleteFile(String resourceId) throws Exception {
        DatasourceResource datasourceResource = datasourceResourceRepository.getResourceById(resourceId);
        if (datasourceResource != null) {
            if (datasourceResource.getUseCount() <= 0) {
                if (datasourceResourceRepository.deleteResource(resourceId) == 1) {
                    fsTemplate.deleteFile(datasourceResource.getPath());
                    return true;
                }
            }
            throw new BusinessException("资源已被占用");
        } else {
            return true;
        }
    }

    /**
     * 更新资源文件信息
     *
     * @param datasourceResource
     * @return
     */
    public Boolean updateResource(DatasourceResource datasourceResource) {
        return datasourceResourceRepository.updateResource(datasourceResource) == 1;
    }


}
