package com.xmcares.platform.hookbox.integrator.handler;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmcares.platform.hookbox.common.job.JobContext;
import com.xmcares.platform.hookbox.common.job.context.JobContextImpl;
import com.xmcares.platform.hookbox.common.job.seatunnel.RemoteSeaTunnelJobContextService;
import com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance;
import com.xmcares.platform.hookbox.integrator.model.IntegratorXxlJobLog;
import com.xmcares.platform.hookbox.integrator.model.JobLogVO;
import com.xmcares.platform.hookbox.integrator.model.XxlJobLogWithInstanceVO;
import com.xmcares.platform.hookbox.integrator.model.XxlJobLogQueryDTO;
import com.xmcares.platform.hookbox.integrator.service.DatasyncJobInstanceService;
import com.xmcares.platform.hookbox.integrator.service.IntegratorXxlJobLogService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * XxlJobDatasyncLogHandler
 *
 * <AUTHOR>
 * @Descriptions 数据同步作业日志处理器
 * @Date 2025/7/23 10:33
 */
@RestController
@RequestMapping("/hookbox/datasync/log")
public class XxlJobDatasyncLogHandler {

    @Autowired
    private DatasyncJobInstanceService datasyncJobInstanceService;

    @Autowired
    private IntegratorXxlJobLogService integratorXxlJobLogService;

    private final RemoteSeaTunnelJobContextService seaTunnelJobService = new RemoteSeaTunnelJobContextService();

    @RequestMapping(value = "/get-logs/{jobInstanceId}", method = {RequestMethod.GET, RequestMethod.POST})
    @ApiOperation(value = "根据任务实例ID获取聚合日志")
    public JobLogVO getLogsByInstanceId(
            @ApiParam(value = "任务实例的唯一ID", required = true)
            @PathVariable String jobInstanceId) {

        JobLogVO logVO = new JobLogVO();

        // 1. 根据 jobInstanceId 查找任务实例记录
        DatasyncJobInstance instance = datasyncJobInstanceService.getById(jobInstanceId);

        if (instance == null) {
            String errorMessage = "未找到对应的任务实例，请检查instanceId。";
            logVO.setXxlJobLog(errorMessage);
            logVO.setXxlJobDetailLog(errorMessage);
            logVO.setSeaTunnelLog(errorMessage);
            logVO.setReceivedCount(0);
            logVO.setReceviedQps(0d);
            logVO.setWritedCount(0);
            logVO.setWritedQps(0d);
            return logVO;
        }

        logVO.setReceivedCount(instance.getReceivedCount());
        logVO.setReceviedQps(instance.getReceviedQps());
        logVO.setWritedCount(instance.getWritedCount());
        logVO.setWritedQps(instance.getWritedQps());

        // 2. 获取 xxl-job 日志ID (scheduleLogId)
        String scheduleLogId = instance.getScheduleLogId();
        if (scheduleLogId == null || scheduleLogId.isEmpty()) {
            String errorMessage = "任务实例中未记录 scheduleLogId，无法查询XXL-Job日志。";
            logVO.setXxlJobLog(errorMessage);
            logVO.setXxlJobDetailLog(errorMessage);
            // 仍然尝试查询SeaTunnel日志
            logVO.setSeaTunnelLog(fetchSeaTunnelLog(instance));
            return logVO;
        }

        // 3. 查询 XXL-Job 日志元数据
        IntegratorXxlJobLog xxlJobLog = integratorXxlJobLogService.getById(Long.parseLong(scheduleLogId));
        if (xxlJobLog == null) {
            String errorMessage = "根据 schedule_id [" + scheduleLogId + "] 未找到XXL-Job日志记录。";
            logVO.setXxlJobLog(errorMessage);
            logVO.setXxlJobDetailLog(errorMessage);
            logVO.setSeaTunnelLog(fetchSeaTunnelLog(instance));
            return logVO;
        }

        // 4. 填充各类日志到VO中
        // 4.1 填充XXL-Job摘要日志
        StringBuilder summaryLog = new StringBuilder();
        summaryLog.append("调度结果: \n").append(xxlJobLog.getTriggerMsg());
        summaryLog.append("\n\n");
        summaryLog.append("执行结果: \n").append(xxlJobLog.getHandleMsg());
        logVO.setXxlJobLog(summaryLog.toString());

        // 4.2 **调用Service层方法，获取并填充XXL-Job详细过程日志**
        logVO.setXxlJobDetailLog(integratorXxlJobLogService.fetchXxlJobDetailLog(xxlJobLog));

        // 4.3 获取并填充SeaTunnel日志
        logVO.setSeaTunnelLog(fetchSeaTunnelLog(instance));

        return logVO;
    }

    /**
     * 封装获取 SeaTunnel 日志的逻辑
     * @param instance 任务实例对象
     * @return SeaTunnel 日志字符串
     */
    private String fetchSeaTunnelLog(DatasyncJobInstance instance) {
        JobContext jobContext = new JobContextImpl(instance.getJobId(), instance.getId(), "datasync-job");
        return seaTunnelJobService.getJobExecutionLog(jobContext);
    }

    @PostMapping("/page-query")
    @ApiOperation(value = "分页查询XXL-Job日志与任务实例关联数据",
                  notes = "支持多种查询条件的分页查询，使用正确的xl.id=dji.schedule_log_id连接条件")
    public IPage<XxlJobLogWithInstanceVO> pageQuery(
            @ApiParam(value = "页码，从1开始", defaultValue = "1")
            @RequestParam(value = "current", defaultValue = "1") Integer current,

            @ApiParam(value = "每页条数，范围1-100", defaultValue = "10")
            @RequestParam(value = "size", defaultValue = "10") Integer size,

            @ApiParam(value = "查询条件DTO", required = false)
            @RequestBody(required = false) XxlJobLogQueryDTO queryDTO) {

        // 构建分页对象
        Page<XxlJobLogWithInstanceVO> page = new Page<>(current, size);

        // 构建动态查询条件
        QueryWrapper<XxlJobLogWithInstanceVO> queryWrapper =
            integratorXxlJobLogService.buildQueryWrapper(queryDTO);

        // 调用Service层分页查询方法
        return integratorXxlJobLogService.pageQueryJobLogsWithInstance(page, queryWrapper);
    }

}

