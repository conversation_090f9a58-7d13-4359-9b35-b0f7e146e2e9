package com.xmcares.platform.hookbox;

import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.platform.hookbox.core.properties.HookboxProperties;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class HookboxContext implements InitializingBean, DisposableBean {

    private static HookboxContext CONTEXT;

    public static HookboxContext getInstance() {
        return CONTEXT;
    }

    @Autowired
    private FSTemplate fsTemplate;

    @Autowired
    private HookboxProperties properties;

    @Override
    public void destroy() throws Exception {

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        CONTEXT = this;
    }


    public FSTemplate getFsTemplate() {
        return fsTemplate;
    }

    public HookboxProperties getProperties() {
        return properties;
    }
}




