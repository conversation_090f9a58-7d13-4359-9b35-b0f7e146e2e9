/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/1
 */
package com.xmcares.platform.hookbox.integrator.service;

import com.xmcares.platform.hookbox.integrator.model.DatasyncJob;
import com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public class DatasyncJobService {
    public DatasyncJob getDatasyncJobById(String jobId) {
        return null;
    }

    /**
     * 获取正在运行的同步的任务实例
     * @return
     */
    public List<DatasyncJobInstance> findRunningInstances() {
        return null;
    }

    /**
     * 获取正在运行的同步的任务实例
     * @param jobId 任务ID
     * @return 运行中的任务实例
     */
    public List<DatasyncJobInstance> findRunningInstancesByJobId(String jobId) {

        return null;
    }

    public void insertJobInstance(DatasyncJobInstance jobInstance) {
        //TODO: 插入任务实例
    }
}
