import React, { PureComponent, useEffect, useState } from 'react';
import { connect } from 'dva';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { DeleteOutlined, DownloadOutlined, PlusCircleOutlined, DownOutlined, EditOutlined, MenuOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Input,
  InputNumber,
  Modal,
  Row,
  Select,
  Tooltip,
  Table,
  Tree,
} from 'antd';

import PageHeaderWrapper from '@/components/PageHeaderWrapper/index';
import CommonTable from '@/components/CommonTable/index';
import * as utils from '@/utils/utils';
import { messageModal } from '@/utils/messageModal';

import TransferModal from "@/pages/modules/Metadata/DataSourceTable/components/TransferModal";
import ModalForm from "@/pages/modules/Metadata/DataSource/components/ModalForm";
import styles from './DataSourceTable.less';

let markPage=1,pageSize=10;
const modelsName = 'dataSourceTable';
const FormItem = Form.Item;
const { TextArea } = Input;
const confirm = Modal.confirm;
const { Option } = Select;

let columnTypesAll = [
  {
    "key":"mysql",
    "content":[
      {key:"VARCHAR",name:"VARCHAR"},
      {key:"INT",name:"INT"},
      {key:"DATETIME",name:"DATETIME"},
      {key:"NUMBER",name:"NUMBER"},
    ]
  },
  {
    "key":"oracle",
    "content":[
      {key:"VARCHAR2",name:"VARCHAR2"},
      {key:"BIGINT",name:"BIGINT"},
      {key:"TIMESTAMP",name:"TIMESTAMP"},
      {key:"NUMBER",name:"NUMBER"},
    ]
  }
];

const columnOks = [
  {key:"1",name:"是"},
  {key:"0",name:"否"},
];

@connect(({ dataSourceTable, tenantManager, deptManager, role, userRole, menu, loading }) => ({
  dataSourceTable,
  tenantManager,
  deptManager,
  role,
  userRole,
  menu,
  loading: loading.models.dataSourceTable,
}))
@Form.create()

export default class DataSourceTable extends PureComponent {

  state = {
    selectedRows: undefined,
    selectedRowKeys: undefined,
    modalVisible: false,
    modalTitle: '',
    dataSourceList: [],
    dataSourceId: '', //选中的数据源
    syncedTableList:[],
    tableId: '', //选中的表
    tableInfo: {}, //表信息,如表昵称,备注等
    columnsInfo: [], //表字段信息
    columnTypes: [], //表字段类型
  };

  /**
   * 用户列表(第一次渲染后调用)
   */
  componentDidMount() {
    console.log("componentDidMount");
    this.queryDataSourceList();
    const { location} = this.props;
    const {dataSourceId} = location.query;
    if(dataSourceId) {
      this.setState({
        dataSourceId: dataSourceId,
      });
      this.querySyncedTableList(dataSourceId);
    }
  }

  //查询数据源的下拉列表
  queryDataSourceList() {
    const {dispatch} = this.props;
    dispatch({
      type: `${modelsName}/queryDataSourceList`,
      callback: (body) => {
        let result = body.filter((item) => item.category==='JDBC');
        this.setState({
          dataSourceList: result,
        });
      }
    });
  }

  //查询数据表的树列表
  querySyncedTableList = (dataSourceId) => {
    const {dispatch} = this.props;
    dispatch({
      type: `${modelsName}/queryTableList`,
      payload:dataSourceId,
      callback: (body) => {
        let syncedTableList = body.map((item)=>{
          let itemNew = {};
          itemNew.title = item.name;
          itemNew.key = item.id;
          return itemNew;
        });
        this.setState({
          syncedTableList: syncedTableList,
        });
        this.resetSelectTableData();
      }
    });
  }

  //树节点选中事件
  treeClick = (selectKey, info) => {
    //取消选中节点
    if(selectKey.length===0) {
      this.setState({
        tableId: '',
      });
      return;
    }
    //选中某个节点
    this.setState({
      tableId: selectKey[0],
    });
    const {dispatch, form} = this.props;
    dispatch({
      type: `${modelsName}/queryTableDetails`,
      payload: selectKey[0],
      callback: (body) => {
        //设置基础信息输入框
        if(body.datasourceTable) {
          form.setFieldsValue({
            alias:body.datasourceTable.alias,
            remark: body.datasourceTable.remark,
          });
          this.setState({
            tableInfo: body.datasourceTable,
          })
        }
        if(body.datasourceColumns){
          this.setColumnInfos(body.datasourceColumns)
        }
        this.resetSelectRowData();
      }
    });
  }

  //设置列信息
  //支持列信息排序
  setColumnInfos(columns) {
    let finalData = [];
    for(let i=0; i<columns.length; i++) {
      columns[i].index = i;
      columns[i].key = columns[i].id;
      finalData.push(columns[i]);
    }
    this.setState({
      columnsInfo: finalData,
    })
  }

  //数据源下拉框选中事件
  selectHandleChange = (value) => {
    this.setState({
      dataSourceId: value,
    });
    console.log("当前选中的数据源："+value);
    //不同数据库设置不同字段类型
    this.setFieldTypes(value);
    this.querySyncedTableList(value);
  };

  //不同数据库设置不同字段类型
  setFieldTypes(value) {
    const {dataSourceList} = this.state;
    let type = '';
    let columnTypes = [];
    for(let i=0; i<dataSourceList.length; i++) {
      if(dataSourceList[i].id === value) {
        type = dataSourceList[i].type;
      }
    }
    for(let j=0; j<columnTypesAll.length; j++) {
      if(columnTypesAll[j].key === type) {
        columnTypes = columnTypesAll[j].content;
      }
    }
    this.setState({
      columnTypes: columnTypes
    })
  }

  //弹出框：同步数据表
  sync() {
    if(this.checkDataSourceSelected()===false) return;
    const {dispatch} = this.props;
    dispatch({
      type: `${modelsName}/queryUnSyncTables`,
      payload:this.state.dataSourceId,
      callback: (body) => {
        if(body&&body?.unSyncTables){
          this.transferRef.showItems(body);
        }

      }
    });
    this.transferRef.toggleVisible(true)
  }

  //删除同步的数据表
  delete() {
    if(this.checkDataSourceAndTableSelected()===false) return;
    const {dispatch} = this.props;
    const {dataSourceId, tableId} = this.state;
    const topThis = this;
    confirm({
      title: '确定删除?',
      content: '删除该同步表',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        dispatch({
          type: `${modelsName}/deleteTable`,
          payload: tableId,
          callback: () => {
            //重新加载树列表
            topThis.querySyncedTableList(dataSourceId);
          }
        });
      },
    });
  }

  //数据源是否已选择
  checkDataSourceSelected() {
    if(this.state.dataSourceId==='' || typeof(this.state.dataSourceId) == "undefined") {
      messageModal('error','请选择数据源');
      return false;
    }
  }
  //数据表是否已选择
  checkTableSelected() {
    if(this.state.tableId==='' || typeof(this.state.tableId) == "undefined") {
      messageModal('error','请选择数据表');
      return false;
    }
  }
  //数据源和数据表是否已选择
  checkDataSourceAndTableSelected() {
    if(this.checkDataSourceSelected()===false) return false;
    if(this.checkTableSelected()===false) return false;
  }
  //数据列是否已选中
  checkColumnSelected() {
    const { selectedRows } = this.state;
    if( typeof(selectedRows) == "undefined" || selectedRows.length===0 ) {
      messageModal('error','请选择数据列');
      return false;
    }
  }
  validateColumns() {
    const { columnsInfo } = this.state;
    for(let i=0; i<columnsInfo.length; i++) {
      let column = columnsInfo[i];
      if(column.name ==='' || typeof(column.name) == "undefined") {
        messageModal('error','列名称不能为空');
        return false;
      }
      if(column.type ==='' || typeof(column.type) == "undefined") {
        messageModal('error','列类型不能为空');
        return false;
      }
      if(column.length ==='' || typeof(column.length) == "undefined") {
        messageModal('error','列长度不能为空');
        return false;
      }else{
        if(this.validateNumber(column.length) === false){
          messageModal('error','列长度必须为正整数');
          return false;
        }
      }
      if(column.accuracy ==='' || typeof(column.accuracy) == "undefined") {
        messageModal('error','列精度不能为空');
        return false;
      }else{
        if(this.validateNumber(column.accuracy) === false){
          messageModal('error','列精度必须为正整数');
          return false;
        }
      }
      if(column.allowEmpty ==='' || typeof(column.allowEmpty) == "undefined") {
        messageModal('error','是否NULL不能为空');
        return false;
      }
      if(column.columnKey ==='' || typeof(column.columnKey) == "undefined") {
        messageModal('error','是否主键不能为空');
        return false;
      }
    }
  }
  //必须为数字
  validateNumber(value) {
    let regResult = true;
    let reg = /^[+]{0,1}(\d+)$/;
    if(!reg.test(value)){
      regResult = false;
    }
    return regResult;
  }


  //表基础信息提交
  baseInfoSubmit() {
    if(this.checkDataSourceAndTableSelected()===false) return;
    const {form, dispatch} = this.props;
    const {tableInfo} = this.state;
    form.validateFields((err, values) => {
      if (!err) {
        let data = {...tableInfo};
        data.alias = values.alias;
        data.remark = values.remark;
        dispatch({
          type: `${modelsName}/updateTableBaseInfo`,
          payload: data,
          callback: (body) => {
            if(body===true) {
              messageModal('success','提交成功');
            }else{
              messageModal('error','提交失败');
            }
          }
        });
      }
    });
  }

  //同步字段
  syncField() {
    if(this.checkDataSourceAndTableSelected()===false) return;
    const {dispatch} = this.props;
    const {columnsInfo, tableId} = this.state;
    //去除系统列
    let columnsInfoNew = [];
    for(let i=0; i<columnsInfo.length; i++) {
      if(columnsInfo[i].hasSystem === '0'){
        columnsInfoNew.push(columnsInfo[i])
      }
    }
    dispatch({
      type: `${modelsName}/findSyncFields`,
      payload: tableId,
      callback: (body) => {
        //添加系统列
        columnsInfoNew = columnsInfoNew.concat(body);
        this.setColumnInfos(columnsInfoNew);
        messageModal('success','同步成功');
      }
    });
  }

  //删除列
  columnDelete() {
    if(this.checkDataSourceAndTableSelected()===false) return;
    if(this.checkColumnSelected()===false) return;
    const { columnsInfo, selectedRows } = this.state;
    let columnsInfoNew = [];
    for(let i=0; i<columnsInfo.length; i++) {
      if(columnsInfo[i].id !== selectedRows[0].id) {
        columnsInfoNew.push(columnsInfo[i]);
      }
    }
    this.setColumnInfos(columnsInfoNew)
    this.resetSelectRowData();
  }

  //保存所有列
  columnsSave() {
    if(this.checkDataSourceAndTableSelected()===false) return;
    if(this.validateColumns()===false) return;
    const {dispatch} = this.props;
    const {columnsInfo, tableId} = this.state;
    let data = {};
    data.tableId = tableId;
    data.columns = columnsInfo;
    dispatch({
      type: `${modelsName}/saveFields`,
      payload: data,
      callback: (body) => {
        messageModal('success','保存成功');
      }
    });
  }

  //新增列
  columnCreate() {
    if(this.checkDataSourceAndTableSelected()===false) return;
    const {columnsInfo} = this.state;
    let columnsInfoNew = columnsInfo;
    columnsInfoNew.push(this.newColumn());
    this.setColumnInfos(columnsInfoNew);
  }

  //插入列
  columnInsert() {
    if(this.checkDataSourceAndTableSelected()===false) return;
    if(this.checkColumnSelected()===false) return;
    const {columnsInfo, selectedRows} = this.state;
    let columnsInfoNew = [];
    for(let i=0; i<columnsInfo.length; i++) {
      if(columnsInfo[i].id === selectedRows[0].id) {
        columnsInfoNew.push(columnsInfo[i]);
        columnsInfoNew.push(this.newColumn());
      }else{
        columnsInfoNew.push(columnsInfo[i]);
      }
    }
    this.setColumnInfos(columnsInfoNew);
  }

  newColumn() {
    const {tableInfo} = this.state;
    let newColumn = {};
    newColumn.datatableId = tableInfo.id;
    newColumn.datatableName = tableInfo.name;
    newColumn.datasourceId = tableInfo.datasourceId;
    newColumn.name = '';
    newColumn.alisa = '';
    newColumn.type = '';
    newColumn.length = '0';
    newColumn.accuracy = '0';
    newColumn.allowEmpty = '1';
    newColumn.columnKey = '0';
    newColumn.hasSystem = '0';
    newColumn.remark = '';
    newColumn.id = this.uuid();
    return newColumn;
  }

  uuid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      var r = Math.random() * 16 | 0,
        v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  //重置页面数据
  resetSelectTableData() {
    //重置选中的树节点
    this.setState({
      tableId: '',
    });
    this.resetTableBaseInfo();
    this.resetSelectRowData();
  }
  resetSelectRowData() {
    this.setState({
      selectedRows: undefined,
      selectedRowKeys: undefined,
    })
  }
  resetTableBaseInfo() {
    const {form} = this.props;
    form.setFieldsValue({
      alias:'',
      remark: '',
    });
    this.setState({
      tableInfo: {},
      columnsInfo: [],
    })
  }

  //列编辑控制
  handleColumnInputChange = (value, record) => {
    for (var i in value) {
      record[i] = value[i];//这一句是必须的，不然状态无法更改
      this.setState({
        columnsInfo: this.state.columnsInfo.map((item, key) => item.id == record.id ? {...item, [i]: value[i]} : item)
      })
    }
  }

  //列上移下移
  columnMove(type) {
    if(this.checkDataSourceAndTableSelected()===false) return;
    if(this.checkColumnSelected()===false) return;
    const { selectedRows, columnsInfo } = this.state;
    let index = selectedRows[0].index;
    let arr = columnsInfo;
    if(type==='down') {
      this.downData(arr, index);
    }else{
      this.upData(arr, index);
    }
    this.setColumnInfos(arr);
  }
  //上移
  upData(arr, index) {
    let newArr = []
    if (arr.length > 1 && index !== 0) {
      newArr = this.swapItems(arr, index, index - 1)
    }
    return newArr
  }
  //下移
  downData(arr, index) {
    let newArr = []
    if (arr.length > 1 && index !== (arr.length - 1)) {
      newArr = this.swapItems(arr, index, index + 1)
    }
    return newArr
  }
  //移动
  swapItems(arr, index1, index2) {
    arr[index1] = arr.splice(index2, 1, arr[index1])[0]
    return arr
  }

  renderForm() {
    const { form } = this.props;
    const { getFieldDecorator } = this.props.form;
    const { tableInfo } = this.state;
    return (
      <Form>
        <Row gutter={{ md: 4, lg: 12, xl: 24 }}>
          <Col md={5} sm={24}>
            <FormItem label="表别称">
              {getFieldDecorator('alias', {
                initialValue: tableInfo.alias,
                rules: [
                  { max: 20, message: '最多可输入20字' },
                ],
              })(<Input placeholder="请输入表别称" onBlur={utils.valToTrim.bind(this, 'alias', form)} allowClear={true}/>)}
            </FormItem>
          </Col>

          <Col xxl={5} xl={8} sm={9}>
            <FormItem label="表">
              {tableInfo.name}
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4, lg: 12, xl: 24 }} style={{ marginTop: '-20px' }}>
          <Col xxl={12} xl={12} sm={12}>
            <FormItem  label="备&nbsp;&nbsp;&nbsp;注">
              {form.getFieldDecorator('remark', {
                initialValue: tableInfo.remark,
                rules: [
                  { max: 100, message: '最多可输入100字' },
                ],
              })(<TextArea placeholder="请输入描述" style={{ minHeight: 16 }} rows={2} onBlur={utils.valToTrim.bind(this, 'remark', form)} allowClear={true}/>)}
            </FormItem>
          </Col>
        </Row>
      </Form>
    );
  }

  render() {

    const { selectedRows, dataSourceList, transferModelVisible, syncedTableList, selectedRowKeys, columnsInfo, columnTypes, tableId } = this.state;
    const { dataSourceTable: { data, loading }, menu: { currentBtnArray }, dispatch, location } = this.props;
    const {dataSourceId} = location.query;

    //选中的表
    let treeSelectedKeys = [];
    treeSelectedKeys.push(tableId);


    //行选中
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      this.setState({
        selectedRows: selectedRows,
        selectedRowKeys:selectedRowKeys,
      })
    };
    const rowSelection = {
      selectedRowKeys,
      onChange: onSelectChange,
    };

    //行定义
    const columns = [
      {
        title: '列名称',
        dataIndex: 'name',
        width: '12%',
        render: (text, record, index) => {
          return (
            <div className={styles.resultColumnsDiv}>
              <Input placeholder="请输入列名称" value={record.name} onChange={(e) => this.handleColumnInputChange({name: e.target.value}, record)}/>
            </div>

          );
        },
      },
      {
        title: '列别名',
        dataIndex: 'alisa',
        width: '12%',
        render: (text, record, index) => {
          return (
            <div className={styles.resultColumnsDiv}>
              <Input placeholder="请输入列别名" value={record.alisa} onChange={(e) => this.handleColumnInputChange({alisa: e.target.value}, record)}/>
            </div>
          );
        },
      },
      {
        title: '列类型',
        dataIndex: 'type',
        width: '10%',
        render: (text, record, index) => {
          if(record.hasSystem==='1'){
            return (
              <Tooltip title={text}>
                <div className={styles.resultColumnsDiv}>{text}</div>
              </Tooltip>
            );
          }
          return (
            <div className={styles.resultColumnsDiv}>
              <Select style={{width: '100%'}}
                      defaultValue={text}
                      value={record.type} onChange={(e) => this.handleColumnInputChange({type: e}, record)}
                      placeholder="请选择列类型"
              >
                {columnTypes && columnTypes.map(item => (
                  <Select.Option value={item.key} key={item.key}>{item.key}</Select.Option>
                ))}
              </Select>
            </div>
          );
        },
      },
      {
        title: '列长度',
        dataIndex: 'length',
        width: '10%',
        render: (text, record, index) => {
          if(record.hasSystem==='1'){
            return (
              <Tooltip title={text}>
                <div className={styles.resultColumnsDiv}>{text}</div>
              </Tooltip>
            );
          }
          return (
            <div className={styles.resultColumnsDiv}>
              <Input placeholder="请输入列长度" value={record.length} onChange={(e) => this.handleColumnInputChange({length: e.target.value}, record)}/>
            </div>
          );
        },
      },
      {
        title: '列精度',
        dataIndex: 'accuracy',
        width: '10%',
        render: (text, record, index) => {
          if(record.hasSystem==='1'){
            return (
              <Tooltip title={text}>
                <div className={styles.resultColumnsDiv}>{text}</div>
              </Tooltip>
            );
          }
          return (
            <div className={styles.resultColumnsDiv}>
              <Input placeholder="请输入列精度" value={record.accuracy} onChange={(e) => this.handleColumnInputChange({accuracy: e.target.value}, record)}/>
            </div>
          );
        },
      },
      {
        title: '是否NULL',
        dataIndex: 'allowEmpty',
        width: '10%',
        render: (text, record, index) => {
          if(record.hasSystem==='1'){
            if(text==='1') {
              text = '是';
            }else{
              text = '否';
            }
            return (
              <Tooltip title={text}>
                <div className={styles.resultColumnsDiv}>{text}</div>
              </Tooltip>
            );
          }
          return (
            <div className={styles.resultColumnsDiv}>
              <Select style={{width: '100%'}}
                      defaultValue={text}
                      value={record.allowEmpty} onChange={(e) => this.handleColumnInputChange({allowEmpty: e}, record)}
                      placeholder="请选择"
              >
                {columnOks && columnOks.map(item => (
                  <Select.Option value={item.key} key={item.key}>{item.name}</Select.Option>
                ))}
              </Select>
            </div>
          );
        },
      },
      {
        title: '是否主键',
        dataIndex: 'columnKey',
        width: '10%',
        render: (text, record, index) => {
          if(record.hasSystem==='1'){
            if(text==='1') {
              text = '是';
            }else{
              text = '否';
            }
            return (
              <Tooltip title={text}>
                <div className={styles.resultColumnsDiv}>{text}</div>
              </Tooltip>
            );
          }
          return (
            <div className={styles.resultColumnsDiv}>
              <Select style={{width: '100%'}}
                      defaultValue={text}
                      value={record.columnKey} onChange={(e) => this.handleColumnInputChange({columnKey: e}, record)}
                      placeholder="请选择"
              >
                {columnOks && columnOks.map(item => (
                  <Select.Option value={item.key} key={item.key}>{item.name}</Select.Option>
                ))}
              </Select>
            </div>
          );
        },
      },
      {
        title: '是否系统列',
        dataIndex: 'hasSystem',
        width: '10%',
        render: (text, record, index) => {
          if(text==='1') {
            text = '是';
          }else{
            text = '否';
          }
          return (
            <Tooltip title={text}>
              <div className={styles.resultColumnsDiv}>{text}</div>
            </Tooltip>
          );
        },
      },
      {
        title: '列注释',
        dataIndex: 'remark',
        render: (text, record, index) => {
          if(record.hasSystem==='1'){
            return (
              <Tooltip title={text}>
                <div className={styles.resultColumnsDiv}>{text}</div>
              </Tooltip>
            );
          }
          return (
            <div className={styles.resultColumnsDiv}>
              <Input placeholder="请输入列注释" value={record.remark} onChange={(e) => this.handleColumnInputChange({remark: e.target.value}, record)}/>
            </div>
          );
        },
      },
    ];

    //同步表穿梭框
    const transferMethods = {
      transferModelTitle: '同步表',
      dispatch,
      querySyncedTableList: this.querySyncedTableList,
    };

    return (
      <PageHeaderWrapper>
        <Card bordered={false} style={{marginLeft:'20px'}}>
        {/*上*/}
        <Row>
          <Col span={5}>
            <Row>
              <Col span={5} style={{fontSize: 14, marginTop:'5px'}}>
                数据源：
              </Col>
              <Col span={19}>
                <Select style={{width: '100%'}}
                        defaultValue={dataSourceId}
                        showSearch
                        filterOption={(input, option) =>
                            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                        onChange={this.selectHandleChange}
                        placeholder="请先选择数据源"
                >
                  {dataSourceList && dataSourceList.map(item => (
                    <Select.Option value={item.id} key={item.id}>{item.name}</Select.Option>
                  ))}
              </Select>
              </Col>
            </Row>
          </Col>
        </Row>

        {/*下*/}
        <Row style={{marginTop:'20px'}}>
          {/*左*/}
          <Col span={5}>
            <Row style={{fontSize: 20, fontWeight:'bold'}}>
              <Col span={4} >
                表：
              </Col>
              <Col span={20} style={{textAlign:'left'}}>
                <span>
                  <a onClick={() => this.sync(true)}>
                    <DownloadOutlined style={{ color: '#40a9ff' }} />
                  </a>
                </span>&nbsp;&nbsp;&nbsp;&nbsp;
                <span>
                  <a onClick={() => this.delete()}>
                    <DeleteOutlined style={{ color: '#d20c23' }} />
                  </a>
                </span>
              </Col>
            </Row>
            <Row style={{marginTop:'10px', paddingRight:'20px'}}>
              <Col span={24} >
                <Tree
                  showIcon
                  blockNode={true}
                  defaultExpandAll
                  defaultSelectedKeys={['0-0-0']}
                  switcherIcon={<DownOutlined />}
                  treeData={syncedTableList}
                  onSelect={this.treeClick}
                  selectedKeys={treeSelectedKeys}
                />
              </Col>
            </Row>
          </Col>
          {/*右*/}
          <Col span={19} >

              <div>
                {/*右上*/}
                <Row>
                  <Col span={6}><span style={{fontSize: 20, fontWeight:'bold'}}>基础信息</span></Col>
                  <Col span={18} style={{textAlign:'right', marginTop:'5px'}}>
                    <span>
                      <Button type="primary" onClick={()=>{this.baseInfoSubmit()}}>提交</Button>
                    </span>
                  </Col>
                </Row>
                <div className={styles.tableListForm}>{this.renderForm()}</div>
                {/*右下*/}
                <Row style={{marginTop:'20px',paddingBottom:'10px'}}>
                  <Col span={6}><span style={{fontSize: 20, fontWeight:'bold'}}>列信息</span></Col>
                  <Col span={18} style={{textAlign:'right'}}>
                    <Button type="primary" style={{marginLeft: 8}} onClick={()=>{this.syncField()}}>同步字段</Button>
                    <Button type="primary" style={{marginLeft: 8}} onClick={()=>{this.columnMove('up')}}>上移</Button>
                    <Button type="primary" style={{marginLeft: 8}} onClick={()=>{this.columnMove('down')}}>下移</Button>
                    <Button type="primary" style={{marginLeft: 8}} onClick={()=>{this.columnDelete()}}>删除字段</Button>
                    <Button type="primary" style={{marginLeft: 8}} onClick={()=>{this.columnsSave()}}>保存</Button>
                  </Col>
                </Row>
                <Row style={{paddingBottom:'10px'}}>
                  <Col span={24}>
                    <Table
                      pagination={false}
                      dataSource={columnsInfo}
                      columns={columns}
                      rowKey="key"
                      components={{
                        body: {
                          //wrapper: DraggableContainer,
                          //row: DraggableBodyRow,
                        },
                      }}
                      rowSelection={{
                        type: 'radio',
                        ...rowSelection,
                      }}
                    />
                  </Col>
                </Row>
              </div>
          </Col>
        </Row>
      </Card>
      <TransferModal key={"transferModal"} ref={(inst) => this.transferRef = inst} {...transferMethods} transferModelVisible={transferModelVisible} />
      </PageHeaderWrapper>
    );
  }
}
