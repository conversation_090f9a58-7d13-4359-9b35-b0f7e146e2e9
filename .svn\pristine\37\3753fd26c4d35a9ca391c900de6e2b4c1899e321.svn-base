/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/3/2
 */
package com.xmcares.platform.admin.integrator.datasync.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmcares.platform.admin.integrator.datasync.dto.DatasyncDto;
import com.xmcares.platform.admin.integrator.datasync.dto.JobLogPageListDto;
import com.xmcares.platform.admin.integrator.datasync.dto.XxlJobLogWithInstanceVO;
import com.xmcares.platform.admin.integrator.datasync.model.Datasync;
import com.xmcares.platform.admin.integrator.datasync.model.JobLogVO;
import com.xmcares.platform.admin.integrator.datasync.model.SchedulerJob;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobGroup;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobInfo;
import com.xmcares.platform.admin.integrator.datasync.vo.DataxTempVo;
import com.xxl.job.core.biz.model.LogResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SchedulerRepository {


    Map<String, SchedulerJob>  querySchedulerJobs(List<String> jobIds);
    /**
     * 添加任务调度信息
     * @param saveDatasync 数据同步信息
     * @param dataSyncTaskId datax 根据这个同步任务的id，获取json模板字段
     * @param filePath 文件路径 废弃
     * @param context 模板内容与参数信息
     * @return
     */
    XxlJobInfo addScheduler(DatasyncDto saveDatasync, String dataSyncTaskId, String filePath, DataxTempVo context);

    void updateScheduler(String dispatchId, Datasync saveDatasync);

    void removeScheduler(String dispatchId);

    void beginScheduler(String dispatchId);

    void endScheduler(String dispatchId);

    void triggerScheduler(String dispatchId);

    List<String> nextTriggerTime(String scheduleType, String scheduleConf);

    Map<String, Object> jobLogPageList(JobLogPageListDto jobLogPageListDto);

    List<XxlJobGroup> findAllJobGroup();

    List<XxlJobInfo> getJobsByGroup(int jobGroup);

    Boolean clearLog(int jobGroup, int jobId, int type);

    JobLogVO datasyncLog(String jobInstanceId);

    Page<XxlJobLogWithInstanceVO> pageDatasyncLog(JobLogPageListDto jobLogPageListDto, int page, int rows);

    LogResult logDetailCat(long logId, int fromLineNum);

    Boolean logKill(long jobId);

}
