/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：5/16/23
 */
package com.xmcares.platform.admin.common.datasource;

import java.beans.Transient;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据源配置项
 * <AUTHOR>
 * @since 1.4.1
 */
public class DataSourceOptions extends HashMap<String, Object> {

    // —— 以下为通用常量 —— //
    public static final String KEY_JDBC_DRIVER = "driver";
    public static final String KEY_JDBC_DRIVER_CLASS_NAME = "driverClassName";

    public final static String KEY_ID = "id";
    public final static String KEY_NAME = "name";
    public final static String KEY_TYPE = "type";
    public final static String KEY_GROUP = "group"; //SQL 、 NoSQL 、
    public final static String KEY_REMARK = "remark";
    public static final String KEY_URL = "url";
    public static final String KEY_SCHEMA = "schema";
    public static final String KEY_USERNAME = "username";
    public static final String KEY_PASSWORD = "password";

    /** HBase Zookeeper quorum 配置 key */
    public static final String HBASE_ZK_QUORUM = "hbase.zookeeper.quorum";


    public DataSourceOptions() {
    }

    public DataSourceOptions(Map<String, Object> options) {
        super(options);
    }

    @Transient
    public String getId() {
        return (String) this.get(KEY_ID);
    }

    @Transient
    public String getName() {
        return (String) this.get(KEY_NAME);
    }

    @Transient
    public String getDriverClassName() {
        String result = (String) this.get(KEY_JDBC_DRIVER);
        if (null == result) {
            result = (String) this.get(KEY_JDBC_DRIVER_CLASS_NAME);
        }
        return result;
    }
    @Transient
    public String getGroupName() {
        return (String) this.get(KEY_GROUP);
    }

    @Transient
    public String getTypeName() {
        return (String) this.get(KEY_TYPE);
    }

    @Transient
    public String getUrl() {
        return (String) this.get(KEY_URL);
    }

    @Transient
    public String getUsername() {
        return (String) this.get(KEY_USERNAME);
    }

    @Transient
    public String getPassword() {
        return (String) this.get(KEY_PASSWORD);
    }

    @Transient
    public String getSchema() {
        return (String) this.get(KEY_SCHEMA);
    }

    @SuppressWarnings("unchecked")
    public Map<String, Object> getForMap(String key) {
        Object value = this.get(key);
        if (value == null) {
            return Collections.emptyMap();
        }
        // 不进行类型校验，由用户自行校验
        return (Map<String, Object>)value;
    }

    public String getForString(String key, String defaultValue) {
        Object value = this.get(key);
        if (value == null) {
            return defaultValue;
        }
        return value.toString();
    }

    public Integer getForInteger(String key, Integer defaultValue) {
        Object value = this.get(key);
        if (value == null) {
            return defaultValue;
        }
        return (value instanceof Number) ? ((Number) value).intValue() : Integer.parseInt(value.toString());
    }

    public Boolean getForBoolean(String key, Boolean defaultValue) {
        Object value = this.get(key);
        if (value == null) {
            return defaultValue;
        }
        return (value instanceof Boolean) ? (Boolean) value : Boolean.valueOf(value.toString());
    }

    public Long getForLong(String key, Long defaultValue) {
        Object value = this.get(key);
        if (value == null) {
            return defaultValue;
        }
        return (value instanceof Number) ? ((Number) value).longValue() : Long.parseLong(value.toString());
    }
}
