import React, {PureComponent} from 'react';
import {connect} from 'dva';
import {DeleteOutlined, EditOutlined, PlusCircleOutlined} from '@ant-design/icons';
import {Form} from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Button,
  Card,
  Checkbox,
  Col,
  Divider,
  Input,
  Modal,
  Row,
  Spin,
  Select,
  Tooltip,
  Tree,
} from 'antd';
import CommonTable from '@/components/CommonTable/index';
import PageHeaderWrapper from '@/components/PageHeaderWrapper/index';
import styles from './DataSourceModel.less';
import * as utils from '@/utils/utils';
import {messageModal} from '@/utils/messageModal';
import Editor from './components/Editor'

let markPage = 1, pageSize = 10;
const TreeNode = Tree.TreeNode;
const confirm = Modal.confirm;
const modelsName = 'dataSourceModel';
const FormItem = Form.Item;



const category = [
  {code: "JDBC", name: "关系数据库"}, //关系数据库
  // {code: "BIGDATA", name: "大数据存储"},
  {code: "MQ", name: "消息队列"},
  // {code: "NOSQL", name: "NOSQL"},  // todo 去除数据源类别
];

@connect(({dataSourceModel, loading}) => ({
  dataSourceModel,
  loading: loading.models.dataSourceModel,
}))
@Form.create()

export default class DataSourceModel extends PureComponent {
  state = {
    selectedRows: undefined,
    defaultValue: [],
    modalVisible: false,
    modalTitle: '',
    selectedRowKeys: undefined
  };


  queryList = () => {
    const {dispatch} = this.props;
    dispatch({
      type: `${modelsName}/fetch`,
      callback: () => {
        this.setState({
          selectedRows: [],
          selectedRowKeys: []
        });
      },
    });
  };



  /**
   * 用户列表(第一次渲染后调用)
   */
  componentDidMount() {
    this.queryList();
  }
  deleteDataSourceModel = () => {
    const {dispatch} = this.props;
    const {selectedRows} = this.state;
    const ids = [];
    const topThis = this;
    if (!!selectedRows && selectedRows.length > 0) {
      confirm({
        title: '确定删除?',
        content: '删除该条记录',
        okText: '确定',
        cancelText: '取消',
        onOk() {
          const data = {
            id: selectedRows[0].id,
          };
          dispatch({
            type: `${modelsName}/deleteDataSourceModel`,
            payload: data,
            callback: () => {
              topThis.setState({
                selectedRows: [],
                selectedRowKeys: []
              });
              const data = {
                topThis,
                path: `${modelsName}/fetch`,
              };
              utils.search(data);
            },
          });
        },
      });
    } else {
      messageModal('warning', '请选择一条记录!');
    }
  };


  onChange = checkedValues => {
    this.setState({
      defaultValue: checkedValues,
    });
  };

  submitForm = (data, mark) => {
    const {dispatch} = this.props;
    const {selectedRowKeys} = this.state;
    if (mark == 0) {
      dispatch({
        type: `${modelsName}/create`,
        payload: {
          ...data,
        },
        callback: (res) => {
          if (res === true) {
            this.formRef.handleMinModalVisible(false);
            messageModal('success', res.message || '新增成功');
            this.reloadData();
          } else {
            messageModal('error', res.message || '新增失败');
          }
        }
      });
    }

    if (mark == 1) {
      dispatch({
        type: `${modelsName}/update`,
        payload: {
          id: selectedRowKeys[0],
          ...data,
        },
        callback: (res) => {
          if (res === true) {
            this.formRef.handleMinModalVisible(false);
            messageModal('success', '编辑成功');
            this.reloadData();
            this.setState({
              selectedRows: [],
              selectedRowKeys: undefined,
            });
          } else {
            messageModal('error', res.message || '编辑失败');
          }
        }
      });
    }
  }


  renderTreeNodes = data => {
    if (!!data && data.length > 0) {
      return data.map(item => {
        if (utils.isNull(item.children)) {
          item.children = [];
        }
        if (item.children) {
          return (
            <TreeNode title={item.text} key={item.id} dataRef={item}>
              {this.renderTreeNodes(item.children)}
            </TreeNode>
          );
        }
        return <TreeNode {...item} />;
      });
    }
  };

  handleModalVisible = flag => {
    this.setState({
      modalVisible: flag,
    });
  };

  /**
   * CommonTable 分页 排序 触发事件
   * @param pagination 分页
   * @param filtersArg
   * @param sorter 排序
   */
  handleCommonTableChange = (pagination, filtersArg, sorter) => {
    const {form} = this.props;
    markPage = pagination.current;
    pageSize = pagination.pageSize;
    form.validateFields((err, fieldsValue) => {
      let data = {
        page: pagination.current,
        rows: pagination.pageSize,
        ...fieldsValue
      };
      const {dispatch} = this.props;
      dispatch({
        payload: data,
        type: `${modelsName}/fetch`,
        callback: () => {
          this.setState({
            selectedRows: undefined,
          });
        },
      });
    });
  };

  reloadData = () => {
    const {form, dispatch} = this.props;
    form.validateFields((err, values) => {
      for (let key in values) {
        if (values[key] === '') {
          delete values[key]
        }
      }
      if (!err) {
        const params = {
          page: markPage,
          rows: pageSize,
          ...values,
        };
        dispatch({
          type: `${modelsName}/fetch`,
          payload: params,
        });
      }
    });
  }
  /**
   * 查询
   */
  handleSearch = (e) => {
    if (e) e.preventDefault();
    this.reloadData();
    this.setState({
      selectedRows: [],
      selectedRowKeys: [],
    });
  };

  /**
   * 重置
   */
  handleFormReset = () => {
    const data = {
      topThis: this,
    };
    utils.reset(data);
  };
  /**
   * 查询框
   */
  renderForm() {
    const {form} = this.props;
    const {getFieldDecorator} = this.props.form;
    return (
      <Form onSubmit={this.handleSearch}>
        <Row gutter={{md: 4, lg: 12, xl: 24}}>
          <Col md={5} sm={24}>
            <FormItem label="模型名称">
              {getFieldDecorator('type', {
                rules: [
                  {max: 20, message: '最多可输入20字'},
                ],
              })(<Input placeholder="请输入名称" onBlur={utils.valToTrim.bind(this, 'name', form)} allowClear={true}/>)}
            </FormItem>
          </Col>

          <Col md={5} sm={24}>
            <FormItem label="模型名称">
              {getFieldDecorator('type', {
                rules: [
                  {max: 20, message: '最多可输入20字'},
                ],
              })(<Input placeholder="请输入模型名称" onBlur={utils.valToTrim.bind(this, 'type', form)} allowClear={true}/>)}
            </FormItem>
          </Col>

          <Col xxl={5} xl={8} sm={9}>
            <FormItem label="模型类型：">
              {getFieldDecorator('category', {
                initialValue: ''
              })(<Select style={{width: '100%'}} allowClear={true}>
                {category && category.map(item => (
                  <Select.Option key={item.code}>{item.name}</Select.Option>
                ))}
              </Select>)}
            </FormItem>
          </Col>

          {/*<Col md={5} sm={24}>
            <FormItem label="创建人">
              {getFieldDecorator('createUser', {
                rules: [
                  { max: 20, message: '最多可输入20字' },
                ],
              })(<Input placeholder="请输入创建人" onBlur={utils.valToTrim.bind(this, 'createUser', form)} allowClear={true}/>)}
            </FormItem>
          </Col>*/}

          <Col md={5} sm={24}>
            <Button type="primary" htmlType="submit">
              查询
            </Button>
            <Button style={{marginLeft: 8}} type="primary" onClick={this.handleFormReset}>重置</Button>
          </Col>
        </Row>
      </Form>
    );
  }


  render() {
    const columns = [
      {
        title: '模型名称',
        dataIndex: 'type',
        width: '15%'
      },
      {
        title: '类目',
        dataIndex: 'category',
        width: '15%',
        render: (text) => {
          let name = null;
          for (let item of category) {
            if (text == item.code) {
              name = item.name;
            }
          }
          return (
            <Tooltip title={name === null ? text : name}>
              <div className={styles.resultColumnsDiv}>{name === null ? text : name}</div>
            </Tooltip>
          );
        },
      },
      {
        title: '创建人',
        dataIndex: 'createUser',
        width: '10%',
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: '15%',
        render: (text) => {
          return (
            <Tooltip title={text}>
              <div className={styles.resultColumnsDiv}>{text}</div>
            </Tooltip>
          );
        },
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        width: '15%',
        render: (text) => {
          return (
            <Tooltip title={text}>
              <div className={styles.resultColumnsDiv}>{text}</div>
            </Tooltip>
          );
        },
      },
      {
        title: '备注',
        dataIndex: 'remark',
        width: '25%'
      }
    ];
    const {
      selectedRows,
    } = this.state;
    const {dataSourceModel: {data, loading}} = this.props;


    return (
      <PageHeaderWrapper>
        <Card bordered={false}>
          <div>
            {/*<div className={styles.tableListForm}>{this.renderForm()}</div>*/}
            <div className={styles.btnStyle}>
              <span>
                <Editor syncOrSourceMark={true} queryList={this.queryList} title={'新增模型'} name={'新增'} mark={'add'} markModel={'source'}>
                   <PlusCircleOutlined style={{fontSize: 16, color: '#40a9ff'}}/>&nbsp;
                </Editor>
                <Divider type="vertical"/>
              </span>
              <span>
                <Editor syncOrSourceMark={true} queryList={this.queryList} title={'修改模型'} name={'修改'} selectedRows={selectedRows} mark={'edit'} markModel={'source'}>
                   <EditOutlined style={{fontSize: 16, color: '#40a9ff'}}/>&nbsp;
                </Editor>
                <Divider type="vertical"/>
              </span>
              <span>
                <DeleteOutlined style={{fontSize: 16, color: 'red'}}/>&nbsp;
                <a style={{color: 'red'}} onClick={this.deleteDataSourceModel}>删除</a>
              </span>
            </div>

            <CommonTable
              paginationShow={false}
              selectedRows={selectedRows}
              selectType="radio"
              loading={loading}
              data={{data: data}}
              columns={columns}
              current={markPage}
              onSelect={(selectedRowKeys, selectedRows) => {
                this.setState({
                  selectedRowKeys,
                  selectedRows,
                });
              }}
              onChange={this.handleCommonTableChange}
            />
          </div>
        </Card>
      </PageHeaderWrapper>
    );
  }
}
