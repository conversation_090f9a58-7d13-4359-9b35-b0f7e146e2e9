<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmcares.platform.hookbox.integrator.mapper.DatasyncJobMapper">
  <resultMap id="BaseResultMap" type="com.xmcares.platform.hookbox.integrator.model.Datasync">
    <!--@mbg.generated-->
    <!--@Table bdp_intg_datasync-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="intg_name" jdbcType="VARCHAR" property="intgName" />
    <result column="orgin_type" jdbcType="CHAR" property="orginType" />
    <result column="orgin_intg_model_id" jdbcType="VARCHAR" property="orginIntgModelId" />
    <result column="orgin_datasource_name" jdbcType="VARCHAR" property="orginDatasourceName" />
    <result column="orgin_datasource_id" jdbcType="VARCHAR" property="orginDatasourceId" />
    <result column="orgin_plugin_path" jdbcType="VARCHAR" property="orginPluginPath" />
    <result column="orgin_base_json" jdbcType="LONGVARCHAR" property="orginBaseJson" />
    <result column="orgin_adv_json" jdbcType="LONGVARCHAR" property="orginAdvJson" />
    <result column="orgin_high_json" jdbcType="LONGVARCHAR" property="orginHighJson" />
    <result column="orgin_column_json" jdbcType="LONGVARCHAR" property="orginColumnJson" />
    <result column="orgin_run_schema" jdbcType="VARCHAR" property="orginRunSchema" />
    <result column="dest_type" jdbcType="CHAR" property="destType" />
    <result column="dest_intg_model_id" jdbcType="VARCHAR" property="destIntgModelId" />
    <result column="dest_datasource_name" jdbcType="VARCHAR" property="destDatasourceName" />
    <result column="dest_datasource_id" jdbcType="VARCHAR" property="destDatasourceId" />
    <result column="dest_plugin_path" jdbcType="VARCHAR" property="destPluginPath" />
    <result column="dest_base_json" jdbcType="VARCHAR" property="destBaseJson" />
    <result column="dest_adv_json" jdbcType="VARCHAR" property="destAdvJson" />
    <result column="scheduler_expr" jdbcType="VARCHAR" property="schedulerExpr" />
    <result column="dest_high_json" jdbcType="VARCHAR" property="destHighJson" />
    <result column="dest_column_json" jdbcType="LONGVARCHAR" property="destColumnJson" />
    <result column="route_strategy" jdbcType="VARCHAR" property="routeStrategy" />
    <result column="block_strategy" jdbcType="VARCHAR" property="blockStrategy" />
    <result column="child_jobid" jdbcType="VARCHAR" property="childJobid" />
    <result column="executor_timeout" jdbcType="INTEGER" property="executorTimeout" />
    <result column="executor_fail_retry_count" jdbcType="INTEGER" property="executorFailRetryCount" />
    <result column="has_delete" jdbcType="CHAR" property="hasDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, create_user, intg_name, orgin_type, orgin_intg_model_id,
    orgin_datasource_name, orgin_datasource_id, orgin_plugin_path, orgin_base_json, orgin_adv_json,
    orgin_high_json, orgin_column_json, orgin_run_schema, dest_type, dest_intg_model_id,
    dest_datasource_name, dest_datasource_id, dest_plugin_path, dest_base_json, dest_adv_json,
    scheduler_expr, dest_high_json, dest_column_json, route_strategy, block_strategy,
    child_jobid, executor_timeout, executor_fail_retry_count, has_delete
  </sql>
</mapper>
