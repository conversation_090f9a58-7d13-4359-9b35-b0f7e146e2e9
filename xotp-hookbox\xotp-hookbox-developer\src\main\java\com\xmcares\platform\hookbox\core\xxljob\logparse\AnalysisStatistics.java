package com.xmcares.platform.hookbox.core.xxljob.logparse;

import com.xmcares.platform.admin.common.util.CommonConstants;
import com.xxl.job.core.context.XxlJobHelper;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * Log of analysis statistics
 *
 * <AUTHOR> 2020-06-07
 */

public class AnalysisStatistics {

    public static final String TASK_START_TIME_SUFFIX = "任务启动时刻";
    public static final String TASK_END_TIME_SUFFIX = "任务结束时刻";

    public static final String TASK_TOTAL_TIME_SUFFIX = "任务总计耗时";
    public static final String TASK_AVERAGE_FLOW_SUFFIX = "任务平均流量";
    public static final String TASK_RECORD_WRITING_SPEED_SUFFIX = "记录写入速度";
    public static final String TASK_RECORD_READER_NUM_SUFFIX = "读出记录总数";
    public static final String TASK_RECORD_WRITING_NUM_SUFFIX = "读写失败总数";

    /**
     * Log of analysis statistics
     *
     * @param inputStream
     * @throws IOException
     */
    public static LogStatistics analysisStatisticsLog(InputStream inputStream) throws IOException {

        LogStatistics logStatistics = new LogStatistics();
        try {
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains(TASK_START_TIME_SUFFIX)) {
                    logStatistics.setTaskStartTime(subResult(line));
                } else if (line.contains(TASK_END_TIME_SUFFIX)) {
                    logStatistics.setTaskEndTime(subResult(line));
                } else if (line.contains(TASK_TOTAL_TIME_SUFFIX)) {
                    logStatistics.setTaskTotalTime(subResult(line));
                } else if (line.contains(TASK_AVERAGE_FLOW_SUFFIX)) {
                    logStatistics.setTaskAverageFlow(subResult(line));
                } else if (line.contains(TASK_RECORD_WRITING_SPEED_SUFFIX)) {
                    logStatistics.setTaskRecordWritingSpeed(subResult(line));
                } else if (line.contains(TASK_RECORD_READER_NUM_SUFFIX)) {
                    logStatistics.setTaskRecordReaderNum(Integer.parseInt(subResult(line)));
                } else if (line.contains(TASK_RECORD_WRITING_NUM_SUFFIX)) {
                    logStatistics.setTaskRecordWriteFailNum(Integer.parseInt(subResult(line)));
                }
                XxlJobHelper.log(line);
            }
            reader.close();
            inputStream = null;
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
        return logStatistics;
    }

    private static String subResult(String line) {
        if (StringUtils.isBlank(line)) { return CommonConstants.STRING_BLANK; }
        int pos = line.indexOf(CommonConstants.SPLIT_SCOLON);
        if (pos > 0) { return line.substring(pos + 1).trim(); }
        return line.trim();
    }
}
