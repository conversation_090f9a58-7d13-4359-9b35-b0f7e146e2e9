/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/29
 */
package com.xmcares.platform.hookbox.integrator.manager;

import com.fasterxml.jackson.databind.JsonNode;
import com.xmcares.framework.commons.util.JacksonJsonUtils;
import com.xmcares.framework.imc.client.utils.SnowflakeIdUtils;
import com.xmcares.platform.admin.common.datasource.DataSourceOptions;
import com.xmcares.platform.admin.common.datasource.mq.MqType;
import com.xmcares.platform.hookbox.common.job.*;
import com.xmcares.platform.hookbox.common.job.context.JobContextImpl;
import com.xmcares.platform.hookbox.common.job.context.JsonJobParams;
import com.xmcares.platform.hookbox.common.job.error.JobExecutionException;
import com.xmcares.platform.hookbox.common.mq.MqTemplate;
import com.xmcares.platform.hookbox.integrator.model.DatasyncJob;
import com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance;
import com.xmcares.platform.hookbox.integrator.service.DatasyncJobService;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.log.XxlJobFileAppender;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

import static com.xmcares.platform.hookbox.integrator.handler.XxlJobDatasyncJobHandler.XXL_JOB_MDC_KEY;
import static com.xmcares.platform.hookbox.integrator.handler.XxlJobDatasyncJobHandler.XXL_JOB_MDC_VALUE;

/**
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since 2.1.0
 */
@Component
public class DatasyncJobManager implements ApplicationRunner {

    private static final Logger logger = LoggerFactory.getLogger(DatasyncJobManager.class);

    @Resource
    private DatasyncJobService datasyncJobService;

    @Resource
    private XimcReaderBridgeManager readerBridgeManager;

    @Resource
    private JobContextService jobContextService;

    @Resource
    private MqTemplate mqTemplate;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        resumeJobs();
        resumeListener();
    }

    private void resumeListener() {
    }

    /**
     * 恢复任务
     */
    public void resumeJobs() {
        List<DatasyncJobInstance> startedInstances = datasyncJobService.findRunningInstances();
        for (DatasyncJobInstance jobInst : startedInstances) {
            String jobParams = jobInst.getJobParams(); //恢复任务参数
            String jobOptions = jobInst.getJobOptions(); //恢复任务配置
            JobContextImpl jobContext = new JobContextImpl(jobInst.getJobId(), jobInst.getId(), jobInst.getJobName());
            jobContext.setJobParams(new JsonJobParams(jobParams));
            jobContext.setJobOptions(jobOptions);

            //设置XxlJobContext，让log衔接到对应的xxl-job日志文件中
            //在任务开始时设置 MDC 上下文，使线程下所有logger日志接入到xxl-job的日志中---
            MDC.put(XXL_JOB_MDC_KEY, XXL_JOB_MDC_VALUE);
            try {
                //TODO ：解除与XXl-job的耦合
                String logFileName = XxlJobFileAppender.makeLogFileName(jobInst.getScheduleTime(), jobInst.getScheduleLogId());
                XxlJobContext xxlContext = new XxlJobContext(jobInst.getScheduleId(), jobParams, logFileName, 0, 0 );
                XxlJobContext.setXxlJobContext(xxlContext);

                startJobInstance(jobContext);
                logger.info("恢复任务实例[jobId={}, instanceId={}]成功", jobInst.getJobId(), jobInst.getId());
            } catch (Exception e) {
                logger.error("恢复任务实例[jobId={}, instanceId={}]失败", jobInst.getJobId(), jobInst.getId(), e);
            }  finally {
                XxlJobContext.setXxlJobContext(null);
                // 在任务结束时清除 MDC 上下文 ---
                MDC.remove(XXL_JOB_MDC_KEY);
            }
        }

    }


    /**
     * 启动数据同步任务
     * @param jobParams 数据同步任务参数
     */
    public void startJob(JobParams jobParams) {
        String jobId = jobParams.getJobId();
        //根据jobId去管理中心数据库获取数据同步实例信息，设置以下JobContext信息。
        DatasyncJob job = datasyncJobService.getDatasyncJobById(jobId);
        if (job == null) {
            //抛出异常，让任务调度层能够获取错误信息
            throw new IllegalArgumentException("无效的数据同步任务ID: "+ jobId);
        }

        //调度层没有问题，实际任务运行时开始......
        //启动任务实例，分配新实例ID
        JobContextImpl jobContext = new JobContextImpl(jobId, SnowflakeIdUtils.getNextIdAsString(), job.getJobName());
        jobContext.setJobParams(jobParams);
        jobContext.setJobOptions(job.getJobOptions());

        logger.info("数据同步任务实例[jobId={}, instanceId={}]开始提交", jobId, jobContext.getJobInstanceId());
        String status = JobConstants.STATUS_DEAD_STARTED;
        String statusMsg = null;
        try {
            startJobInstance(jobContext);
            status = JobConstants.STATUS_RUNNING;
            logger.info("数据同步任务实例[jobId={}, instanceId={}]提交成功", jobId, jobContext.getJobInstanceId());
        } catch (JobExecutionException e) {
            statusMsg = e.getMessage();
            logger.error("数据同步任务实例[jobId={}, instanceId={}]提交失败", jobId, jobContext.getJobInstanceId(), e);
        } catch (Exception e) {
            statusMsg = e.getMessage();
            logger.error("数据同步任务实例[jobId={}, instanceId={}]启动失败", jobId, jobContext.getJobInstanceId(), e);
        }

        logger.info("数据同步任务实例[jobId={}, instanceId={}]保存状态信息", jobId, jobContext.getJobInstanceId());
        DatasyncJobInstance jobInstance = buildJobInstance(job, jobContext);
        jobInstance.setStatus(status);
        jobInstance.setStatusMessage(statusMsg);
        datasyncJobService.insertJobInstance(jobInstance);
        //调度层没有问题，实际任务运行时结束......
    }



    /**
     * 停止数据同步任务
     * @param jobParams 数据同步任务参数
     */
    public void stopJob(JobParams jobParams) {
        String jobId = jobParams.getJobId();
        //根据jobId去管理中心数据库获取数据同步实例信息，设置以下JobContext信息。
        List<DatasyncJobInstance> jobInstances = datasyncJobService.findRunningInstancesByJobId(jobId);
        if (jobInstances.isEmpty()) {
            logger.warn("数据同步任务[jobId={}]没有状态是运行时的实例", jobId);
            return;
        }
        for (DatasyncJobInstance jobInstance : jobInstances) {
            JobContextImpl jobContext = new JobContextImpl(jobId, jobInstance.getId(), jobInstance.getJobName());
            jobContext.setJobParams(jobParams);
            jobContext.setJobOptions(jobInstance.getJobOptions());
            stopJobInstance(jobContext);
        }
    }


    /**
     * 启动数据同步任务实例
     * @param jobContext 数据同步任务
     */
    protected void startJobInstance(JobContext jobContext) {
        //检查是否存在并启动XIMC Source桥接任务
        startIfXimcBridgeJob(jobContext);
        jobContextService.startJob(jobContext);
    }

    /**
     * 停止数据同步任务实例
     * @param jobContext 数据同步任务
     */
    protected void stopJobInstance(JobContext jobContext) {
        stopIfXimcBridgeJob(jobContext);
        jobContextService.stopJob(jobContext);
    }

    /**
     * 启动XIMC Source桥接任务
     * @param jobContext 数据同步任务
     */
    protected void startIfXimcBridgeJob(JobContext jobContext) {
        JsonNode ximcSourceNode = getXimcSourceIfPresent(jobContext);
        if (ximcSourceNode != null) {
            //消息中心（XIMC）Reader先转移到内部消息队列
            DataSourceOptions options = getXimcSourceOptions(jobContext, ximcSourceNode);
            readerBridgeManager.addXimcReaderIfAbsent(options, jobContext.getJobId());
            logger.info("数据同步任务[{}]添加XIMC Source桥接任务: {}", jobContext.getJobId(), options);
        }
    }

    /**
     * 启动XIMC Source桥接任务
     * @param jobContext 数据同步任务
     */
    protected void stopIfXimcBridgeJob(JobContext jobContext) {
        JsonNode ximcSourceNode = getXimcSourceIfPresent(jobContext);
        if (ximcSourceNode != null) {
            //消息中心（XIMC）Reader先转移到内部消息队列
            DataSourceOptions options = getXimcSourceOptions(jobContext, ximcSourceNode);
            readerBridgeManager.removeXimcReaderIfPresent(options, jobContext.getJobId());
            logger.info("数据同步任务[{}]移除XIMC Source桥接任务: {}", jobContext.getJobId(), options);
        }
    }



    private static DatasyncJobInstance buildJobInstance(DatasyncJob job, JobContext jobContext) {
        DatasyncJobInstance jobInstance = new DatasyncJobInstance();
        jobInstance.setId(jobContext.getJobInstanceId());
        jobInstance.setJobId(job.getId());
        jobInstance.setJobName(job.getJobName());
        jobInstance.setJobType(job.getJobType());
        jobInstance.setJobOptions(job.getJobOptions());
        JobParams jobParams = jobContext.getJobParams();
        jobInstance.setJobParams(jobParams.toJsonString());
        jobInstance.setScheduleId(jobParams.getScheduleId());
        jobInstance.setScheduleLogId(jobParams.getScheduleLogId());
        jobInstance.setScheduleTime(jobParams.getScheduleTime());
        jobInstance.setTriggerTime(jobParams.getTriggerTime());
        return jobInstance;
    }

    /**
     * 获取XIMC DataSource配置
     * @param jobContext 数据同步任务
     * @param ximcSourceNode XIMC Source配置
     * @return XIMC DataSource配置
     */
    @NotNull
    private static DataSourceOptions getXimcSourceOptions(JobContext jobContext, JsonNode ximcSourceNode) {
        DataSourceOptions options = new DataSourceOptions();
        options.put(DataSourceOptions.KEY_NAME, "ximc_" + jobContext.getJobName());
        options.put(DataSourceOptions.KEY_TYPE, MqType.XIMC.getTypeName());
        options.put(DataSourceOptions.KEY_GROUP, MqType.XIMC.getGroup().name());
        options.put(DataSourceOptions.KEY_URL, ximcSourceNode.get("url").asText());
        options.put(DataSourceOptions.KEY_USERNAME, ximcSourceNode.get("username").asText());
        options.put(DataSourceOptions.KEY_PASSWORD, ximcSourceNode.get("password").asText());
        return options;
    }


    /**
     * 获取XIMC Source配置
     * @param jobContext 数据同步任务
     * @return XIMC Source配置
     */
    private static JsonNode getXimcSourceIfPresent(JobContext jobContext) {
        String jobOptions = jobContext.getJobOptions();
        JsonNode jobConfig;
        try {
            jobConfig = JacksonJsonUtils.readJson(jobOptions);
        } catch (IOException e) {
            logger.error("解析数据同步任务[{}]配置内容失败:{}", jobContext.getJobId(), e.getMessage());
            //TODO 更新任务状态为失败
            throw new IllegalArgumentException("解析数据同步任务[{"+ jobContext.getJobId()+"}]配置异常", e);
        }

        //检查输入源是否是消息中心XIMC
        //TODO 优化判断是否是消息中心的方式
        JsonNode sourceNode = jobConfig.get("source");
        JsonNode ximcSourceNode = null;
        if (sourceNode.isArray()) {
            for (int i=0; i<sourceNode.size(); i++) {
                JsonNode jsonNode = sourceNode.get(i);
                //平台需要自定义出XIMC_PULLSAR和XIMC_ARTEMIS的Source
                if (jsonNode.has("XIMC_PULSAR") || jsonNode.has("XIMC_ARTEMIS")) {
                    ximcSourceNode = jsonNode;
                    break;
                }
            }
        }
        return ximcSourceNode;
    }


}
