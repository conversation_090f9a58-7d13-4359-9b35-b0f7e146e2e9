/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/3/2
 */
package com.xmcares.platform.admin.integrator.datasync.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xmcares.framework.commons.context.UserContext;
import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.platform.admin.common.datasource.mq.MqType;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.util.CommonConstants;
import com.xmcares.platform.admin.common.vo.ReturnT;
import com.xmcares.platform.admin.integrator.IntegratorProperties;
import com.xmcares.platform.admin.integrator.common.error.UnknownException;
import com.xmcares.platform.admin.integrator.common.util.ConstantUtils;
import com.xmcares.platform.admin.integrator.datasync.dto.DatasyncDto;
import com.xmcares.platform.admin.integrator.datasync.dto.JobLogPageListDto;
import com.xmcares.platform.admin.integrator.datasync.model.Datasync;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncInstance;
import com.xmcares.platform.admin.integrator.datasync.model.SchedulerJob;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.XxljobClient;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobGroup;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobInfo;
import com.xmcares.platform.admin.integrator.datasync.vo.DataxTempVo;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceRepository;
import com.xxl.job.core.biz.model.LogResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 基于xxl-job调度datax完成数据同步
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public class XxlJobSchedulerRepository implements SchedulerRepository {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private XxljobClient xxlJobClient;

    @Autowired
    private IntegratorProperties properties;

    @Resource
    private DatasyncInstanceRepository datasyncInstanceRepository;
    @Autowired
    private DatasourceRepository datasourceRepository;

    @Override
    public Map<String, SchedulerJob> querySchedulerJobs(List<String> jobIds) {
        if (CollectionUtils.isEmpty(jobIds)) {
            return new HashMap<>();
        }
        // TODO: 这里对应的其实是bdp_intg_datasync_task下的dispatch_id，需要改造成查自己的
//        ReturnT<List<XxlJobInfo>> findResult = xxlJobClient.queryList(jobIds);
        List<DatasyncInstance> datasyncInstances = datasyncInstanceRepository.listByIds(jobIds);
//        if (findResult.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
//            throw new BusinessException(findResult.getMsg());
//        }
//        List<XxlJobInfo> jobs = findResult.getContent();

        if(CollectionUtils.isEmpty(datasyncInstances)) {
            return new HashMap<>();
        }
        Map<String, SchedulerJob> schedulerJobMap = new HashMap<>();
        datasyncInstances.forEach(job -> {
            SchedulerJob schedulerJob = new SchedulerJob();
            schedulerJob.setId(job.getDispatchId());
            schedulerJob.setJobGroup(job.getJobGroup() + "");
            schedulerJob.setExpression(job.getScheduleConf());
            schedulerJob.setTriggerStatus(job.getTriggerStatus());
            schedulerJob.setExecutorTimeout(job.getExecutorTimeout());
            schedulerJob.setExecutorFailRetryCount(job.getExecutorFailRetryCount());
            schedulerJob.setSchedulerType(job.getScheduleType());
            schedulerJob.setExecutorRouteStrategy(job.getExecutorRouteStrategy());
            schedulerJob.setExecutorBlockStrategy(job.getExecutorBlockStrategy());
            schedulerJobMap.put(job.getDispatchId(), schedulerJob);
        });
        return schedulerJobMap;
    }


    @Override
    public XxlJobInfo addScheduler(DatasyncDto saveDatasync, String dataSyncTaskId, String filePath, DataxTempVo context) {
        try {
            // 1. 从XXL Job上获取任务运行的服务信息
            // 固定死默认的执行器
            ReturnT<String> jobGroupResult = xxlJobClient.loadXxlJobGroupById(2);
            // code 为 200，才是正确的
            if (!(null != jobGroupResult && jobGroupResult.getCode() == ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE)) {
                throw new BusinessException(jobGroupResult.getMsg());
            }

            XxlJobGroup xxlJobGroup = JSON.parseObject(jobGroupResult.getContent(), XxlJobGroup.class);


//            ReturnT<XxlJobGroup> jobGroupResult = xxlJobClient.findJobGroup(properties.getXxlJob().getGroup());
//            if (jobGroupResult.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
//                throw new BusinessException(jobGroupResult.getMsg());
//            }


            // 2. 将文件路径设置成用户参数
            String userParam = CommonConstants.DATAX_DISPATCH_PARAMS_PARAM +
                    CommonConstants.DATAX_DISPATCH_PARAMS_PARAM_FILEPATH + CommonConstants.KV_SPLIT +
                    filePath;

            // 2.1. 设置下载文件参数
            StringBuilder builder = new StringBuilder(userParam);
            // TODO: 魔法值
            builder.append(CommonConstants.FIELDS_SPLIT).append("DATASYNC_INSTANCE_ID=").append(dataSyncTaskId);
            // 获取原始数据类型
            Datasource orig = datasourceRepository.get(saveDatasync.getOrginDatasourceId());
            Datasource dest = datasourceRepository.get(saveDatasync.getDestDatasourceId());
            if (MqType.XIMC.equalsTypeName(orig.getType())) {
                // TODO: 后续如果是到db类型的，需要有一个大类来判断
                // 判断是否为消息中心类型，如果是需要加上消息中心类型参数，其他类型暂未实现
                builder.append(CommonConstants.FIELDS_SPLIT).append("syncType=").append("msg2db");
            }else {
                builder.append(CommonConstants.FIELDS_SPLIT).append("syncType=").append("db2db");
            }


            if (null != context.getParams() && !context.getParams().isEmpty()) {
                StringBuilder downloadPath = new StringBuilder();
                for (String key : context.getParams().keySet()) {
                    JSONObject object;
                    try {
                        object = JSON.parseObject(String.valueOf(context.getParams().get(key)));
                    } catch (Exception e) {
                        continue;
                    }
                    for (Map.Entry<String, Object> echo : object.entrySet()) {
                        String path = parserPath(echo.getKey(), echo.getValue());
                        if (path != null) {
                            downloadPath.append(downloadPath.length() > 0 ? CommonConstants.FIELDS_SPLIT : "")
                                    .append(echo.getKey()).append(CommonConstants.KV_SPLIT).append(path);
                        }
                    }

                }
                if (downloadPath.length() > 0) {
                    builder.append(CommonConstants.DATAX_DISPATCH_PARAMS_DOWNLOAD_FILE).append(downloadPath.toString());
                }
            }

            // 2.2. 设置动态参数
            Map<String, Object> entity = saveDatasync.buildOrginMapInfo();
            if (entity.containsKey(CommonConstants.DATAX_DISPATCH_PARAMS_SYNC_KEY) && entity.get(CommonConstants.DATAX_DISPATCH_PARAMS_SYNC_KEY) != null
                    && StringUtils.isNotEmpty(entity.get(CommonConstants.DATAX_DISPATCH_PARAMS_SYNC_KEY).toString())) {
                builder.append(CommonConstants.DATAX_DISPATCH_PARAMS_SYNC).append(entity.get(CommonConstants.DATAX_DISPATCH_PARAMS_SYNC_KEY));
            }

            // 3. 获取登陆用户信息
            UserContext userContext = UserContextHolder.getUserContext();
            // 4. 构建XXL JOB 任务信息
            XxlJobInfo xxlJobInfo =
                    // 4.1. 初始化XXL JOB 任务信息
                    XxlJobInfo.createNewFromByBeginHandler(saveDatasync, userContext.getUsername(),
                                    xxlJobGroup.getId(), properties.getXxlJob().getAlarmEmail(), builder.toString())
                            // 4.2. 构建增量参数信息
                            .buildIncrement(saveDatasync.buildOrginMapInfo(), saveDatasync.getFromMod(), saveDatasync.getOrginDatasourceId())
                            // 4.3. 构建JVM运行参数信息
                            .buildJvm(saveDatasync.buildOrginMapInfo());
            //设置子任务id linbk  20241014
            xxlJobInfo.setChildJobId(saveDatasync.getChildJobid());
            // 5. 将 XXL JOB 任务信息提交到 调度服务
            // 使用xxl-job默认的新增方法
            ReturnT<String> jobResult = xxlJobClient.addJobInfo(xxlJobInfo);
            if (jobResult.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
                throw new BusinessException("创建Job任务失败：" + jobResult.getMsg());
            } else {
                // 需要手动设置jobResult返回新增成功后的jobId，否则默认为空
                xxlJobInfo.setId(Integer.parseInt(jobResult.getContent()));
                return xxlJobInfo;
            }
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            logger.error("添加调度任务异常", e);
            throw new UnknownException("内部服务异常");
        }
    }

    private String parserPath(String key, Object param) {
        if (key.length() <= 4) { return null; }
        if (!"PATH".equalsIgnoreCase(key.substring(key.length() - 4))) {
            return null;
        }
        String v = param instanceof JSON ? param.toString() : String.valueOf(param);
        if (JSONObject.isValid(v)) {
            JSONObject result = JSON.parseObject(v);
            if (result.containsKey("path")) {
                return result.getString("path");
            }
        }
        return null;
    }

    @Override
    public void updateScheduler(String dispatchId, Datasync saveDatasync) {
        try {
            // 1. 获取旧的信息
            ReturnT<String> findResult = xxlJobClient.loadJobInfoById(Integer.parseInt(dispatchId));

            if (findResult.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
                throw new BusinessException(findResult.getMsg());
            }
            XxlJobInfo findResultObject = JSON.parseObject(findResult.getContent(), XxlJobInfo.class);

            // 3. 获取登陆用户信息
            UserContext userContext = UserContextHolder.getUserContext();
            // 4. 构建XXL JOB 任务信息
            XxlJobInfo xxlJobInfo =
                    // 4.1. 初始化XXL JOB 任务信息
                    XxlJobInfo.createUpdateFromByBeginHandler(dispatchId, findResultObject.getJobDesc(), saveDatasync, userContext.getUsername(),
                            findResultObject.getJobGroup(),
                            properties.getXxlJob().getAlarmEmail(), findResultObject.getExecutorParam());
            // 5. 将 XXL JOB 任务信息提交到 调度服务
            // TODO: 待修改
            ReturnT<String> jobResult = xxlJobClient.updateJob(xxlJobInfo);
            if (jobResult.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
                throw new BusinessException("创建Job任务失败：" + jobResult.getMsg());
            }

        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            logger.error("添加调度任务异常", e);
            throw new UnknownException("内部服务异常");
        }
    }

    @Override
    public void removeScheduler(String dispatchId) {
        try {
            ReturnT<String> stringReturnT = xxlJobClient.removeJob(Integer.parseInt(dispatchId));
            if (stringReturnT.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
                // 删除任务失败，抛出异常
                throw new BusinessException(stringReturnT.getMsg());
            }
        } catch (Exception e) {
            logger.error("移除调度服务任务异常，id:{}", dispatchId, e);
            throw new UnknownException("任务删除失败");
        }
    }

    @Override
    public void beginScheduler(String dispatchId) {
        try {
            ReturnT<String> result = xxlJobClient.startJob(Integer.parseInt(dispatchId));
            if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
                throw new BusinessException(result.getMsg());
            }
        } catch (Exception e) {
            logger.error("开始调度服务任务异常", e);
            throw new UnknownException("任务开始失败");
        }
    }

    @Override
    public void endScheduler(String dispatchId) {
        try {
            ReturnT<String> result = xxlJobClient.endJob(Integer.parseInt(dispatchId));
            if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
                throw new BusinessException(result.getMsg());
            }
        } catch (Exception e) {
            logger.error("停止调度服务任务异常", e);
            throw new UnknownException("任务停止失败");
        }
    }

    @Override
    public void triggerScheduler(String dispatchId) {
        // 查询本地的job状态
        // 判断状态是否为停止，挺值得才允许运行一次
        // 调用触发方法
        DatasyncInstance byDispatchId = datasyncInstanceRepository.getByDispatchId(dispatchId);
        if (ObjectUtils.isEmpty(byDispatchId)) {
            throw new BusinessException("该任务不存在");
        }




//        // 1. 获取旧的信息
//        ReturnT<XxlJobInfo> findResult = xxlJobClient.findJob(dispatchId);
//        if (findResult.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
//            throw new BusinessException(findResult.getMsg());
//        }

        if (byDispatchId.getTriggerStatus() != 0) {
            throw new BusinessException("只有停止的任务才允许运行一次");
        }

        xxlJobClient.triggerJob(XxlJobInfo.buildTriggerJob(byDispatchId));
    }

    @Override
    public List<String> nextTriggerTime(String scheduleType, String scheduleConf) {
        // 参数校验
        if (StringUtils.isEmpty(scheduleType) || StringUtils.isEmpty(scheduleConf)) {
            throw new BusinessException("传入参数为空！");
        }
        ReturnT<List<String>> stringReturnT = xxlJobClient.nextTriggerTime(scheduleType, scheduleConf);
        if (stringReturnT.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
            // 获取调度信息失败，抛出异常
            throw new BusinessException(stringReturnT.getMsg());
        }
        return stringReturnT.getContent();
    }

    @Override
    public Map<String, Object> jobLogPageList(JobLogPageListDto jobLogPageListDto) {
        Map<String, Object> findResult =  xxlJobClient.jobLogPageList(jobLogPageListDto.getStart(), jobLogPageListDto.getLength(), jobLogPageListDto.getJobGroup(), jobLogPageListDto.getJobId(), jobLogPageListDto.getLogStatus(), jobLogPageListDto.getFilterTime());

        return findResult;
    }

    @Override
    public List<XxlJobGroup> findAllJobGroup() {
        ReturnT<String> result = xxlJobClient.findAllJobGroup();
        if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
            throw new BusinessException(result.getMsg());
        }
        return JSON.parseArray(result.getContent(), XxlJobGroup.class);
    }

    @Override
    public List<XxlJobInfo> getJobsByGroup(int jobGroup) {
        ReturnT<List<XxlJobInfo>> result = xxlJobClient.getJobsByGroup(jobGroup);
        if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
            throw new BusinessException(result.getMsg());
        }
        return result.getContent();
    }

    @Override
    public Boolean clearLog(int jobGroup, int jobId, int type) {
        ReturnT<String> result = xxlJobClient.clearLog(jobGroup, jobId, type);
        if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
            throw new BusinessException(result.getMsg());
        }
        return true;
    }

    @Override
    public LogResult logDetailCat(long logId, int fromLineNum) {
        ReturnT<LogResult> result = xxlJobClient.logDetailCat(logId, fromLineNum);
        if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
            throw new BusinessException(result.getMsg());
        }
        return result.getContent();
    }

    @Override
    public Boolean logKill(long jobId) {
        ReturnT<String> result = xxlJobClient.logKill(jobId);
        if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
            throw new BusinessException(result.getMsg());
        }
        return true;
    }
}
