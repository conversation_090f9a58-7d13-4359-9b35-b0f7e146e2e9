package com.xmcares.platform.hookbox.integrator.handler;

/**
 * SeatunnelEvent
 *
 * <AUTHOR>
 * @Descriptions SeaTunnelEvent
 * @Date 2025/7/24 15:53
 */

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

// SeaTunnel发送的事件JSON对应的Java对象
// @JsonIgnoreProperties可以不关心的字段，增强兼容性
@JsonIgnoreProperties(ignoreUnknown = true)
public class SeaTunnelEvent {

    private long createdTime;
    private String jobId;
    private String eventType; // 使用String类型接收，更灵活

    // Getters and Setters
    public long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(long createdTime) {
        this.createdTime = createdTime;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    @Override
    public String toString() {
        return "SeaTunnelEvent{" +
                "createdTime=" + createdTime +
                ", jobId='" + jobId + '\'' +
                ", eventType='" + eventType + '\'' +
                '}';
    }
}
