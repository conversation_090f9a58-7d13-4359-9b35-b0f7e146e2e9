package com.xmcares.platform.hookbox.integrator.service;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmcares.platform.hookbox.integrator.mapper.IntegratorXxlJobLogMapper;
import com.xmcares.platform.hookbox.integrator.model.IntegratorXxlJobLog;
import com.xmcares.platform.hookbox.integrator.model.XxlJobLogWithInstanceVO;
import com.xmcares.platform.hookbox.integrator.model.XxlJobLogQueryDTO;
import com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance;
import com.xmcares.platform.hookbox.integrator.service.DatasyncJobInstanceService;
import com.xmcares.platform.hookbox.common.job.JobContext;
import com.xmcares.platform.hookbox.common.job.context.JobContextImpl;
import com.xmcares.platform.hookbox.common.job.seatunnel.RemoteSeaTunnelJobContextService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.util.XxlJobRemotingUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
public class IntegratorXxlJobLogService extends ServiceImpl<IntegratorXxlJobLogMapper, IntegratorXxlJobLog> {

    private static final Logger logger = LoggerFactory.getLogger(IntegratorXxlJobLogService.class);

    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${xxl.job.admin.addresses:http://127.0.0.1:8085}")
    private String adminAddresses;

    @Value("${xxl.job.accessToken:default_token}")
    private String accessToken;

    @Autowired
    private DatasyncJobInstanceService datasyncJobInstanceService;

    private final RemoteSeaTunnelJobContextService seaTunnelJobService = new RemoteSeaTunnelJobContextService();

    /**
     * 调用执行器API以获取详细的XXL-Job过程日志
     * @param xxlJobLog 日志元数据对象
     * @return 详细日志字符串
     */
    public String fetchXxlJobDetailLog(IntegratorXxlJobLog xxlJobLog) {
        try {
            // 准备请求 URL
            String url = adminAddresses + "/joblog/logDetailCat";

            // 1. 设置请求头 (Headers)
            HttpHeaders headers = new HttpHeaders();
            // **关键改动**: 设置 Content-Type 为 application/x-www-form-urlencoded
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            // 添加访问令牌，如果您的 admin 开启了 accessToken 验证
            if (accessToken != null && !accessToken.trim().isEmpty()) {
                headers.add("XXL-JOB-ACCESS-TOKEN", accessToken);
            }

            // 2. 构建请求体 (Body)，使用 MultiValueMap 来模拟 form-data
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("logId", String.valueOf(xxlJobLog.getId()));
            body.add("fromLineNum", "1");

            // 3. 创建 HttpEntity，将 headers 和 body 组合起来
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(body, headers);

            // 4. 发送 POST 请求
            // 使用 postForObject，并期望返回一个 Map 类型的 JSON 响应
            @SuppressWarnings("unchecked")
            Map<String, Object> response = restTemplate.postForObject(url, requestEntity, Map.class);

            // 5. 解析响应
            if (response != null && StringUtils.equalsIgnoreCase(MapUtil.getStr(response, "code"),"200")) {
                Map<String, Object> content = (Map<String, Object>) response.get("content");
                String logContent = MapUtil.getStr(content, "logContent");
                if (StringUtils.isNotBlank(logContent)) {
                    return logContent;
                }
            }
            return "从 Admin-Scheduler 获取日志失败，响应: " + response;
        } catch (Exception e) {
            // 记录详细错误日志会更有帮助
            return "调用 Admin-Scheduler 日志接口时发生异常: " + e.getMessage();
        }    }

    /**
     * 分页查询XXL-Job日志与任务实例关联数据
     * @param page 分页参数
     * @param wrapper 查询条件
     * @return 分页查询结果
     */
    public IPage<XxlJobLogWithInstanceVO> pageQueryJobLogsWithInstance(
            Page<XxlJobLogWithInstanceVO> page, QueryWrapper<XxlJobLogWithInstanceVO> wrapper) {

        // 参数校验
        validatePageParameters(page);

        logger.info("执行分页查询XXL-Job日志与任务实例关联数据，页码: {}, 页大小: {}",
                   page.getCurrent(), page.getSize());

        try {
            // 调用Mapper层分页查询方法
            IPage<XxlJobLogWithInstanceVO> result = baseMapper.selectJobLogWithInstancePageByLogId(page, wrapper);

            logger.info("分页查询完成，总记录数: {}, 当前页记录数: {}",
                       result.getTotal(), result.getRecords().size());

            // 为每条记录填充完整日志信息
//            fillLogInformation(result.getRecords());


            return result;
        } catch (Exception e) {
            logger.error("分页查询XXL-Job日志与任务实例关联数据时发生异常", e);
            throw new RuntimeException("分页查询执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 校验分页参数的合法性
     * @param page 分页参数
     */
    private void validatePageParameters(Page<XxlJobLogWithInstanceVO> page) {
        if (page == null) {
            throw new IllegalArgumentException("分页参数不能为null");
        }

        if (page.getCurrent() < 1) {
            throw new IllegalArgumentException("页码必须大于等于1，当前页码: " + page.getCurrent());
        }

        if (page.getSize() < 1 || page.getSize() > 100) {
            throw new IllegalArgumentException("页大小必须在1-100之间，当前页大小: " + page.getSize());
        }

        logger.debug("分页参数校验通过，页码: {}, 页大小: {}", page.getCurrent(), page.getSize());
    }

    /**
     * 构建查询条件的QueryWrapper辅助方法
     * @param queryDTO 查询条件DTO
     * @return 构建好的QueryWrapper
     */
    public QueryWrapper<XxlJobLogWithInstanceVO> buildQueryWrapper(XxlJobLogQueryDTO queryDTO) {

        QueryWrapper<XxlJobLogWithInstanceVO> wrapper = new QueryWrapper<>();

        if (queryDTO == null) {
            logger.debug("查询条件为空，返回空条件的QueryWrapper");
            wrapper.orderByDesc("xl.trigger_time");
            return wrapper;
        }

        // 根据执行器ID查询
        if (StringUtils.isNotBlank(queryDTO.getJobGroup())) {
            wrapper.eq("xl.job_group", queryDTO.getJobGroup());
        }

        // 根据作业ID查询
        if (StringUtils.isNotBlank(queryDTO.getJobId())) {
            wrapper.eq("xl.job_id", queryDTO.getJobId());
        }

        // 根据作业实例ID查询
        if (StringUtils.isNotBlank(queryDTO.getJobInstanceId())) {
            wrapper.eq("xl.id", queryDTO.getJobInstanceId());
        }

        // 根据调度ID查询
        if (StringUtils.isNotBlank(queryDTO.getScheduleId())) {
            wrapper.eq("dji.schedule_id", queryDTO.getScheduleId());
        }

        // 根据调度日志ID查询
        if (StringUtils.isNotBlank(queryDTO.getScheduleLogId())) {
            wrapper.eq("dji.schedule_log_id", queryDTO.getScheduleLogId());
        }

        // 根据任务实例状态查询
        if (StringUtils.isNotBlank(queryDTO.getStatus())) {
            wrapper.eq("dji.status", queryDTO.getStatus());
        }

        // 根据触发码查询
        if (queryDTO.getTriggerCode() != null) {
            wrapper.eq("xl.trigger_code", queryDTO.getTriggerCode());
        }

        // 根据执行码查询
        if (queryDTO.getHandleCode() != null) {
            wrapper.eq("xl.handle_code", queryDTO.getHandleCode());
        }

        // 根据触发时间范围查询
        if (queryDTO.getTriggerTimeStart() != null) {
            wrapper.ge("xl.trigger_time", queryDTO.getTriggerTimeStart());
        }
        if (queryDTO.getTriggerTimeEnd() != null) {
            wrapper.le("xl.trigger_time", queryDTO.getTriggerTimeEnd());
        }

        // 根据执行器地址模糊查询
        if (StringUtils.isNotBlank(queryDTO.getExecutorAddress())) {
            wrapper.like("xl.executor_address", queryDTO.getExecutorAddress());
        }

        // 根据执行器处理器模糊查询
        if (StringUtils.isNotBlank(queryDTO.getExecutorHandler())) {
            wrapper.like("xl.executor_handler", queryDTO.getExecutorHandler());
        }

        // 默认按触发时间倒序排列
        wrapper.orderByDesc("xl.trigger_time");

        logger.debug("构建QueryWrapper完成，查询条件: {}", queryDTO.toString());

        return wrapper;
    }

    /**
     * 为分页查询结果填充完整日志信息
     * @param records 查询结果记录列表
     */
    private void fillLogInformation(java.util.List<XxlJobLogWithInstanceVO> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        logger.debug("开始为{}条记录填充日志信息", records.size());

        for (XxlJobLogWithInstanceVO record : records) {
            try {
                // 1. 填充XXL-Job摘要日志
                fillXxlJobSummaryLog(record);

                // 2. 填充XXL-Job详细过程日志
                fillXxlJobDetailLog(record);

                // 3. 填充SeaTunnel日志
                fillSeaTunnelLog(record);

            } catch (Exception e) {
                logger.warn("为记录 [ID: {}] 填充日志信息时发生异常: {}", record.getId(), e.getMessage());
                // 设置错误信息，避免返回null
                if (record.getXxlJobLog() == null) {
                    record.setXxlJobLog("日志获取失败: " + e.getMessage());
                }
                if (record.getXxlJobDetailLog() == null) {
                    record.setXxlJobDetailLog("详细日志获取失败: " + e.getMessage());
                }
                if (record.getSeaTunnelLog() == null) {
                    record.setSeaTunnelLog("SeaTunnel日志获取失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 填充XXL-Job摘要日志
     */
    private void fillXxlJobSummaryLog(XxlJobLogWithInstanceVO record) {
        StringBuilder summaryLog = new StringBuilder();

        // 调度结果
        summaryLog.append("调度结果: \n");
        if (record.getTriggerMsg() != null) {
            summaryLog.append(record.getTriggerMsg());
        } else {
            summaryLog.append("无调度信息");
        }
        summaryLog.append("\n\n");

        // 执行结果
        summaryLog.append("执行结果: \n");
        if (record.getHandleMsg() != null) {
            summaryLog.append(record.getHandleMsg());
        } else {
            summaryLog.append("无执行信息");
        }

        record.setXxlJobLog(summaryLog.toString());
    }

    /**
     * 填充XXL-Job详细过程日志
     */
    private void fillXxlJobDetailLog(XxlJobLogWithInstanceVO record) {
        try {
            // 构建IntegratorXxlJobLog对象用于获取详细日志
            IntegratorXxlJobLog xxlJobLog = new IntegratorXxlJobLog();
            xxlJobLog.setId(record.getId());
            xxlJobLog.setExecutorAddress(record.getExecutorAddress());
            xxlJobLog.setTriggerTime(record.getTriggerTime());
            xxlJobLog.setHandleTime(record.getHandleTime());

            // 调用现有的详细日志获取方法
            String detailLog = fetchXxlJobDetailLog(xxlJobLog);
            record.setXxlJobDetailLog(detailLog);

        } catch (Exception e) {
            logger.warn("获取XXL-Job详细日志失败 [ID: {}]: {}", record.getId(), e.getMessage());
            record.setXxlJobDetailLog("详细日志获取失败: " + e.getMessage());
        }
    }

    /**
     * 填充SeaTunnel日志
     */
    private void fillSeaTunnelLog(XxlJobLogWithInstanceVO record) {
        try {
            // 根据instanceId查找任务实例
            if (record.getInstanceId() != null) {
                DatasyncJobInstance instance = datasyncJobInstanceService.getById(record.getInstanceId());
                if (instance != null) {
                    JobContext jobContext = new JobContextImpl(instance.getJobId(), instance.getId(), "datasync-job");
                    String seaTunnelLog = seaTunnelJobService.getJobExecutionLog(jobContext);
                    record.setSeaTunnelLog(seaTunnelLog);
                } else {
                    record.setSeaTunnelLog("未找到对应的任务实例，无法获取SeaTunnel日志");
                }
            } else {
                record.setSeaTunnelLog("任务实例ID为空，无法获取SeaTunnel日志");
            }
        } catch (Exception e) {
            logger.warn("获取SeaTunnel日志失败 [InstanceID: {}]: {}", record.getInstanceId(), e.getMessage());
            record.setSeaTunnelLog("SeaTunnel日志获取失败: " + e.getMessage());
        }
    }

}
