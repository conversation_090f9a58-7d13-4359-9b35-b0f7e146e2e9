/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.source;

import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.options.ConnectorCommonOptions;
import org.apache.seatunnel.api.source.SeaTunnelSource;
import org.apache.seatunnel.api.source.SourceSplit;
import org.apache.seatunnel.api.table.connector.TableSource;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableSourceFactory;
import org.apache.seatunnel.api.table.factory.TableSourceFactoryContext;

import com.google.auto.service.AutoService;

import java.io.Serializable;

import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.AUTO_RECONNECT;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.BROKER_URLS;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.CLEAN_SESSION;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.CLIENT_ID;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.CONNECTION_TIMEOUT;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.KEEP_ALIVE_INTERVAL;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.MAX_RECONNECT_DELAY;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.MESSAGE_FORMAT;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.PASSWORD;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.POLL_TIMEOUT;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.QOS;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.SSL_ENABLED;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.TOPICS;
import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.USERNAME;

@AutoService(Factory.class)
public class MqttSourceFactory implements TableSourceFactory {

    @Override
    public String factoryIdentifier() {
        return "Mqtt";
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder()
                // 必填参数
                .required(BROKER_URLS, TOPICS)
                // Schema相关配置，二选一，schema后续会被废弃
                .exclusive(ConnectorCommonOptions.TABLE_CONFIGS, ConnectorCommonOptions.SCHEMA)
                // 可选参数
                .optional(
                        CLIENT_ID,
                        USERNAME,
                        PASSWORD,
                        CONNECTION_TIMEOUT,
                        KEEP_ALIVE_INTERVAL,
                        CLEAN_SESSION,
                        SSL_ENABLED,
                        QOS,
                        MESSAGE_FORMAT,
                        POLL_TIMEOUT,
                        AUTO_RECONNECT,
                        MAX_RECONNECT_DELAY)
                .build();
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T, SplitT extends SourceSplit, StateT extends Serializable>
    TableSource<T, SplitT, StateT> createSource(TableSourceFactoryContext context) {
        return () -> (SeaTunnelSource<T, SplitT, StateT>) new MqttSource(context.getOptions());
    }

    @Override
    public Class<? extends SeaTunnelSource> getSourceClass() {
        return MqttSource.class;
    }
}
