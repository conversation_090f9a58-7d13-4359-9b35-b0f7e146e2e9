/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.source;

import com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttSourceConfig;
import org.apache.seatunnel.api.source.SourceSplitEnumerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class MqttSourceSplitEnumerator
        implements SourceSplitEnumerator<MqttSourceSplit, MqttSourceState> {

    private static final Logger log = LoggerFactory.getLogger(MqttSourceSplitEnumerator.class);

    private final SourceSplitEnumerator.Context<MqttSourceSplit> enumeratorContext;
    private final MqttSourceConfig config;
    private final Object lock = new Object();

    // 状态管理
    private final Map<Integer, Set<MqttSourceSplit>> pendingSplits;
    private final Set<MqttSourceSplit> assignedSplits;

    private boolean noMoreSplitsSignaled = false;

    public MqttSourceSplitEnumerator(
            SourceSplitEnumerator.Context<MqttSourceSplit> enumeratorContext,
            MqttSourceConfig config,
            Set<MqttSourceSplit> assignedSplits) {
        this.enumeratorContext = enumeratorContext;
        this.config = config;
        this.assignedSplits = new HashSet<>(assignedSplits);
        this.pendingSplits = new HashMap<>();
    }

    @Override
    public void open() {
        log.info("Opening MQTT source split enumerator.");
    }

    @Override
    public void run() {
        synchronized (lock) {
            // 发现并分配分片
            discoverySplits();
            assignPendingSplits();

            // 在分配完所有分片后，向所有已注册的Reader发送“没有更多分片”的信号
            if (!noMoreSplitsSignaled) {
                Set<Integer> registeredReaders = enumeratorContext.registeredReaders();
                log.info("Signaling no more splits to all {} registered readers.", registeredReaders.size());
                for (int subtaskId : registeredReaders) {
                    enumeratorContext.signalNoMoreSplits(subtaskId);
                }
                noMoreSplitsSignaled = true;
            }
        }
    }

    @Override
    public void close() throws IOException {
        log.info("Closing MQTT source split enumerator.");
    }

    @Override
    public void addSplitsBack(List<MqttSourceSplit> splits, int subtaskId) {
        log.debug("Adding splits back from subtask {}: {}", subtaskId, splits);
        synchronized (lock) {
            // 将失败的分片放回pending列表等待重新分配
            addSplitChangeToPendingAssignments(splits);
            // 触发一次重新分配
            assignPendingSplits();
        }
    }

    @Override
    public int currentUnassignedSplitSize() {
        synchronized (lock) {
            return pendingSplits.values().stream().mapToInt(Set::size).sum();
        }
    }

    @Override
    public void registerReader(int subtaskId) {
        log.debug("Registering reader: {}", subtaskId);
        synchronized (lock) {
            // Reader注册后，尝试为其分配任务
            assignPendingSplits();
        }
    }

    @Override
    public MqttSourceState snapshotState(long checkpointId) {
        synchronized (lock) {
            return new MqttSourceState(assignedSplits);
        }
    }

    @Override
    public void notifyCheckpointComplete(long checkpointId) {
        // 无需操作
    }

    @Override
    public void handleSplitRequest(int subtaskId) {
        // 在当前设计中，所有分片一次性分配，通常不需要处理后续的split请求
    }

    /**
     * 发现并创建分区后的分片
     */
    private void discoverySplits() {
        List<String> allTopics = config.getTopics();
        int qos = config.getQos();
        int parallelism = enumeratorContext.currentParallelism();

        if (allTopics == null || allTopics.isEmpty()) {
            log.warn("No topics configured, no splits created.");
            return;
        }

        List<List<String>> partitionedTopics = new ArrayList<>(parallelism);
        for (int i = 0; i < parallelism; i++) {
            partitionedTopics.add(new ArrayList<>());
        }

        for (int i = 0; i < allTopics.size(); i++) {
            partitionedTopics.get(i % parallelism).add(allTopics.get(i));
        }
        log.info("Partitioned topics into {} splits.", parallelism);

        for (int i = 0; i < parallelism; i++) {
            List<String> topicsForSplit = partitionedTopics.get(i);
            if (!topicsForSplit.isEmpty()) {
                String splitId = "mqtt-split-" + i;
                MqttSourceSplit split = new MqttSourceSplit(splitId, topicsForSplit, qos);

                if (!assignedSplits.contains(split)) {
                    pendingSplits.computeIfAbsent(i, k -> new HashSet<>()).add(split);
                    log.info("Created partitioned MQTT split: ID={}, Topics={}", split.splitId(), split.getTopics());
                }
            }
        }
    }

    /**
     * 将待处理的分片分配给已注册的Reader
     */
    private void assignPendingSplits() {
        // 为每个已注册的Reader，分配其对应的pending分片
        for (int readerId : enumeratorContext.registeredReaders()) {
            Set<MqttSourceSplit> splitsForReader = pendingSplits.remove(readerId);
            if (splitsForReader != null && !splitsForReader.isEmpty()) {
                log.info("Assigning splits {} to reader {}", splitsForReader, readerId);

                for (MqttSourceSplit split : splitsForReader) {
                    enumeratorContext.assignSplit(readerId, split);
                }

                assignedSplits.addAll(splitsForReader);
            }
        }
    }

    /**
     * 当Reader失败时，将其分片重新加入待分配列表
     */
    private void addSplitChangeToPendingAssignments(Collection<MqttSourceSplit> splits) {
        int readerIndex = 0;
        int parallelism = enumeratorContext.currentParallelism();
        for (MqttSourceSplit split : splits) {
            int targetReader = readerIndex % parallelism;
            pendingSplits.computeIfAbsent(targetReader, k -> new HashSet<>()).add(split);
            assignedSplits.remove(split);
            readerIndex++;
        }
    }
}
