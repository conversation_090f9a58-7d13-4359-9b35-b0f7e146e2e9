/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/28
 */
package com.xmcares.platform.hookbox.common.job;

import com.xmcares.platform.hookbox.common.job.seatunnel.SeaTunnelJobInfo;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public interface JobContextService {

    void startJob(JobContext jobContext);

    void stopJob(JobContext jobContext);

    SeaTunnelJobInfo getJobLogInfo(JobContext jobContext);


}
