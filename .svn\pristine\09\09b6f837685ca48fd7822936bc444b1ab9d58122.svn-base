{"name": "xotp-ui", "version": "1.0.0", "description": "", "private": true, "scripts": {"presite": "node ./scripts/generateMock.js && cd functions && npm install", "start": "set port=8017 && cross-env APP_TYPE=site umi dev", "start:no-mock": "cross-env APP_TYPE=site MOCK=none umi dev", "build": "cross-env APP_TYPE=prod umi build", "site": "npm run presite && cross-env APP_TYPE=site npm run build && firebase deploy && npm run docker:push", "analyze": "cross-env ANALYZE=1 umi build", "lint:style": "stylelint \"src/**/*.less\" --syntax less", "lint": "eslint --ext .js src mock tests && npm run lint:style", "lint:fix": "eslint --fix --ext .js src mock tests && npm run lint:style", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js", "tslint": "npm run tslint:fix", "tslint:fix": "tslint --fix 'src/**/*.ts*'", "test": "umi test", "test:component": "umi test ./src/components", "test:all": "node ./tests/run-tests.js", "prettier": "node ./scripts/prettier.js", "docker:dev": "docker-compose -f ./docker/docker-compose.dev.yml up", "docker:build": "docker-compose -f ./docker/docker-compose.dev.yml build", "docker-prod:dev": "docker-compose -f ./docker/docker-compose.yml up", "docker-prod:build": "docker-compose -f ./docker/docker-compose.yml build", "docker-hub:build": "docker build  -f Dockerfile.hub -t  ant-design-pro ./", "docker:tag": "docker tag ant-design-pro chenshuai2144/ant-design-pro", "docker:push": "npm run docker-hub:build && npm run docker:tag && docker push chenshuai2144/ant-design-pro"}, "dependencies": {"@ant-design/compatible": "^1.0.0", "@ant-design/icons": "^4.0.0", "@ant-design/pro-card": "^1.18.42", "@ant-design/pro-components": "^1.1.6", "@ant-design/pro-form": "^1.60.0", "@ant-design/pro-layout": "^6.4.1", "@ant-design/pro-table": "^2.68.3", "@ant-design/pro-utils": "^2.16.2", "@antv/data-set": "^0.10.0", "@babel/runtime": "^7.1.5", "@monaco-editor/react": "^4.4.6", "ahooks": "^3.3.0", "antd": "^4.5.3", "array-move": "^4.0.0", "bizcharts": "^3.4.0", "bizcharts-plugin-slider": "^2.1.1-beta.1", "classnames": "^2.2.6", "compression-webpack-plugin": "^9.2.0", "core-js": "^2.6.12", "dva": "^2.4.0", "enquire-js": "^0.2.1", "file-loader": "^6.2.0", "form-render": "^1.9.12", "fr-generator": "^2.7.7", "gg-editor": "^2.0.2", "hash.js": "^1.1.5", "lodash": "^4.17.10", "lodash-decorators": "^6.0.0", "md5": "^2.2.1", "memoize-one": "^4.0.0", "moment": "^2.22.2", "monaco-editor": "^0.52.2", "node-sql-parser": "^4.5.1", "numeral": "^2.0.6", "nzh": "^1.0.3", "omit.js": "^1.0.0", "path-to-regexp": "^2.4.0", "prop-types": "^15.5.10", "qs": "^6.6.0", "rc-animate": "^2.4.4", "react": "^16.6.3", "react-container-query": "^0.11.0", "react-copy-to-clipboard": "^5.0.1", "react-document-title": "^2.0.3", "react-dom": "^16.6.3", "react-fittext": "^1.0.0", "react-media": "^1.8.0", "react-router-dom": "^4.3.1", "react-syntax-highlighter": "^15.5.0", "reqwest": "^2.0.5", "sm3": "^1.0.3", "umi-plugin-locale": "^2.11.7", "url-loader": "^4.1.1", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2"}, "devDependencies": {"@types/react": "^16.7.7", "@types/react-dom": "^16.0.10", "antd-pro-merge-less": "^0.2.0", "antd-theme-webpack-plugin": "^1.1.8", "babel-eslint": "^10.0.1", "cross-env": "^5.1.1", "cross-port-killer": "^1.0.1", "enzyme": "^3.7.0", "eslint": "^5.4.0", "eslint-config-airbnb": "^17.0.0", "eslint-config-prettier": "^3.0.1", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-compat": "^2.6.2", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^6.1.2", "eslint-plugin-markdown": "^1.0.0-beta.6", "eslint-plugin-react": "^7.11.1", "gh-pages": "^2.0.1", "husky": "^9.1.7", "jest-puppeteer": "^3.5.1", "lint-staged": "^8.1.0", "merge-umi-mock-data": "^0.0.3", "mockjs": "^1.0.1-beta3", "prettier": "1.15.2", "pro-download": "^1.0.1", "stylelint": "^9.8.0", "stylelint-config-prettier": "^4.0.0", "stylelint-config-standard": "^18.0.0", "tslint": "^5.10.0", "tslint-config-prettier": "^1.10.0", "tslint-react": "^3.6.0", "umi": "2.9.1", "umi-plugin-ga": "^1.1.3", "umi-plugin-react": "^1.2.0"}, "optionalDependencies": {"puppeteer": "^1.10.0"}, "lint-staged": {"**/*.{js,ts,tsx,json,jsx,less}": ["node ./scripts/lint-prettier.js", "git add"], "**/*.{js,jsx}": "npm run lint-staged:js", "**/*.less": "stylelint --syntax less"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "husky": {"hooks": {"pre-commit": "npm run lint-staged"}}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}