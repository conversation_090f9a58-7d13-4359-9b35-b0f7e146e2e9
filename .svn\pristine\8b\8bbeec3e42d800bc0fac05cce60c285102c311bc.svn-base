@import '~antd/lib/style/themes/default.less';
@import '../../../../utils/utils.less';
:global {
  .ant-calendar-picker {
    width: 100%;
  }
}
.btnStyle {
  margin-bottom: 16px;
  button {
    margin-right: 8px;
  }
}
.tableList {
  .tableListOperator {
    margin-bottom: 16px;
    button {
      margin-right: 8px;
    }
  }
}
.tableListForm {
  :global {
    .ant-form-item {
      margin-bottom: 24px;
      margin-right: 0;
      display: flex;
      > .ant-form-item-label {
        text-align: left;
        width: 75px;
        line-height: 32px;
        padding-right: 8px;
      }
      .ant-form-item-control {
        line-height: 32px;
      }
    }
    .ant-form-item-control-wrapper {
      flex: 1;
    }
  }
  .submitButtons {
    white-space: nowrap;
    margin-bottom: 24px;
  }
}
.expandStyle {
  margin-left: 12px;
}
.centerOperator {
  text-align: center;
  button {
    margin-right: 8px;
  }
}
.resultColumnsDiv{
  width: 92%;
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.modalBox{
  :global{
    .ant-pro-steps-form-container{
      min-width: 720px;
      margin: 0px;

    }
    .ant-tabs-content-holder{
      padding: 20px 0px 0px;
    }
    .ant-pro-card-body{
      padding: 0px 20px !important;
    }

    .ant-pro-card-header{
      padding-bottom: 24px !important;
    }
  }
}

.formLoading {
  margin: 20px 0;
  margin-bottom: 20px;
  padding: 30px 50px;
  text-align: center;
  border-radius: 4px;
}

.datasyncTaskTableLine {
  background-color: #e8e8e8;
}
