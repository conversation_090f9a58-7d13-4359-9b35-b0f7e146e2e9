/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xmcares.platform.seatunnel.connectors.mqtt.config;

import java.util.List;

/** MQTT连接配置接口 为Source和Sink提供统一的连接配置访问 */
public interface MqttConnectionConfig {

    /** 获取Broker URL列表 */
    List<String> getBrokerUrls();

    /** 获取客户端ID */
    String getClientId();

    /** 获取用户名 */
    String getUsername();

    /** 获取密码 */
    String getPassword();

    /** 获取连接超时时间 */
    int getConnectionTimeout();

    /** 获取心跳间隔 */
    int getKeepAliveInterval();

    /** 是否清除会话 */
    boolean isCleanSession();

    /** 是否启用SSL */
    boolean isSslEnabled();

    /** 是否自动重连 */
    boolean isAutoReconnect();

    /** 获取最大重连延迟 */
    int getMaxReconnectDelay();
}
