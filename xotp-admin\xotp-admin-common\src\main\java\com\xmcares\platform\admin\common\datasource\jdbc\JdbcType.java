/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：5/16/23
 */
package com.xmcares.platform.admin.common.datasource.jdbc;

import com.xmcares.platform.admin.common.datasource.DataSourceGroup;
import com.xmcares.platform.admin.common.datasource.DataSourceOptions;
import com.xmcares.platform.admin.common.datasource.DataSourceType;
import com.xmcares.platform.admin.common.jdbc.datasource.NonPoolingDataSource;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.security.krb5.KrbException;

import java.sql.Driver;
import java.util.Collection;
import java.util.Collections;
import java.util.Locale;
import java.util.Map;

import static java.util.concurrent.TimeUnit.MINUTES;
import static java.util.concurrent.TimeUnit.SECONDS;

/**
 * 包含SQL、BigData分类的所有Jdbc驱动的数据库
 *
 * <AUTHOR>
 * @since 1.4.1
 */
public enum JdbcType implements DataSourceType<JdbcDataSource> {

//    CDC_ORACLE      (DataSourceGroup.SQL,       "cdc_oracle",   "oracle.jdbc.OracleDriver",     "SELECT 1 FROM DUAL"),   // todo 去除数据源类别
    DM              (DataSourceGroup.JDBC,       "dm",           "dm.jdbc.driver.DmDriver",      "SELECT 1"),
    DORIS           (DataSourceGroup.JDBC,       "doris",        "com.mysql.cj.jdbc.Driver",     "SELECT 1"),
    MYSQL           (DataSourceGroup.JDBC,       "mysql",        "com.mysql.cj.jdbc.Driver",     "SELECT 1"),
    ORACLE          (DataSourceGroup.JDBC,       "oracle",       "oracle.jdbc.OracleDriver",     "SELECT 1 FROM DUAL"),
    SQL_SERVER      (DataSourceGroup.JDBC,       "sqlserver",    "com.microsoft.sqlserver.jdbc.SQLServerDriver", "SELECT 1"),

    OPEN_GAUSS      (DataSourceGroup.JDBC,       "opengauss",   "org.postgresql.Driver",     "SELECT 1"),
//    PHOENIX         (DataSourceGroup.SQL,"phoenix", "org.apache.phoenix.jdbc.PhoenixDriver", "SELECT 1"),
//
//    CLICKHOUSE      (DataSourceGroup.SQL, "clickhouse", "ru.yandex.clickhouse.ClickHouseDriver", "SELECT 1"),
//    DB2             (DataSourceGroup.SQL, "db2", "com.ibm.db2.jcc.DB2Driver", "SELECT 1 FROM SYSIBM.SYSDUMMY1"),
//    DERBY           (DataSourceGroup.SQL, "derby", "org.apache.derby.jdbc.EmbeddedDriver", "VALUES 1"),
//    EDB             (DataSourceGroup.SQL, "edb", "com.edb.Driver", "SELECT 1"),
//    GBASE           (DataSourceGroup.SQL, "gbase", "com.gbase.jdbc.Driver", "SELECT 1"),
//    H2              (DataSourceGroup.SQL, "h2", "org.h2.Driver", "SELECT 1"),
//    HSQL            (DataSourceGroup.SQL, "hsql", "org.hsqldb.jdbc.JDBCDriver", "SELECT 1 FROM INFORMATION_SCHEMA.SYSTEM_USERS"),
//    INFORMIX        (DataSourceGroup.SQL, "informix", "com.informix.jdbc.IfxDriver", "SELECT 1 FROM systables"),
//    KINGBASE        (DataSourceGroup.SQL, "kingbase", "com.kingbase.Driver", "SELECT 1"),
//    MARIADB         (DataSourceGroup.SQL, "mariadb", "org.mariadb.jdbc.Driver", "SELECT 1"),
//    OCEANBASE       (DataSourceGroup.SQL, "oceanbase", "com.mysql.jdbc.Driver", "SELECT 1"),
//    POSTGRESQL      (DataSourceGroup.SQL, "postgresql", "org.postgresql.Driver", "SELECT 1"),
//    SQLITE          (DataSourceGroup.SQL, "sqlite", "org.sqlite.JDBC", "SELECT 1"),
//
//    SYBASE          (DataSourceGroup.SQL, "sybase", "com.sybase.jdbc4.jdbc.SybDriver", "SELECT 1"),

    // ------ 大数据数据库 ------
//    HDFS        (DataSourceGroup.JDBC,   "hdfs1.1",      "org.apache.hadoop.hdfs.jdbc.HdfsDriver", "SELECT 1") ,
//    HBASE_1_1   (DataSourceGroup.JDBC,   "hbase1.1",     "org.apache.hadoop.hbase.jdbc.Driver",  "SELECT 1"),
//    HIVE_1_1    (DataSourceGroup.JDBC,   "hive1.1",      "org.apache.hive.jdbc.HiveDriver",          "SELECT 1"),
//    HIVE        (DataSourceGroup.JDBC,   "hive",      "org.apache.hive.jdbc.HiveDriver",          "SELECT 1"){
//        @Override
//        public JdbcDataSource createDataSource(DataSourceOptions options) {
//            configureKerberos(options);
//            return super.createDataSource(options);
//        }
//    },

    //ELASTIC_SEARCH("elastic_search", "com.alibaba.xdriver.elastic.jdbc.ElasticDriver", "SELECT 1"),
    //KYLIN(DatasourceGroup.BIGDATA,"kylin", "org.apache.kylin.jdbc.Driver", "SELECT 1"),
    ;

    private static final Logger logger = LoggerFactory.getLogger(JdbcType.class);


    private final DataSourceGroup group;

    private final String typeName;

    private final String driverClassName;

    private final String validationSQL;

    JdbcType(DataSourceGroup group, String typeName, String driverClassName) {
        this(group, typeName, driverClassName, null);
    }

    JdbcType(DataSourceGroup group, String typeName, String driverClassName, String validationSQL) {
        this.group = group;
        this.typeName = typeName;
        this.driverClassName = driverClassName;
        this.validationSQL = validationSQL;
    }


    public JdbcDataSource createDataSource(DataSourceOptions options) {
         return createPoolingDataSource(options);
    }

    /**
     * Return the driver class name.
     *
     * @return the class name or {@code null}
     */
    public String getDriverClassName() {
        return this.driverClassName;
    }

    public String getTypeName() {
        return this.typeName;
    }

    public DataSourceGroup getGroup() {
        return group;
    }

    /**
     * Return the validation query.
     *
     * @return the validation query or {@code null}
     */
    public String getValidationSQL() {
        return this.validationSQL;
    }

    /**
     * Find a {@link JdbcType} for the given URL.
     *
     * @param url the JDBC URL
     * @return the database driver or null if not found
     */
    public static JdbcType fromJdbcUrl(String url) {
        if (null != url && !url.isEmpty()) {
            if (!url.startsWith("jdbc")) {
                return null;
            }
            String urlWithoutPrefix = url.substring("jdbc".length()).toLowerCase(Locale.ENGLISH);
            for (JdbcType driver : values()) {
                for (String urlPrefix : driver.getUrlPrefixes()) {
                    String prefix = ":" + urlPrefix + ":";
                    if (urlWithoutPrefix.indexOf(prefix) > 0) {
                        return driver;
                    }
                }
            }
        }
        return null;
    }

    /**
     * Find a {@link JdbcType} for the given product name.
     *
     * @param dbTypeName database type name
     * @return the database driver or null if not found
     */
    public static JdbcType fromTypeName(String dbTypeName) {
        if (dbTypeName != null && !dbTypeName.isEmpty()) {
            for (JdbcType candidate : values()) {
                if (candidate.equalsTypeName(dbTypeName)) {
                    return candidate;
                }
            }
        }
        return null;
    }


    /**
     * 创建池化的基于Hikari的数据源
     * @param options 数据源配置
     * @return Hikari数据源
     */
    public JdbcDataSource createPoolingDataSource(DataSourceOptions options) {
        String driverClassName = options.getDriverClassName();
        if (null == driverClassName || driverClassName.isEmpty()) {
            //使用默认驱动
            driverClassName = this.getDriverClassName();
            options.put(DataSourceOptions.KEY_JDBC_DRIVER_CLASS_NAME, driverClassName);
            logger.info("未配置[driverClassName]数据库驱动，使用默认驱动：{}", driverClassName);
        }
        HikariDataSource dataSource = null;
        try {
            dataSource = new HikariDataSource();
            dataSource.setJdbcUrl(options.getUrl());
            dataSource.setDriverClassName(driverClassName);
            dataSource.setUsername(options.getUsername());
            dataSource.setPassword(options.getPassword());
            dataSource.setPoolName(options.getName());
            dataSource.setConnectionTimeout(options.getForLong("connectionTimeout", SECONDS.toMillis(30)));
            dataSource.setIdleTimeout(options.getForLong("idleTimeout", MINUTES.toMillis(10)));
            dataSource.setMaxLifetime(options.getForLong("maxLifetime", MINUTES.toMillis(30)));
            dataSource.setMaximumPoolSize(options.getForInteger("maximumPoolSize", 10));
            dataSource.setMinimumIdle(options.getForInteger("minimumIdle", 1));
            dataSource.setAutoCommit(options.getForBoolean("autoCommit", true));
            dataSource.setCatalog(options.getForString("catalog", null));
            dataSource.setSchema(options.getForString("schema", null));
        } catch (Exception e) {
            if (dataSource != null) {
                try {
                    dataSource.close();
                } catch (Exception e1) {
                    //do nothing
                }
            }
            throw new RuntimeException("创建数据源失败：" + e.getMessage(), e);
        }
        return new JdbcDataSourceWrapper(options.getName(), dataSource);
    }

    /**
     * 创建数据源，注意Hive数据源创建有所不同.
     *
     * @param options 数据源配置
     * @return 简要数据源对象
     */
    @SuppressWarnings("unchecked")
    public JdbcDataSource createNonPoolingDataSource(DataSourceOptions options) {
        NonPoolingDataSource dataSource = new NonPoolingDataSource();
        String schema = options.getSchema();
        if (null != schema && !schema.isEmpty()) {
            dataSource.setSchema(schema);
        }
        dataSource.setUsername(options.getUsername());
        dataSource.setPassword(options.getPassword());
        dataSource.setUrl(options.getUrl());
        String driverClassName = options.getDriverClassName();
        if (null == driverClassName || driverClassName.isEmpty()) {
            //使用默认驱动
            driverClassName = this.getDriverClassName();
        }
        try {
            Class<?> aClass = Class.forName(driverClassName);
            dataSource.setDriverClass((Class<? extends Driver>) aClass);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(String.format("加载不到数据源驱动[ %s ]", driverClassName));
        }
        return new JdbcDataSourceWrapper(options.getName(), dataSource);
    }


    /**
     * 配置kerberos认证
     * @param options 数据源配置
     */
    private static void configureKerberos(DataSourceOptions options) {
        String enableKerberos = (String) options.get("enableKerberos");
        if (!"3".equals(enableKerberos)) {
            return;
        }
        logger.info("启用并开始[enableKerberos={}]Kerberos认证配置", enableKerberos);

        configureKrb5Conf(options);
        configureKeytabConf(options);
    }

    /**
     * 配置kerberos
     * @param krbOptions 数据源配置
     */
    private static void configureKrb5Conf(Map<String, Object> krbOptions) {
        //===============  krb5.conf 配置 ================
        String krb5Path = (String) krbOptions.get("krb5path");
        if (!StringUtils.isNotEmpty(krb5Path)) {
            logger.warn("启用Krb认证，但未配置kerberos.krb5Path(已忽略启用)");
            return;
        }
        logger.info("设置kerberos认证配置文件路径: {}", krb5Path);
        // 指定 krb5.conf 文件路径
        System.setProperty("java.security.krb5.conf", krb5Path);
        // 刷新 krb5.conf 文件
        try {
            sun.security.krb5.Config.refresh();
        } catch (KrbException e) {
            logger.warn("刷新[sun.security.krb5.Config.refresh]Kerberos配置失败", e);
        }
    }

    private static void configureKeytabConf(Map<String, Object> krbOptions) {
        //===============  keytabPath 配置 ================
        String keytabPath = (String) krbOptions.get("keytabPath");
        if (!StringUtils.isNotEmpty(keytabPath)) {
            logger.warn("启用Krb认证，但未配置kerberos.keytabPath(已忽略启用)");
            return;
        }
        // 指定 Kerberos 认证参数
        String principal = (String) krbOptions.get("principal");
        logger.info("设置kerberos认证keytab文件: {}, principal: {}", keytabPath, principal);

        // 配置 Hadoop 的安全认证
        /*org.apache.hadoop.conf.Configuration conf = new org.apache.hadoop.conf.Configuration();
        conf.set("hadoop.security.authentication", "KERBEROS");
        UserGroupInformation.setConfiguration(conf);
        // 使用 keyTab 文件进行登录
        try {
            UserGroupInformation.loginUserFromKeytab(principal, keytabPath);
        } catch (IOException e) {
            throw new SystemException("kerberos认证失败", e);
        }*/
    }

    private Collection<String> getUrlPrefixes() {
        return Collections.singleton(name().toLowerCase(Locale.ENGLISH));
    }

}
