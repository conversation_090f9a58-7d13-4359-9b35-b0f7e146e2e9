/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/1
 */
package com.xmcares.platform.hookbox.integrator.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmcares.platform.hookbox.integrator.mapper.DatasyncJobMapper;
import com.xmcares.platform.hookbox.integrator.model.DatasyncJob;
import com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
@Service
public class DatasyncJobService extends ServiceImpl<DatasyncJobMapper, DatasyncJob> {

    /**
     * 根据 jobId 获取集成任务
     *
     * @param jobId 任务 ID
     * @return 集成任务
     */
    public DatasyncJob getDatasyncJobById(String jobId) {
        if (StringUtils.isBlank(jobId)) {
            throw new IllegalArgumentException("jobId is null");
        }
        return this.getById(jobId);
    }


}
