/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.exception;

import org.apache.seatunnel.common.exception.SeaTunnelErrorCode;

public enum MqttConnectorErrorCode implements SeaTunnelErrorCode {
    CONNECTION_FAILED("MQTT-01", "Failed to connect to MQTT broker"),
    INVALID_CONFIGURATION("MQTT-02", "Invalid MQTT configuration"),
    SUBSCRIPTION_FAILED("MQTT-03", "Failed to subscribe to MQTT topics"),
    PUBLISH_FAILED("MQTT-04", "Failed to publish message to MQTT broker"),
    SERIALIZATION_ERROR("MQTT-05", "Failed to serialize/deserialize message"),
    CLIENT_ERROR("MQTT-06", "MQTT client error");

    private final String code;
    private final String description;

    MqttConnectorErrorCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
