/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/24
 */
package com.xmcares.platform.seatunnel.connectors.artemis.source;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlFactory;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.activemq.artemis.api.core.*;
import org.apache.activemq.artemis.api.core.client.*;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.source.Collector;
import org.apache.seatunnel.api.source.SourceReader;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public class ArtemisSourceReader implements SourceReader<SeaTunnelRow, ArtemisSourceSplit> {
    private final Logger log = LoggerFactory.getLogger(ArtemisSourceReader.class);

    private final ReadonlyConfig config;
    private final Context readerContext;
    private final SeaTunnelRowType producedType;
    private final Queue<ArtemisSourceSplit>
            splits; // Splits for future extension, not heavily used for simple MQ

    private volatile boolean running = false;

    private ServerLocator locator;
    private ClientSessionFactory sessionFactory;
    private ClientSession session;
    private ClientConsumer consumer;

    private final String format;
    private final long pollTimeoutMs;
    private final int batchSize;
    private final long pollIntervalMs;

    // Parsers
    private final ObjectMapper jsonMapper;
    private final XmlMapper xmlMapper; // For XML parsing

    public ArtemisSourceReader(
            ReadonlyConfig config,
            Context readerContext,
            SeaTunnelRowType producedType) {
        this.config = config;
        this.readerContext = readerContext;
        this.producedType = producedType;
        this.splits = new ConcurrentLinkedQueue<>(); // Initialize the queue

        this.format = config.get(ArtemisSourceFactory.FORMAT);
        this.pollTimeoutMs = config.get(ArtemisSourceFactory.POLL_TIMEOUT_MS);
        this.batchSize = config.get(ArtemisSourceFactory.BATCH_SIZE);
        this.pollIntervalMs = config.get(ArtemisSourceFactory.POLL_INTERVAL_MS);

        JsonFactory factory = new JsonFactory();
        factory.enable(JsonParser.Feature.ALLOW_SINGLE_QUOTES);
        factory.enable(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES);
        jsonMapper = new ObjectMapper(factory);
        configureMapper(jsonMapper);
        jsonMapper.registerModule(new JavaTimeModule());
        jsonMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        XmlFactory xmlFactory = new XmlFactory();
        xmlMapper = new XmlMapper(xmlFactory);
        configureMapper(xmlMapper);
        xmlMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    }
    private void configureMapper(ObjectMapper mapper) {
        mapper.disable(SerializationFeature.WRAP_ROOT_VALUE)
                .enable(SerializationFeature.INDENT_OUTPUT);
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false);
    }

    @Override
    public void open() throws Exception {
        log.info("ArtemisSourceReader opened. Initializing Artemis Core Client.");

        String brokerUrl = config.get(ArtemisSourceFactory.BROKER_URL);
        String username = config.getOptional(ArtemisSourceFactory.USERNAME).orElse(null);
        String password = config.getOptional(ArtemisSourceFactory.PASSWORD).orElse(null);
        String clientId = config.getOptional(ArtemisSourceFactory.CLIENT_ID).orElse(null);
        String topicName = config.getOptional(ArtemisSourceFactory.TOPIC).orElse(null);
        String subscriptionName = config.getOptional(ArtemisSourceFactory.SUBSCRIPTION).orElse(null);

        // 参数检查：对于主题持久订阅，这些字段是必需的
        if (topicName == null || clientId == null || subscriptionName == null) {
            throw new IllegalArgumentException("For topic durable subscription, 'topic', 'clientId', and 'subscription' are required.");
        }
        // Artemis 内部为这个持久订阅自动创建的队列名称
        String durableSubscriptionQueueName = clientId + "." + subscriptionName;
        // ************************************

        try {
            locator = ActiveMQClient.createServerLocator(brokerUrl);
            locator.setReconnectAttempts(-1); // 永远尝试重连
            locator.setInitialConnectAttempts(-1); // 初始连接也永远尝试
            locator.setCallFailoverTimeout(60000L); // 故障转移超时时间 (60秒)
            locator.setRetryInterval(1000);
            sessionFactory = locator.createSessionFactory();

            // 创建会话
            // username, password: 认证信息
            // autoCommitSends: false (发送消息后是否自动提交，对于消费者不重要)
            // autoCommitAcks: true (接收消息后是否自动确认，通常希望自动)
            // xa: false (是否使用 XA 事务，通常不需要，设置为 true 会增加复杂性)
            // preAcknowledge: false (是否预先确认消息，设置为 true 可能导致消息丢失，通常设置为 false)
            // ackBatchSize: -1 (自动批处理确认大小)
            // consumerID: clientId (对于持久订阅非常重要)
            session = sessionFactory.createSession(
                    username, password, false, true, false, false, -1, clientId); // <-- XA 设置为 false
            try {
                QueueConfiguration queueConfig = new QueueConfiguration(new SimpleString(durableSubscriptionQueueName))
                        .setAddress(new SimpleString(topicName)) // 将队列绑定到主题地址
                        .setDurable(true)                        // 队列是持久的
                        .setTemporary(false)                     // 队列不是临时的
                        .setRoutingType(RoutingType.MULTICAST)   // 主题通常使用 MULTICAST 路由
                        .setAutoCreated(true);                   // 允许 Broker 自动创建队列（如果不存在）

                session.createQueue(queueConfig);
            } catch (ActiveMQQueueExistsException e) {
                // 如果队列已经存在，这通常是正常情况，特别是对于持久订阅。
                // 我们假设它以兼容的属性存在，并继续执行。
                log.warn("Durable subscription queue '{}' for address '{}' already exists. Proceeding to consume. Details: {}",
                        durableSubscriptionQueueName, topicName, e.getMessage());
                // 如果你希望在调试时看到更详细的信息，可以使用 log.debug(..., e);
            } catch (ActiveMQException e) {
                // 对于其他类型的 Artemis 异常，我们认为它是更严重的问题，并重新抛出，使作业失败
                log.error("Failed to ensure durable subscription queue '{}' for address '{}' due to unexpected Artemis error: {}",
                        durableSubscriptionQueueName, topicName, e.getMessage(), e);
                throw e; // 重新抛出异常，阻止任务继续
            }
            // ****** 关键修改：消费者连接到正确的持久订阅队列 ******
            consumer = session.createConsumer(new SimpleString(durableSubscriptionQueueName));
            // *******************************************************

            log.info(
                    "Configured for Topic Consumption: BrokerUrl='{}', TopicAddress='{}', " +
                            "Client ID='{}', Durable Subscription Name='{}', Consuming from Queue='{}'",
                    brokerUrl, topicName, clientId, subscriptionName, durableSubscriptionQueueName);

            session.start();
            this.running = true;
            log.info(
                    "Artemis Core Client initialized successfully. Reader starting to poll messages.");

        } catch (Exception e) {
            log.error("Failed to initialize Artemis client: {}", e.getMessage(), e);
            close();
            throw e;
        }
    }

    @Override
    public void close() throws IOException {
        log.info("ArtemisSourceReader closing.");
        this.running = false; // Stop polling loop
        try {
            if (consumer != null) {
                consumer.close();
                log.info("Artemis consumer closed.");
            }
            if (session != null) {
                session.stop(); // Stop the session first
                session.close();
                log.info("Artemis session closed.");
            }
            if (sessionFactory != null) {
                sessionFactory.close();
                log.info("Artemis session factory closed.");
            }
            if (locator != null) {
                locator.close();
                log.info("Artemis server locator closed.");
            }
        } catch (ActiveMQException e) {
            throw new IOException("Failed to close Artemis resources: " + e.getMessage(), e);
        }
        log.info("ArtemisSourceReader closed.");
    }

    @Override
    public void pollNext(Collector<SeaTunnelRow> output) throws Exception {
        if (!running) {
            // If not running, typically means close() has been called or open() failed.
            // Return to let SeaTunnel engine stop or retry.
            return;
        }

        List<SeaTunnelRow> records = new ArrayList<>();
        long currentPollStartTime = System.currentTimeMillis();

        while (records.size() < batchSize
                && (System.currentTimeMillis() - currentPollStartTime < pollTimeoutMs)) {
            ClientMessage message =
                    consumer.receive(pollTimeoutMs); // Blocks until message or timeout
            if (message != null) {
                try {
                    SeaTunnelRow row = parseMessage(message);
                    if (row != null) {
                        records.add(row);
                    }
                    message.acknowledge(); // Acknowledge message after successful processing
                } catch (Exception e) {
                    log.error(
                            "Failed to parse or process Artemis message. Message ID: {}. Error: {}",
                            message.getMessageID(),
                            e.getMessage(),
                            e);
                    // In production, you might want to send bad messages to a Dead Letter Queue
                    // (DLQ)
                    // For now, we acknowledge to prevent re-processing this problematic message
                    // infinitely.
                    message.acknowledge(); // Still acknowledge the message to prevent reprocessing
                    // this specific bad message
                }
            } else {
                // No message received within the poll timeout for this iteration.
                // Break and allow the main loop to re-evaluate or sleep.
                break;
            }
        }

        for (SeaTunnelRow record : records) {
            output.collect(record); // Emit records to SeaTunnel engine
        }

        if (records.isEmpty()) {
            // If no messages were received in this poll attempt, sleep to avoid busy-waiting.
            Thread.sleep(pollIntervalMs);
        }
    }

    /**
     * Parses the Artemis ClientMessage into a SeaTunnelRow based on the configured format and
     * schema.
     */
    private SeaTunnelRow parseMessage(ClientMessage message) throws IOException {
        // Read the message body from the buffer
        byte[] bodyBytes = new byte[message.getBodyBuffer().readableBytes()];
        message.getBodyBuffer().readBytes(bodyBytes);
        String messageContent = new String(bodyBytes, StandardCharsets.UTF_8);

        switch (format) {
            case "json":
                return parseJson(messageContent);
            case "xml":
                return parseXml(messageContent);
            case "text":
                // For "text" format, simply put the raw string into the first column
                return new SeaTunnelRow(new Object[] {messageContent});
            default:
                throw new IllegalArgumentException("Unsupported message format: " + format);
        }
    }

    /** Parses a JSON string into a SeaTunnelRow using the provided schema. */
    private SeaTunnelRow parseJson(String jsonContent) throws IOException {
        JsonNode rootNode = jsonMapper.readTree(jsonContent);
        return convertJsonNodeToSeaTunnelRow(rootNode, producedType);
    }

    /**
     * Parses an XML string into a SeaTunnelRow using the provided schema. XmlMapper transforms XML
     * into a JSON-like tree structure for uniform processing.
     */
    private SeaTunnelRow parseXml(String xmlContent) throws IOException {
        // XmlMapper converts XML to a JSON-like structure, then we process it similarly to JSON
        JsonNode rootNode = xmlMapper.readTree(xmlContent);
        return convertJsonNodeToSeaTunnelRow(rootNode, producedType);
    }

    /**
     * Converts a Jackson JsonNode (representing JSON or XML) to a SeaTunnelRow based on the target
     * SeaTunnelRowType schema. This method handles basic type conversions. For nested structures
     * (ROW, MAP, ARRAY), it needs to be extended with recursive calls.
     */
    private SeaTunnelRow convertJsonNodeToSeaTunnelRow(JsonNode node, SeaTunnelRowType schema) {
        Object[] fields = new Object[schema.getTotalFields()];
        for (int i = 0; i < schema.getTotalFields(); i++) {
            String fieldName = schema.getFieldName(i);
            SeaTunnelDataType<?> fieldType = schema.getFieldType(i);
            JsonNode fieldNode = node.get(fieldName);

            if (fieldNode == null || fieldNode.isNull()) {
                fields[i] = null; // Set to null if field is missing or null
                continue;
            }
            try {
                // Basic type conversion based on SeaTunnel's SQL Type
                switch (fieldType.getSqlType()) {
                    case STRING:
                        fields[i] = fieldNode.asText();
                        break;
                    case BOOLEAN:
                        fields[i] = fieldNode.asBoolean();
                        break;
                    case TINYINT:
                    case SMALLINT:
                    case INT:
                        fields[i] = fieldNode.asInt();
                        break;
                    case BIGINT:
                        fields[i] = fieldNode.asLong();
                        break;
                    case FLOAT:
                        fields[i] = (float) fieldNode.asDouble(); // Cast double to float
                        break;
                    case DOUBLE:
                        fields[i] = fieldNode.asDouble();
                        break;
                    case DECIMAL:
                        fields[i] = new java.math.BigDecimal(fieldNode.asText());
                        break;
                    case BYTES:
                        // Assuming base64 encoded string if coming from JSON/XML that needs to be
                        // bytes
                        fields[i] = fieldNode.asText().getBytes(StandardCharsets.UTF_8); // Or
                        // Base64.getDecoder().decode(fieldNode.asText())
                        break;
                    case DATE:
                        // Assuming date string (e.g., "YYYY-MM-DD")
                        fields[i] = java.sql.Date.valueOf(fieldNode.asText());
                        break;
                    case TIME:
                        // Assuming time string (e.g., "HH:MM:SS")
                        fields[i] = java.sql.Time.valueOf(fieldNode.asText());
                        break;
                    case TIMESTAMP:
                        // Assuming timestamp string or epoch long
                        try {
                            // Try parsing as ISO 8601 string first (e.g., "2023-01-01T10:30:00")
                            fields[i] =
                                    java.sql.Timestamp.valueOf(
                                            fieldNode
                                                    .asText()
                                                    .replace("T", " ")); // MySQL compatible format
                        } catch (IllegalArgumentException e) {
                            // If string parsing fails, try epoch milliseconds
                            fields[i] = new java.sql.Timestamp(fieldNode.asLong());
                        }
                        break;
                        // Implement conversions for complex types if needed (ROW, MAP, ARRAY)
                    case ROW:
                        // Example: If fieldType is a SeaTunnelRowType for a nested struct
                        if (fieldNode.isObject()) {
                            fields[i] =
                                    convertJsonNodeToSeaTunnelRow(
                                            fieldNode, (SeaTunnelRowType) fieldType);
                        } else {
                            fields[i] = null;
                        }
                        break;
                    case MAP:
                        // Example: Assuming MAP<STRING, STRING>
                        if (fieldNode.isObject()) {
                            Map<String, String> map = new HashMap<>();
                            fieldNode
                                    .fields()
                                    .forEachRemaining(
                                            entry ->
                                                    map.put(
                                                            entry.getKey(),
                                                            entry.getValue().asText()));
                            fields[i] = map;
                        } else {
                            fields[i] = null;
                        }
                        break;
                    case ARRAY:
                        // Example: Assuming ARRAY<STRING>
                        if (fieldNode.isArray()) {
                            List<String> list = new ArrayList<>();
                            fieldNode
                                    .elements()
                                    .forEachRemaining(element -> list.add(element.asText()));
                            fields[i] = list.toArray(new String[0]); // Or specific array type
                        } else {
                            fields[i] = null;
                        }
                        break;
                    default:
                        // Fallback: If type is not recognized, attempt to get as text.
                        // This might cause errors if downstream expects a specific type.
                        log.warn(
                                "Unsupported or unrecognized field type '{}' for field '{}'. Attempting to get as text.",
                                fieldType.getSqlType(),
                                fieldName);
                        fields[i] = fieldNode.asText();
                        break;
                }
            } catch (Exception e) {
                log.error(
                        "Error converting field '{}' with type '{}' from value '{}': {}",
                        fieldName,
                        fieldType.getSqlType(),
                        fieldNode.asText(),
                        e.getMessage(),
                        e);
                fields[i] = null; // Set to null on conversion error
            }
        }
        return new SeaTunnelRow(fields);
    }

    @Override
    public List<ArtemisSourceSplit> snapshotState(long checkpointId) throws Exception {
        // For an unbounded message queue, typically no splits are returned for checkpointing
        // if the consumer automatically handles acknowledgements and offset management internally.
        // If you were implementing manual offset tracking, you would return the current offsets
        // here.
        log.info(
                "Snapshotting reader state for checkpoint {}. Returning empty splits as no explicit offset management is done by reader.",
                checkpointId);
        return Collections.emptyList();
    }

    @Override
    public void addSplits(List<ArtemisSourceSplit> splits) {
        this.splits.addAll(splits);
        log.info("Received {} splits. Total splits: {}", splits.size(), this.splits.size());
        // For a single-split-per-reader model, this method might just receive the initial split.
        // For dynamically discovered partitions/queues, it would handle adding new splits.
    }

    @Override
    public void handleNoMoreSplits() {
        log.info(
                "No more splits from enumerator. Current reader will continue consuming until manually stopped or stream ends.");
        // This signifies that the enumerator will not provide any more splits.
        // For an unbounded source, the reader still continues to poll messages.
    }

    @Override
    public void notifyCheckpointComplete(long checkpointId) throws Exception {
        log.info(
                "Checkpoint {} complete. All messages up to this checkpoint are considered processed.",
                checkpointId);
        // For an auto-acknowledging consumer, this simply indicates that the checkpoint succeeded.
        // If manual acknowledgement/transactional commits were in use, this is where you'd commit
        // the acknowledged messages up to this checkpoint.
    }
}
