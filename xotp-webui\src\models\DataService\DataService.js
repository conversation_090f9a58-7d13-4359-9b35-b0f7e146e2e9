/**
 * @Copyright 厦门民航凯亚有限公司
 * @File 数据服务模型设计Model层
 * <AUTHOR>
 * @Date 2022-09-06
 */
import {
  getDataset,
  getDatasourceTable,
  getDatasourceTableField,
  saveDataset,
} from '@/services/DataService/Dataset';
import { datasyncSourceList } from '@/services/Integrate/DataSync/DataSync';

import {
  dataState,
  errorMessage,
  formatAstCol,
  setLimit,
  astify,
  sqlify,
  setOrderby,
  setGroupby,
  isNumberVal,
  setWhere,
  addWhere,
  removeWhere,
  removeTableAst,
} from '@/utils/DataService';

export default {
  namespace: 'DataService',
  state: {
    ...dataState,
  },
  effects: {
    *executeInit({ payload, callback }, { put, take }) {
      yield put.resolve({ type: 'init' });
      yield put({ type: 'fetchDataSources' });
      //等待 fetchDataSources任务结束
      //yield take('fetchDataSources/@@end');
      yield put({ type: 'fetchDataset', payload, callback });
    },
    *fetchDataset({ payload, callback }, { call, put, select }) {
      const state = yield select(state => state.DataService);
      const dataset = state.dataset;
      const response = yield call(getDataset, payload);

      if (response && response.success && response.data) {
        yield put({
          type: 'saveDataset',
          payload: response.data,
        });
        if (!(dataset && dataset.datasourceId == response.data.datasourceId)) {
          yield put({
            type: 'fetchDataSourceTables',
            payload: {
              datasourceId: response.data.datasourceId,
              datasetSql: response.data.datasetSql,
              datasetMode: response.data.datasetMode,
            },
          });
        }
      }
      callback && callback(response);
    },
    *fetchDataSources(_, { call, put, select }) {
      const state = yield select(state => state.DataService);
      const dataSourceList = state.dataSourceList;

      if (!(dataSourceList && dataSourceList.length > 0)) {
        const response = yield call(datasyncSourceList);
        yield put({
          type: 'saveDataSources',
          payload: response,
        });
      }
    },
    *fetchDataSourceTables({ payload }, { call, put }) {
      const response = yield call(getDatasourceTable, payload.datasourceId);
      if (response && response.success && response.data) {
        yield put({
          type: 'saveDataSourceTables',
          payload: response.data,
        });
        if (payload.datasetMode != '1') {
          yield put({
            type: 'updateSqlAst',
            payload: {
              tables: response.data,
              datasetSql: payload.datasetSql,
            },
          });
        } else {
          yield put({
            type: 'changeSqlAst',
            payload: {
              ...dataState.sqlAst,
            },
          });
        }
      }
    },
    *updateSqlAst({ payload }, { call, put, select, all }) {
      const { tables, datasetSql } = payload;
      const state = yield select(state => state.DataService);
      const { sqlAst, dataset } = state;
      if (!tables) {
        errorMessage('“' + dataset.datasourceName + '“中获取不到表！');
        yield put({
          type: 'changeSqlAst',
          payload: {
            ...dataState.sqlAst,
          },
        });
      }
      if (datasetSql) {
        const ast = astify(datasetSql);
        if (!sqlAst.from && ast) {
          const fromObject = {};
          if (ast.from && ast.from.length > 0) {
            yield all(
              ast.from.map(function*(item) {
                const obj = {};
                const table = tables.find(t => t.name === item.table);
                if (table) {
                  obj.table = {
                    ...table,
                    name: item.table,
                    as: item.as || table.name,
                  };
                  if (item.join) {
                    obj.table.join = item.join;
                    obj.table.on = item.on;
                  }

                  const response = yield call(getDatasourceTableField, {
                    datasourceId: table.datasourceId,
                    datatableId: table.id,
                  });
                  const columns = [];
                  if (ast.columns && typeof ast.columns == 'object') {
                    if (response && response.success && response.data) {
                      response.data.forEach(item => {
                        const i =
                          ast.columns && ast.columns.length > 0
                            ? ast.columns.findIndex(col => {
                                if (col.expr.type == 'aggr_func') {
                                  return (
                                    col.expr.args.expr.table === obj.table.as &&
                                    col.expr.args.expr.column === item.name
                                  );
                                } else {
                                  return (
                                    col.expr.table === obj.table.as && col.expr.column === item.name
                                  );
                                }
                              })
                            : -1;

                        const col = ast.columns[i];
                        let column = {};
                        if (col) {
                          column = {
                            as: col.as,
                            isShow: true,
                          };
                          if (col.expr.type == 'aggr_func') {
                            column.fun = col.expr.name;
                          }
                        }
                        //orderby
                        columns.push({
                          ...item,
                          ...column,
                        });
                      });
                    }
                  }
                  obj.columns = columns;
                } else {
                  removeTableAst(item.table, fromObject, ast);
                  errorMessage(item.table + '在“' + dataset.datasourceName + '“中不存在！');
                }
                fromObject[item.table] = obj;
              })
            );
          }
          yield put({
            type: 'saveFromObject',
            payload: {
              fromObject,
              sqlAst: ast,
            },
          });
        } else {
          yield put({
            type: 'changeSqlAst',
            payload: {
              ...dataState.sqlAst,
            },
          });
        }
      } else {
        yield put({
          type: 'changeSqlAst',
          payload: {
            ...dataState.sqlAst,
          },
        });
      }
    },
    *fetchDataSourceTableField({ payload }, { call, put }) {
      const { oTable, table } = payload;
      const response = yield call(getDatasourceTableField, {
        datasourceId: table.datasourceId,
        datatableId: table.id,
      });
      if (response && response.success && response.data) {
        let columns = response.data;
        columns = columns.map(col => {
          return { ...col, isShow: true };
        });
        yield put({
          type: 'changeAstTable',
          payload: {
            ...payload,
            columns,
          },
        });
      }
    },
    *sendDataset({ payload, callback }, { call, put, select }) {
      const state = yield select(state => state.DataService);

      const response = yield call(saveDataset, payload);
      if (response && response.success == true && response.data?.datasetId) {
        yield put({
          type: 'fetchDataset',
          payload: payload.datasetId,
        });
      }
      callback && callback(response);
    },
  },
  reducers: {
    init(state) {
      return {
        ...dataState,
        dataSourceList: state.dataSourceList,
      };
    },
    saveDataset(state, { payload }) {
      let params = [];
      let datasetMode = '0';
      try {
        if (payload && payload.datasetParameter) {
          params = JSON.parse(payload.datasetParameter);
        }
        if (payload.datasetMode) {
          datasetMode = payload.datasetMode.toString();
        }
      } catch (e) {}
      return {
        ...state,
        dataset: {
          ...payload,
          datasetId: payload?.id,
          params,
        },
        datasetMode,
      };
    },
    saveDataSources(state, { payload }) {
      return {
        ...state,
        dataSourceList:
          payload && payload.length > 0 ? payload.filter(item => item.category === 'JDBC') : [],
      };
    },
    saveDataSourceTables(state, { payload }) {
      return {
        ...state,
        dataSourceTableList: payload,
      };
    },
    saveFromObject(state, { payload }) {
      return {
        ...state,
        sqlAst: payload.sqlAst,
        fromObject: payload.fromObject,
      };
    },
    changeSqlAst(state, { payload }) {
      return {
        ...state,
        sqlAst: payload,
      };
    },
    changeAstTable(state, { payload }) {
      const { sqlAst, fromObject } = state;
      if (!payload) {
        return state;
      }
      const { oTable, table, columns } = payload;
      if (!table) {
        return state;
      }
      let from =
        sqlAst.from && sqlAst.from.length > 0
          ? sqlAst.from.filter(table => table.table !== table.name)
          : [];
      let cols =
        sqlAst.columns && typeof sqlAst.columns == 'object' && sqlAst.columns.length > 0
          ? sqlAst.columns.filter(col => {
              if (col.expr.type == 'aggr_func') {
                return col.expr.args.expr.table !== table.as;
              } else {
                return col.expr.table !== table.as;
              }
            })
          : [];
      if (oTable && oTable.name) {
        delete fromObject[oTable.name];
        cols = cols.filter(col => {
          if (col.expr.type == 'aggr_func') {
            return col.expr.args.expr.table !== oTable.as;
          } else {
            return col.expr.table !== oTable.as;
          }
        });
        from = from.filter(table => {
          if (table.join) {
            delete fromObject[table.table];
            return table.on.left.table !== oTable.name && table.on.right.table !== oTable.name;
          } else {
            return table.table !== oTable.name;
          }
        });

        if (sqlAst.orderby) {
          sqlAst.orderby = sqlAst.orderby.filter(g => g.expr.table == oTable.as);
        }
        if (sqlAst.groupby) {
          sqlAst.groupby = sqlAst.groupby.filter(g => g.table == oTable.as);
        }
        if (sqlAst.where) {
          sqlAst.where = null;
        }
      }
      const tb = {
        table: table.name,
        as: table.as || table.name,
        db: null,
      };
      if (table.join) {
        tb.join = table.join;
        tb.on = table.on;
      }
      from.push(tb);
      sqlAst.from = from;
      if (columns && columns.length > 0) {
        columns.forEach(col => {
          if (col.isShow === true) {
            const column = formatAstCol(table, col);
            cols.push(column);
          }
        });
      }
      sqlAst.columns = cols;
      fromObject[table.name] = {
        table: {
          ...table,
          as: table.as || table.name,
        },
        columns: columns,
      };
      return {
        ...state,
        sqlAst,
        fromObject: fromObject,
        t: new Date(),
      };
    },
    changeAstColumns(state, { payload }) {
      const { sqlAst, dataSourceTableList, fromObject } = state;
      const { table, columns } = payload;
      if (!table) {
        return state;
      }
      const obj = fromObject[table.name];
      if (!obj) {
        return state;
      }
      const cols =
        sqlAst.columns && typeof sqlAst.columns == 'object'
          ? sqlAst.columns.filter(col => {
              if (col.expr.type == 'aggr_func') {
                return col.expr.args.expr.table !== table.as;
              } else {
                return col.expr.table !== table.as;
              }
            })
          : [];
      columns.forEach(col => {
        if (col.isShow === true) {
          const column = formatAstCol(table, col);
          cols.push(column);
        }
      });
      sqlAst.columns = cols;
      obj.columns = columns;

      fromObject[table.name] = obj;
      return {
        ...state,
        sqlAst,
        fromObject: fromObject,
        t: new Date(),
      };
    },
    changeAstLimit(state, { payload }) {
      const { sqlAst } = state;
      if (sqlAst.limit) {
        if (payload) {
          sqlAst.limit = setLimit(payload);
        } else {
          delete sqlAst.limit;
        }
      } else {
        sqlAst.limit = setLimit(payload);
      }
      return {
        ...state,
        sqlAst,
        t: new Date(),
      };
    },
    changeAstOrderby(state, { payload }) {
      const { sqlAst, dataSourceTableList } = state;

      let orderby = [];
      if (payload instanceof Array) {
        orderby = sqlAst.orderby
          ? sqlAst.orderby.filter(item => {
              if (payload.findIndex(r => r.item.id === item.id) > -1) return item;
            })
          : [];
        payload &&
          payload.forEach(row => {
            const col = setOrderby(dataSourceTableList, row.item);
            if (!!col) {
              orderby.push(col);
            }
          });
        sqlAst.orderby = orderby;
      } else if (payload) {
        orderby = sqlAst.orderby
          ? sqlAst.orderby.filter(row => row.expr.column != payload.name)
          : [];
        const col = setOrderby(dataSourceTableList, payload);
        if (!!col) {
          orderby.push(col);
        }
      }
      if (orderby && orderby.length > 0) {
        sqlAst.orderby = orderby;
      } else {
        if (sqlAst.orderby) {
          delete sqlAst.orderby;
        }
      }

      return {
        ...state,
        sqlAst,
        t: new Date(),
      };
    },
    changeAstGroupby(state, { payload }) {
      const { sqlAst, fromObject, dataSourceTableList } = state;
      if (sqlAst.groupby && (!payload || payload.length < 1)) {
        delete sqlAst.groupby;
      }
      const cols =
        sqlAst.columns && typeof sqlAst.columns == 'object'
          ? sqlAst.columns.filter(col => {
              return col.expr.type == 'aggr_func';
            })
          : [];

      // const groupby = sqlAst.groupby ? sqlAst.groupby.filter(item => {
      //   if (payload.findIndex(r => r.item ? r.item.name === item.column : r.name === item.column) > -1) return item;
      // }) : [];
      const groupby = [];
      payload &&
        payload.forEach(row => {
          row = row.item || row;
          const col = setGroupby(dataSourceTableList, row);
          if (!!col) {
            const table = row.datatableId
              ? dataSourceTableList.find(t => t.id == row.datatableId)
              : null;
            if (table) {
              const from = fromObject[table.name];
              const columns = [];
              from.columns.forEach(item => {
                if (item.id == row.id) {
                  const column = formatAstCol(table, row);
                  cols.push(column);
                }
                item.isShow =
                  payload.findIndex(r => (r.item ? r.item.id === item.id : r.id === item.id)) > -1;
                columns.push(item);
              });
              from.columns = columns;
            }
            groupby.push(col);
          }
        });
      if (groupby && groupby.length > 0) {
        sqlAst.groupby = groupby;
      }
      sqlAst.columns = cols;

      return {
        ...state,
        sqlAst,
        fromObject,
        t: new Date(),
      };
    },
    addFunColumn(state, { payload }) {
      const { sqlAst, fromObject, dataSourceTableList } = state;
      if (!payload) {
        return state;
      }
      const cols = sqlAst.columns.filter(col => {
        if (col.expr.type == 'aggr_func') {
          if (col.expr.args.expr.type == 'column_ref') {
            return false;
          } else {
            return col.expr.name !== col.fun;
          }
        } else {
          return col.expr.type !== 'column_ref';
        }
      });
      const table = payload.datatableId
        ? dataSourceTableList.find(t => t.id == payload.datatableId)
        : null;
      if (payload.id && table) {
        //有统计到列
        const from = fromObject[table.name];
        if (sqlAst.groupby) {
          from.columns = from.columns.map(item => {
            const j = sqlAst.groupby.findIndex(
              col => col.column == item.name && col.table == item.datatableName
            );
            return {
              ...item,
              isShow: item.fun || j > -1,
            };
          });
        }

        const i = from.columns.findIndex(col => col.id == payload.id);
        payload.isShow = true;
        from.columns.splice(i, 1, payload);
      } else {
        //star   *
        //number  1
      }
      const column = formatAstCol(table, payload);
      cols.push(column);
      sqlAst.columns = cols;
      return {
        ...state,
        sqlAst,
        fromObject,
        t: new Date(),
      };
    },
    removeFunColumn(state, { payload }) {
      const { sqlAst, fromObject, dataSourceTableList } = state;
      if (!payload) {
        return state;
      }

      const cols = sqlAst.columns.filter(col => {
        if (col.expr.type == 'aggr_func') {
          if (col.expr.args.expr.type == 'column_ref') {
            return true;
          } else {
            return col.expr.name !== col.fun && col.expr.args.expr.value !== col.value;
          }
        } else {
          return true;
        }
      });
      const table = payload.datatableId
        ? dataSourceTableList.find(t => t.id == payload.datatableId)
        : null;
      if (table) {
        const from = fromObject[table.name];
        const i = from.columns.findIndex(col => col.id == payload.id);
        from.columns.splice(i, 1, payload);
      }
      sqlAst.columns = cols;
      return {
        ...state,
        sqlAst,
        fromObject,
        t: new Date(),
      };
    },
    removeTable(state, { payload }) {
      const { sqlAst, fromObject } = state;
      if (fromObject) {
        if (payload && payload.name) {
          removeTableAst(payload.name, fromObject, sqlAst);
        } else {
          Object.keys(fromObject).map(key => removeTableAst(key, fromObject, sqlAst));
        }
      }
      return {
        ...state,
        sqlAst,
        fromObject: fromObject,
        t: new Date(),
      };
    },
    changeAstWhere(state, { payload }) {
      const { sqlAst, fromObject } = state;

      if (!payload) {
        //payload 为空删除where 条件
        if (sqlAst.where) {
          delete sqlAst.where;
        }
      } else {
        if (payload.column) {
          //添加
          const where = setWhere(payload, fromObject);
          sqlAst.where = addWhere(sqlAst.where || null, where, payload.operator || 'AND');
        } else {
          //移除
          if (sqlAst.where) {
            sqlAst.where = removeWhere(sqlAst.where || null, payload);
          }
        }
      }

      return {
        ...state,
        sqlAst,
        t: new Date(),
      };
    },
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname, query }) => {
        // dispatch({
        //   type: `fetchDataSources`,
        // });
      });
    },
  },
};
