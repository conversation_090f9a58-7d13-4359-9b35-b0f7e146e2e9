package com.xmcares.platform.hookbox.integrator.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.xmcares.framework.commons.util.CollectionUtils;
import com.xmcares.platform.hookbox.common.job.JobConstants;
import com.xmcares.platform.hookbox.common.job.JobContext;
import com.xmcares.platform.hookbox.common.job.JobContextService;
import com.xmcares.platform.hookbox.common.job.context.JobContextImpl;
import com.xmcares.platform.hookbox.common.job.seatunnel.SeaTunnelJobInfo;
import com.xmcares.platform.hookbox.common.job.seatunnel.SeatunnelEventType;
import com.xmcares.platform.hookbox.integrator.handler.SeaTunnelEvent;
import com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * SeaTunnelJobEventService
 *
 * <AUTHOR>
 * @Descriptions SeaTunnelJobEventService
 * @Date 2025/7/28 10:11
 */
@Service
public class SeaTunnelJobEventService {


    private static final Logger logger = LoggerFactory.getLogger(SeaTunnelJobEventService.class);


    @Resource
    private DatasyncJobInstanceService datasyncJobInstanceService;

    @Autowired
    private SeaTunnelJobPoller seaTunnelJobPoller;

    @Resource
    private JobContextService jobContextService;


    public void handleEvents(List<SeaTunnelEvent> events) {

        List<String> allJobInstanceIdList = events.stream().map(SeaTunnelEvent::getJobId).collect(Collectors.toList());

        List<DatasyncJobInstance> datasyncJobInstances = datasyncJobInstanceService.listByIds(allJobInstanceIdList);

        Map<String, List<DatasyncJobInstance>> jobInstanceMapById = datasyncJobInstances.stream().collect(Collectors.groupingBy(DatasyncJobInstance::getId));

        if (CollectionUtils.isEmpty(jobInstanceMapById)) {
            logger.warn("未找到与事件关联的作业实例: {}", allJobInstanceIdList);
            return;
        }

        events.sort(Comparator.comparing(SeaTunnelEvent::getCreatedTime));


        for (SeaTunnelEvent event : events) {
            logger.info("Processing individual event: {}", event);
            String jobId = event.getJobId();

            if (!jobInstanceMapById.containsKey(jobId) || jobInstanceMapById.get(jobId).isEmpty()) {
                logger.warn("在数据库中未找到 jobId [{}] 对应的实例，跳过事件处理。", jobId);
                continue;
            }
            DatasyncJobInstance byId = jobInstanceMapById.get(jobId).get(0);

            if (StringUtils.equalsIgnoreCase(byId.getStatus().toString(), JobConstants.STATUS_FINISHED)) {
                logger.info("作业实例 [{}] 已成功完成，忽略后续事件: {}", byId, event.getEventType());
                continue; // 直接跳过对此事件的任何处理
            }

            if (StringUtils.equalsIgnoreCase(SeatunnelEventType.LIFECYCLE_ENUMERATOR_OPEN.name(), event.getEventType())) {
                handleJobStart(byId);
            } else if (StringUtils.equalsIgnoreCase(SeatunnelEventType.LIFECYCLE_ENUMERATOR_CLOSE.name(), event.getEventType())) {
                reconcileFinalJobState(byId);
            }
        }
    }

    /**
     * 处理作业启动信号。
     * 如果任务当前状态为终结态（如FAILED），则会“重新打开”任务，使其进入运行中状态。
     */
    private void handleJobStart(DatasyncJobInstance jobInstance) {
        Byte currentStatus = jobInstance.getStatus();
        // 只要任务不处于运行中状态，就响应启动信号
        if (!JobConstants.STATUS_RUNNING.equals(currentStatus)) {
            logger.info("作业实例 [{}] 收到启动信号。当前状态: {}，将更新为 RUNNING。", jobInstance.getId(), currentStatus);

            jobInstance.setStatus(Byte.parseByte(JobConstants.STATUS_RUNNING));
            jobInstance.setStatusMessage("任务运行中...");
            jobInstance.setFinishTime(null); // 清理掉上一次尝试可能留下的结束时间
            datasyncJobInstanceService.updateById(jobInstance);

            // 如果是流任务，启动实时指标轮询
            if ("STREAMING".equalsIgnoreCase(jobInstance.getJobMode()) && seaTunnelJobPoller != null) {
                seaTunnelJobPoller.startPolling(jobInstance);
            }
        } else {
            logger.debug("作业实例 [{}] 已处于运行状态，忽略非首次的 READER_OPEN 事件。", jobInstance.getId());
        }
    }

    /**
     * 立即执行的状态同步方法。
     * 当收到一次尝试结束的信号时调用，通过查询API来获取最权威的状态。
     */
    private void reconcileFinalJobState(DatasyncJobInstance jobInstance) {
        String jobInstanceId = jobInstance.getId();
        logger.info("作业实例 [{}] 收到一次尝试结束的信号，立即同步其最终状态...", jobInstanceId);

        // 如果是流任务，先执行的轮询
        if ("STREAMING".equalsIgnoreCase(jobInstance.getJobMode()) && seaTunnelJobPoller != null) {
            seaTunnelJobPoller.stopPolling(jobInstanceId);
        }

        // 结束后，重新调用一次获取最终状态的方法
        JobContext jobContext = new JobContextImpl(jobInstance.getJobId(), jobInstanceId, jobInstance.getJobName());
        SeaTunnelJobInfo jobInfo = jobContextService.getJobLogInfo(jobContext);

        if (jobInfo != null) {
            logger.info("作业实例 [{}] 的权威状态为: {}", jobInstanceId, jobInfo.getJobStatus());

            switch (jobInfo.getJobStatus()) {
                case "FINISHED":
                    jobInstance.setStatus(Byte.parseByte(JobConstants.STATUS_FINISHED));
                    jobInstance.setStatusMessage("任务执行成功");
                    break;
                case "FAILED":
                    jobInstance.setStatus(Byte.parseByte(JobConstants.STATUS_FAILED));
                    // 截断错误信息以防过长
                    jobInstance.setStatusMessage(StringUtils.abbreviate(jobInfo.getErrorMsg(), 255));
                    break;
                case "CANCELED":
                    jobInstance.setStatus(Byte.parseByte(JobConstants.STATUS_CANCELED));
                    jobInstance.setStatusMessage("任务被手动取消或停止");
                    break;
                default:
                    jobInstance.setStatus(Byte.parseByte(JobConstants.STATUS_FAILED));
                    jobInstance.setStatusMessage("任务结束，但获得未知的最终状态: " + jobInfo.getJobStatus());
                    break;
            }

            // 更新最终的指标和完成时间
            jobInstance.setFinishTime(DateUtil.parse(jobInfo.getFinishTime()));
            if (jobInfo.getMetrics() != null) {
                jobInstance.setWritedCount(jobInfo.getMetrics().getSinkWriteCount());
                jobInstance.setWritedQps(jobInfo.getMetrics().getSinkWriteQPS());
                jobInstance.setReceivedCount(jobInfo.getMetrics().getSourceReceivedCount());
                jobInstance.setReceviedQps(jobInfo.getMetrics().getSourceReceivedQPS());
            }

        } else {
            logger.error("为作业实例 [{}] 同步最终状态时未能从API获取信息！将状态标记为未知。", jobInstanceId);
            jobInstance.setStatus(Byte.parseByte(JobConstants.STATUS_FAILED));
            jobInstance.setStatusMessage("同步最终状态失败：无法从SeaTunnel API获取信息。");
        }
        datasyncJobInstanceService.updateById(jobInstance);
    }


//        if (CollectionUtils.isEmpty(jobInstanceMapById)) {
//            logger.warn("Received an empty job【{}】 instance.", datasyncJobInstances.toString());
//            return;
//        }
//
//        for (SeaTunnelEvent event : events) {
//            logger.info("Processing individual event: {}", event);
//            String jobId = event.getJobId();
//            DatasyncJobInstance byId = jobInstanceMapById.get(jobId).get(0);
//            // 遍历处理所有类型的事件
//            if (StringUtils.equalsIgnoreCase(SeatunnelEventType.LIFECYCLE_READER_OPEN.name(), event.getEventType())) {
//                // TODO: 任务开始，判断如果是流类型，则需要开启一个轮询定时器（定时10s），轮询获取job_instance对应的seatunnel日志，并且更新以下类型数据，并且如果是failed或者finished的，则停止定时器（批类型怎么办？）
//                // byId.setReceivedCount();
//                // byId.setReceviedQps();
//                // byId.setWritedCount();
//                // byId.setWritedQps();
//
//                logger.info("开启获取job_instance_by_id:{}", byId.toString());
//                // 设置状态
//                byId.setStatus(Byte.parseByte(JobConstants.STATUS_RUNNING));
//                // 根据设置的内容，执行更新
//                byId.setUpdateTime(DateUtil.date());
//                datasyncJobInstanceService.updateById(byId);
//                // 2. 如果是流任务，则开启轮询
//                if ("STREAMING".equalsIgnoreCase(byId.getJobMode())) {
//                    seaTunnelJobPoller.startPolling(jobId);
//                }
//
//            } else if (StringUtils.equalsIgnoreCase(SeatunnelEventType.LIFECYCLE_WRITER_CLOSE.name(), event.getEventType())) {
//                logger.info("结束获取job_instance_by_id:{}", byId.toString());
//                // 结束任务
//                // TODO: 如何判断是否为FAILED? Seatunnel没有专门提供出FAILED类型，估计需要通过轮询机制，才能查询出具体状态
//                if (StringUtils.equalsIgnoreCase(byId.getJobMode(), "STREAMING")) {
//                    seaTunnelJobPoller.stopPolling(jobId);
//                    // 流式任务的停止类型为 CANCELED
//                    byId.setStatus(Byte.parseByte(JobConstants.STATUS_CANCELED));
//                    byId.setStatusMessage("手动停止流式任务");
//                } else {
//                    byId.setStatus(Byte.parseByte(JobConstants.STATUS_FINISHED));
//                    byId.setStatusMessage("批任务执行完成");
//                }
//                byId.setFinishTime(DateUtil.date());
//                logger.info("正在为任务 {} 执行最终状态同步...", jobId);
//                seaTunnelJobPoller.fetchAndUpdateJobState(jobId);
//            }
//
//        }


}
