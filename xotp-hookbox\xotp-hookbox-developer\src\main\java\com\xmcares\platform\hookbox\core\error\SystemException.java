package com.xmcares.platform.hookbox.core.error;

import com.xmcares.framework.commons.error.BaseException;
import com.xmcares.framework.commons.error.ErrorCode;

public class SystemException extends BaseException {
    private ErrorCode errorCode;

    public SystemException(ErrorCode code) {
        this.errorCode = code;
    }

    public SystemException(ErrorCode code, String message) {
        super(message);
        this.errorCode = code;
    }

    public SystemException(ErrorCode code, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = code;
    }

    public SystemException(ErrorCode code, Throwable cause) {
        super(cause);
        this.errorCode = code;
    }

    public SystemException(ErrorCode code, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.errorCode = code;
    }

    public ErrorCode getErrorCode() {
        return this.errorCode;
    }
}