package com.xmcares.platform.hookbox.developer.util;

import com.xmcares.framework.commons.util.io.FileUtils;
import com.xmcares.platform.admin.common.util.CommonConstants;
import com.xmcares.platform.hookbox.developer.util.dynamicParam.DynamicParamFactory;
import com.xmcares.platform.hookbox.developer.util.dynamicParam.IDynamicParamExecute;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chenYG
 * @date : 2022/3/31 13:38
 */
public class XxlJobParamUtils {

    public static Map<String, String> findParams(String param, String key) {
        Map<String, Map<String, String>> parserResult = parserParam(param);
        if (parserResult.containsKey(key)) {
            return parserResult.get(key);
        }
        return new HashMap<>();
    }

    public static Map<String, Map<String, String>> parserParam(String param) {
        if (StringUtils.isEmpty(param)) {
            return new HashMap<>();
        }
        String[] splitResults = param.split(CommonConstants.DATAX_DISPATCH_PARAMS_SPLIT);
        Map<String, String> paramLines = new HashMap<>();
        for (String splitResult : splitResults) {
            if (StringUtils.isNotEmpty(splitResult)) {
                int blankIndex = StringUtils.indexOf(splitResult, ' ');
                String key = StringUtils.substring(splitResult, 0, blankIndex);
                String context = StringUtils.substring(splitResult, blankIndex + 1, splitResult.length());
                paramLines.put(key, context);
            }
        }
        return paramLines.entrySet().stream().map(entry ->
                new AbstractMap.SimpleEntry<>(entry.getKey(), handlerParam(entry.getKey(), entry.getValue()))
        ).collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue));
    }

    public static Map<String, String> sysParams(String param) {
        return findParams(param, CommonConstants.DATAX_DISPATCH_PARAMS_SYS_KEY);
    }

    private static final List<String> MULTIPLE_PARAMS = Arrays.asList(
            CommonConstants.DATAX_DISPATCH_PARAMS_PARAM_KEY,
            CommonConstants.DATAX_DISPATCH_PARAMS_INC_KEY, CommonConstants.DATAX_DISPATCH_PARAMS_SYS_KEY,
            CommonConstants.DEVELOP_DISPATCH_PARAMS_PARTIAL_KEY, CommonConstants.DEVELOP_DISPATCH_PARAMS_INC_KEY,
            CommonConstants.DATAX_DISPATCH_PARAMS_SYNC_KEY, CommonConstants.DATAX_DISPATCH_PARAMS_DOWNLOAD_FILE_KEY);

    private static final List<String> SIMPLE_PARAMS = Arrays.asList(CommonConstants.DATAX_DISPATCH_PARAMS_JVM_KEY);

    private static Map<String, String> handlerParam(String key, String value) {
        Map<String, String> result = new HashMap<>();
        if (MULTIPLE_PARAMS.contains(key)) {
            String[] contextLines = StringUtils.split(value, CommonConstants.FIELDS_SPLIT);
            for (String contextLine : contextLines) {
                if (StringUtils.isNotEmpty(contextLine)) {
                    Map.Entry<String, String> handlerKv = handlerKv(contextLine);
                    result.put(handlerKv.getKey(), handlerKv.getValue());
                }
            }
        } else if (SIMPLE_PARAMS.contains(key)) {
            result.put(key, value);
        }
        return result;
    }

    private static Map.Entry<String, String> handlerKv(String contextLine) {
        int kvIndex = StringUtils.indexOf(contextLine, CommonConstants.KV_SPLIT);
        String contextKey = contextLine.substring(0, kvIndex);
        String contextValue = contextLine.substring(kvIndex + 1);
        return new AbstractMap.SimpleEntry<>(contextKey, contextValue);
    }

    public static void builderParam(Map<String, Map<String, String>> executeParams) {

        //1. 处理动态文件
        if (executeParams.containsKey(CommonConstants.DATAX_DISPATCH_PARAMS_DOWNLOAD_FILE_KEY)) {
            Map<String, String> pathParams = executeParams.get(CommonConstants.DATAX_DISPATCH_PARAMS_DOWNLOAD_FILE_KEY);
            for (String key : pathParams.keySet()) {
                String name = FileUtils.getName(pathParams.get(key));
                pathParams.put(key, name);
            }
            executeParams.get(CommonConstants.DATAX_DISPATCH_PARAMS_SYNC_KEY).putAll(pathParams);
        }

        //2. 解析动态参数
        Set<String> syncKeys = executeParams.get(CommonConstants.DATAX_DISPATCH_PARAMS_SYNC_KEY).keySet();
        for (String key : syncKeys) {
            String result = parserSyncParam(executeParams.get(CommonConstants.DATAX_DISPATCH_PARAMS_SYNC_KEY).get(key));
            if (result != null) {
                executeParams.get(CommonConstants.DATAX_DISPATCH_PARAMS_SYNC_KEY).put(key, result);
            }
        }

    }

    private static String parserSyncParam(String expression) {
        if (expression.startsWith("${") && expression.endsWith("}")) {
            String[] expressions = StringUtils.substringBetween(expression, "${", "}").split(",");
            IDynamicParamExecute execute = DynamicParamFactory.create(expressions[0]);
            if (execute == null) {
                throw new IllegalArgumentException("识别到了动态参数【" + expression + "】， 但当前系统不支持！");
            }
            return execute.execute(ArrayUtils.subarray(expressions,1, expressions.length));
        }
        return null;
    }
}
