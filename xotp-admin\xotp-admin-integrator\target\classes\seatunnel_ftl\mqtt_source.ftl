<#-- mqtt_source.ftl -->
{
  "plugin_name": "Mqtt",
  "plugin_output": "${source.plugin_output!'default_source_output'}",
  "broker.urls": [
  <#list orginDatasource.url?split(',') as url>
      "${url?trim}"<#if url_has_next>,</#if>
  </#list>
  ],
  "topics": [
  <#list orgin.topics?split(',') as t>
      "${t?trim}"<#if t_has_next>,</#if>
  </#list>
  ],
  "qos": ${(orgin.qos)!'1'},
  "message.format": "${orgin.messageFormat!'json'}"
  <#-- 只有当 username 存在且不为空时，才生成 "username" 字段 -->
  <#if orginDatasource.username?has_content>
      ,"username": "${orginDatasource.username}"
  </#if>

  <#-- 只有当 password 存在且不为空时，才生成 "password" 字段 -->
  <#if orginDatasource.password?has_content>
      ,"password": "${orginDatasource.password}"
  </#if>

  <#-- 只有当 client_id 存在且不为空时，才生成 "client.id" 字段 -->
  <#if orgin.client_id?has_content>
      ,"client.id": "${orgin.client_id}"
  </#if>
  ,"schema": {
  "fields": {
  <#-- schema 字段从 orginColumns 动态生成 -->
  <#list orginColumns as column>
      "${column.title}": "${column.type}"<#if column_has_next>,</#if>
  </#list>
  }
  }
}
