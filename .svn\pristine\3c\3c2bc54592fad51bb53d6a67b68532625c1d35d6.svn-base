package com.xmcares.platform.admin.integrator.datasync.repository;

import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.common.errors.MetadataException;
import com.xmcares.platform.admin.integrator.datasync.model.Datasync;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncModel;
import com.xmcares.platform.admin.integrator.datasync.vo.QueryDatasync;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.model.DatasourceModel;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceModelRepository;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/2/23 09:59
 */
@Repository
public class DatasyncRepository {


//    @Autowired
//    private DatasyncModelService datasyncModelService;

    @Autowired
    DatasyncModelRepository datasyncModelRepository;

    @Autowired
    DatasourceRepository datasourceRepository;

    @Autowired
    DatasourceModelRepository datasourceModelRepository;

    public static final String TABLE_DATASYNC = Datasync.ENTITY_NAME;

    private static final String FIELDS = "id, create_time, update_time, create_user, intg_name, orgin_type, orgin_intg_model_id" +
            ", orgin_datasource_name, orgin_datasource_id, orgin_plugin_path, orgin_base_json, orgin_adv_json, orgin_hign_json" +
            ", orgin_cloumn_json, orgin_run_schema, dest_type, dest_intg_model_id, dest_datasource_name, dest_datasource_id" +
            ", dest_plugin_path, dest_base_json, dest_adv_json, dest_hgih_json, dest_column_json, dispatch_id, scheduler_expr, file_path, job_mode, field_mapping_json";
    private static final String INSERT_ALL = "INSERT INTO " + TABLE_DATASYNC + "(" + FIELDS + ") VALUES " +
            "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    private static final String UPDATE_ALL = "UPDATE " + TABLE_DATASYNC + " SET " +
            "update_time = ?, create_user = ?, intg_name = ?, orgin_type = ?, orgin_intg_model_id = ?" +
            ", orgin_datasource_name = ?, orgin_datasource_id = ?, orgin_plugin_path = ?, orgin_base_json = ?" +
            ", orgin_adv_json = ?, orgin_hign_json = ?, orgin_cloumn_json = ?, orgin_run_schema = ?, dest_type = ?" +
            ", dest_intg_model_id = ?, dest_datasource_name = ?, dest_datasource_id = ?, dest_plugin_path = ?" +
            ", dest_base_json = ?, dest_adv_json = ?, dest_hgih_json = ?, dest_column_json = ?, dispatch_id = ?" +
            ", scheduler_expr = ?, job_mode = ?, field_mapping_json = ? WHRER id = ?";
    private static final String SELECT_BY_ID = "SELECT " + FIELDS + " FROM " + TABLE_DATASYNC + " WHERE id = ?";
    private static final String COUNT_BY_DATASOURCE_ID = "SELECT COUNT(1) FROM " + TABLE_DATASYNC +
            " WHERE (orgin_type = '0' and orgin_datasource_id = ?) " +
            " or (dest_type = '0' and dest_datasource_id = ?)";


    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    /**
     * 查询数据同步定义分页列表
     *
     * @param queryInfo 查询条件
     * @param page      分页信息
     * @return 分页列表
     */
    public Page<Datasync> queryPage(QueryDatasync queryInfo, Page<Datasync> page) {
        Map<String, Object> conditions = buildCondition(queryInfo);
        Map<String, Object> map = DBUtils.queryList(TABLE_DATASYNC, conditions);
        String sql = DBUtils.getSql(map) + " ORDER BY update_time DESC ";
        Object[] args = DBUtils.getObjects(map);
        List<Datasync> queryResult = xcfJdbcTemplate.queryForEntities(sql, args, page, Datasync.class);
        int totalCount = 0;
        if (CollectionUtils.isNotEmpty(queryResult)) {
            Map<String, Object> countMap = DBUtils.queryCount(TABLE_DATASYNC, conditions);
            totalCount = xcfJdbcTemplate.queryForObject(DBUtils.getSql(countMap), Integer.class, DBUtils.getObjects(countMap));
        } else {
            queryResult = new ArrayList<>();
        }
        Page<Datasync> pageResult = new Page<>();
        pageResult.setData(queryResult);
        pageResult.setTotal(totalCount);
        pageResult.setPageNo(page.getPageNo());
        pageResult.setPageSize(page.getPageSize());
        return pageResult;
    }

    /**
     * 使用分页的方式获取需要删除的列表
     *
     * @param page 分页条件
     * @return 需要删除的列表
     */
    public Page<Datasync> queryNeedDeletePage(Page<Datasync> page) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("hasDelete", "1");
        Map<String, Object> map = DBUtils.queryList(TABLE_DATASYNC, conditions);
        String sql = DBUtils.getSql(map) + " ORDER BY update_time ASC ";
        Object[] args = DBUtils.getObjects(map);
        List<Datasync> queryResult = xcfJdbcTemplate.queryForEntities(sql, args, page, Datasync.class);
        int totalCount = 0;
        if (CollectionUtils.isNotEmpty(queryResult)) {
            Map<String, Object> countMap = DBUtils.queryCount(TABLE_DATASYNC, conditions);
            totalCount = xcfJdbcTemplate.queryForObject(DBUtils.getSql(countMap), Integer.class, DBUtils.getObjects(countMap));
        } else {
            queryResult = new ArrayList<>();
        }
        Page<Datasync> pageResult = new Page<>();
        pageResult.setData(queryResult);
        pageResult.setTotal(totalCount);
        pageResult.setPageNo(page.getPageNo());
        pageResult.setPageSize(page.getPageSize());
        return pageResult;
    }

    /**
     * 添加数据同步定义信息
     *
     * @param datasync 数据同步定义信息
     */
    public void addDatasync(Datasync datasync) {
        datasync.setId(SnowflakeGenerator.getNextId() + "");
        datasync.setCreateTime(new Date());
        datasync.setCreateUser(UserContextHolder.getUserContext().getUsername());
        datasync.setUpdateTime(new Date());
        Map<String, Object> map = DBUtils.insertSqlAndObjects(datasync, Datasync.class, TABLE_DATASYNC);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        xcfJdbcTemplate.update(sql, args);
    }

    public void updateDatasync(Datasync datasync) {
        datasync.setUpdateTime(new Date());
        Map<String, Object> map = DBUtils.updateSqlAndObjects("id", datasync, Datasync.class, TABLE_DATASYNC);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        xcfJdbcTemplate.update(sql, args);
    }


    public Datasync get(String id) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("id", String.valueOf(id));
        conditions.put("hasDelete", "0");
        Map<String, Object> map = DBUtils.queryList(TABLE_DATASYNC, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, Datasync.class);
    }

    public boolean changeHasDelete(String id, String hasDelete) {
        String sql = "UPDATE " + TABLE_DATASYNC + " SET has_delete = ?, update_time = ? WHERE id = ?";
        return xcfJdbcTemplate.update(sql, hasDelete, new Date(), id) > 0;
    }


    public boolean removeDatasync(String id) {
        xcfJdbcTemplate.update("DELETE FROM " + TABLE_DATASYNC + " WHERE id = ? and has_delete = ?", id, "1");
        return true;
    }

    public boolean isUsed(String datasourceId) {
        return xcfJdbcTemplate.queryForObject(COUNT_BY_DATASOURCE_ID, Integer.class, datasourceId, datasourceId) > 0;
    }

    private Map<String, Object> buildCondition(QueryDatasync queryInfo) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("hasDelete", "0");
        if (queryInfo != null) {
            if (StringUtils.isNotEmpty(queryInfo.getIntgName())) {
                conditions.put("intgName", "%" + queryInfo.getIntgName() + "%");
            }
        }
        return conditions;
    }

    public Datasync getByName(String name) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("intgName", name);
        Map<String, Object> map = DBUtils.queryList(TABLE_DATASYNC, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, Datasync.class);
    }

    public boolean existName(String name) {
        String sql = "SELECT count(1) from " + Datasync.ENTITY_NAME + " WHERE intg_name = ? and has_delete = ?";
        Integer count = xcfJdbcTemplate.queryForObject(sql, Integer.class, name, "0");
        return count > 0;
    }

    public DatasyncModel getByDatasourceType(String datasourceId, String type) {
        Datasource datasource = datasourceRepository.get(datasourceId);
        if(datasource==null) {
            throw new MetadataException("数据源不存在");
        }
        DatasourceModel datasourceModel = datasourceModelRepository.getByType(datasource.getType());
        if(datasourceModel==null) {
            throw new MetadataException("数据源模型不存在");
        }
        return datasyncModelRepository.get(datasourceModel.getId(), type);
    }

    /**
     * 根据数据源，获取集成模型信息
     *
     * @param id   数据源id
     * @param type 数据源类型
     * @return 数据源集成模型信息
     */
    public DatasyncModel getDatasyncModel(String id, String type) {
        if (StringUtils.isNotBlank(type)) {
            return this.getByDatasourceType(id, type);
        }
        return datasyncModelRepository.get(id);
    }

    public Integer datasyncModelIdForeignKeyUseCount(String id) {
        return xcfJdbcTemplate.queryForObject("SELECT count(*) FROM " + TABLE_DATASYNC + " WHERE has_delete=0 and ( orgin_intg_model_id = ? OR dest_intg_model_id = ? )", Integer.class, id, id);
    }

}
