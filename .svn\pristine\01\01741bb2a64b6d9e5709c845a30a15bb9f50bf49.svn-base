<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xmcares.platform</groupId>
        <artifactId>xotp</artifactId>
        <version>1.0.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>xotp-seatunnel</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>xotp-seatunnel-plugins</module>
        <module>xotp-seatunnel-connectors</module>
    </modules>

    <properties>
        <seatunnel.version>2.3.11</seatunnel.version>
        <auto-service.version>1.0.1</auto-service.version>
        <system-rules.version>1.2.1</system-rules.version>
        <powermock.version>2.0.9</powermock.version>
        <prometheus.simpleclient.version>0.16.0</prometheus.simpleclient.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.google.auto.service</groupId>
            <artifactId>auto-service</artifactId>
            <version>${auto-service.version}</version>
            <scope>provided</scope>
        </dependency>
        <!-- ***************** slf4j & provider & bridges start ***************** -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-1.2-api</artifactId>
        </dependency>
        <!-- ***************** slf4j & provider & bridges end ***************** -->

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.stefanbirkner</groupId>
            <artifactId>system-lambda</artifactId>
            <version>${system-rules.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- The prometheus simpleclient -->
        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient</artifactId>
            <version>${prometheus.simpleclient.version}</version>
        </dependency>
        <!-- Hotspot JVM metrics-->
        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient_hotspot</artifactId>
            <version>${prometheus.simpleclient.version}</version>
        </dependency>
        <!-- Exposition HTTPServer-->
        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient_httpserver</artifactId>
            <version>${prometheus.simpleclient.version}</version>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.apache.seatunnel</groupId>
                <artifactId>seatunnel-connectors-v2-fake</artifactId>
                <version>${seatunnel.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.seatunnel</groupId>
                <artifactId>seatunnel-api</artifactId>
                <version>${seatunnel.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.seatunnel</groupId>
                <artifactId>connector-common</artifactId>
                <version>${seatunnel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seatunnel</groupId>
                <artifactId>seatunnel-format-json</artifactId>
                <version>${seatunnel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seatunnel</groupId>
                <artifactId>seatunnel-format-text</artifactId>
                <version>${seatunnel.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


</project>