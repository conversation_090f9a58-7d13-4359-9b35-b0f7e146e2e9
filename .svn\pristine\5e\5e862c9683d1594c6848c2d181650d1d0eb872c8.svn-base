package com.xmcares.platform.admin.integrator.common.util;

/**
 * <AUTHOR> chenYG
 * @date : 2022/3/29 9:53
 */
public class ConstantUtils {
    /** 调度服务请求响应成功编码 */
    public static final int SCHEDULER_REQUEST_SUCCESS_CODE = 200;
    /** 调度服务默认的调度类型 */
    public static final String SCHEDULER_DEF_SCHEDULE_TYPE = "CRON";
    /** 调度服务默认的过期策略 */
    public static final String SCHEDULER_DEF_MISFIRE = "DO_NOTHING";
    /** 勾盒服务应用名称 */
    public static final String HOOKBOX_APPNAME = "xxl-job-sample-executor";
    /** 勾盒服务的DataxJob任务启动处理器名称 */
    public static final String HOOKBOX_INTEGRATOR_BEGIN_HANDLER = "DataxJobHandler";
    /** 增量Key 当该Key存在时， 即表示使用增量 */
    public static final String INCREMENT_MODE_KEY = "incMode";
    /** 读锝模式 分别为 all：全量 inc：增量 */
    public static final String READER_MODE = "readMode";
    /** Datax Jvm配置参数Key */
    public static final String DATAX_FIELDS_JVM = "jvm";
    /** datax 主要模板文件名称 */
    public static final String DATAX_TEMP_MAIN_NAME = "main.ftl";

    public static final String SEATUNNEL_TEMP_MAIN_NAME = "main.ftl";

    /** datax 模板文件在文件服务器上的目录名称 */
    public static final String FILE_DATAX_JOB_DIR = "/datax/job";

    public static final String FILE_SEATUNNEL_JOB_DIR = "/seatunnel/job";

    /** datax 主要模板文件后缀 */
    public static final String FILE_DATAX_TEMP_SUFFIX = ".json";

    public static final String FILE_SEATUNNEL_CONF_SUFFIX = ".json";

    /** datax 主要模板文件后缀 */
    public static final String FILE_DATAX_PLUGIN_DIR = "/datax/plugin";

}
