<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding" native2AsciiForPropertiesFiles="true" defaultCharsetForPropertiesFiles="UTF-8">
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-dataservice/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-dataservice/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-developer/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-developer/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-governance/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-governance/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-metadata/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-metadata/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-scheduler/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-admin/xotp-admin-scheduler/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-cdc-oraclereader/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-cdc-oraclereader/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-kafkareader/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-kafkareader/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-kafkawriter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-kafkawriter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-rabbitmqreader/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-rabbitmqreader/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-rabbitmqwriter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-rabbitmqwriter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-rocketmqreader/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-rocketmqreader/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-rocketmqwriter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-rocketmqwriter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-ximcreader/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-datax/xotp-datax-ximcreader/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-flinkx/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-flinkx/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-flinkx/xotp-flinkx-demo/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-flinkx/xotp-flinkx-demo/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-flinkx/xotp-flinkx-quality/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-flinkx/xotp-flinkx-quality/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-flinkx/xotp-flinkx-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-flinkx/xotp-flinkx-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-hookbox/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-hookbox/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-developer/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-developer/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-governance/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-governance/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-openapi/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-openapi/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-openapi/xotp-openapi-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-openapi/xotp-openapi-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-openapi/xotp-openapi-dataservice/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-openapi/xotp-openapi-dataservice/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-openapi/xotp-openapi-dataview/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-openapi/xotp-openapi-dataview/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-admin-dataservice/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-admin-dataservice/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-admin-integrator/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-admin-integrator/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-admin-scheduler/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-admin-scheduler/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-admin/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-admin/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-hookbox-integrator/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-hookbox-integrator/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-hookbox/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-hookbox/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-openapi/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-openapi/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-seatunnel-integrator/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-release/xotp-release-seatunnel-integrator/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/connector-artemis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/connector-artemis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/connector-fako/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/connector-fako/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/connector-mqtt/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/connector-mqtt/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/connector-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/connector-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-examples/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-examples/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-plugins/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-plugins/src/main/resources" charset="UTF-8" />
    <file url="PROJECT" charset="UTF-8" />
  </component>
</project>