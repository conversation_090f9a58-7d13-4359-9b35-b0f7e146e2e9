package com.xmcares.platform.admin.integrator.datasync.repository;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.platform.admin.common.datasource.jdbc.JdbcType;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.integrator.IntegratorProperties;
// CHANGED: 建议将 ConstantUtils 中的常量名也进行更新
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;

import com.xmcares.platform.admin.integrator.common.util.ConstantUtils;
import com.xmcares.platform.admin.integrator.datasync.dto.DatasyncDto;
import com.xmcares.platform.admin.integrator.datasync.error.ScheduleFailureException;
import com.xmcares.platform.admin.integrator.datasync.model.Datasync;
import com.xmcares.platform.admin.integrator.datasync.vo.DataxTempVo; // CHANGED: 建议重命名为 JobConfigVo
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.service.DatasourceService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chenYG
 * @date : 2022/3/29 16:58
 * REMARK : Refactored for SeaTunnel configuration generation.
 */
// CHANGED: Class name updated from DataxFileRepository to SeaTunnelConfigRepository
@Component
public class SeaTunnelConfigRepository {

    private static final Logger LOG = LoggerFactory.getLogger(SeaTunnelConfigRepository.class);

    private final String rootPath;
    private final FSTemplate fsTemplate;
    private final DatasourceService datasourceService;
    private final Configuration tempConfig;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    public SeaTunnelConfigRepository(FSTemplate fsTemplate, @Qualifier(value = "tmplConfiguration") Configuration tempConfig, IntegratorProperties properties, DatasourceService datasourceService) {
        this.fsTemplate = fsTemplate;
        this.tempConfig = tempConfig;
        // CHANGED: The directory name in the path is updated for clarity
        this.rootPath = properties.getFileServerRoot() + ConstantUtils.FILE_SEATUNNEL_JOB_DIR; // 建议在 ConstantUtils 中新增此常量
        this.datasourceService = datasourceService;
    }

    /**
     * 构建 SeaTunnel 作业配置文件的存储路径
     * @param id 任务ID
     * @param code 任务编码
     * @return 文件路径
     */
    public String buildJobConfigPath(String id, String code) {
        // CHANGED: File suffix updated to .conf for SeaTunnel
        return this.rootPath + "/" + id + "_" + code + ConstantUtils.FILE_SEATUNNEL_CONF_SUFFIX; // 建议在 ConstantUtils 中新增此常量, e.g., ".conf"
    }

    /**
     * 根据 DTO 生成并上传 SeaTunnel 配置文件
     * @param datasyncDto 数据同步任务的 DTO
     * @param code 任务编码
     * @return 上传后的文件路径
     */
    public String upload(DatasyncDto datasyncDto, String code) {
        String jobConfigPath = buildJobConfigPath(datasyncDto.getId(), code);
        // NEW: Build the data model required by the new SeaTunnel templates
        Map<String, Object> dataModel = buildSeaTunnelDataModel(datasyncDto);
        // Generate and upload the file using the new data model
        addFile(datasyncDto.getIntgName(), dataModel, jobConfigPath);
        return jobConfigPath;
    }

    /**
     * 根据 DTO 生成 SeaTunnel 配置文件的预览内容
     * @param datasyncDto 数据同步任务的 DTO
     * @return 包含配置内容和数据模型的VO对象
     */
    public DataxTempVo reader(DatasyncDto datasyncDto) { // CHANGED: 建议将返回值 DataxTempVo 重命名为 JobConfigVo
        // NEW: Build the data model required by the new SeaTunnel templates
        Map<String, Object> dataModel = buildSeaTunnelDataModel(datasyncDto);
        String configContent = processTemplate(datasyncDto.getIntgName(), dataModel);
        return new DataxTempVo(configContent, dataModel);
    }

    // NEW: A new private method to build the data model for SeaTunnel templates.
    // This replaces the old DataxFtlUtils.buildDataxFtlDatasource call.
    private Map<String, Object> buildSeaTunnelDataModel(DatasyncDto dto) {
        Map<String, Object> dataModel = new HashMap<>();

        // 1. 基本环境配置 (env block)
        dataModel.put("jobMode", dto.getJobMode());
        dataModel.put("jobName", dto.getIntgName());


        // 3. 数据源信息
        dataModel.put("orginDatasource", checkAndGetDataSource("数据来源", dto.getOrginDatasourceId()));
        dataModel.put("destDatasource", checkAndGetDataSource("数据去向", dto.getDestDatasourceId()));

        // 4. Source/Sink 的具体参数
        Map<String, Object> orginParams = dto.buildOrginMapInfo();
        Map<String, Object> destParams = dto.buildDestMapInfo();
        dataModel.put("orgin", orginParams);
        dataModel.put("dest", destParams);

        // 5. [已修正] 字段信息
        //    使用提供的 "name" -> "title" 回退逻辑来解析字段名
        if (dto.getOrginColumnJsonEntities() != null) {
            // 解析完整的字段信息 (name, type) 供 RabbitMQ 等模板使用
            dataModel.put("orginColumns", dto.getOrginColumnJsonEntities().stream()
                    .map(item -> (Map<String, Object>) item)
                    .collect(Collectors.toList()));

            // 【已修正】使用您的逻辑解析 orginFieldNames
            dataModel.put("orginFieldNames", dto.getOrginColumnJsonEntities().stream()
                    .map(item -> {
                        JSONObject json = (JSONObject) item;
                        String name = json.getString("name");
                        return StringUtils.isNotBlank(name) ? name : json.getString("title");
                    })
                    .collect(Collectors.toList()));
        }
        if (dto.getDestColumnJsonEntities() != null) {
            // 解析完整的字段信息 (name, type)
            dataModel.put("destColumns", dto.getDestColumnJsonEntities().stream()
                    .map(item -> (Map<String, Object>) item)
                    .collect(Collectors.toList()));

            // 【已修正】使用您的逻辑解析 destFieldNames
            dataModel.put("destFieldNames", dto.getDestColumnJsonEntities().stream()
                    .map(item -> {
                        JSONObject json = (JSONObject) item;
                        String name = json.getString("name");
                        return StringUtils.isNotBlank(name) ? name : json.getString("title");
                    })
                    .collect(Collectors.toList()));
        }

        // 6. 字段映射
        if (StringUtils.isNotEmpty(dto.getFieldMappingJson())) {
            dataModel.put("fieldMappings", JSON.parseArray(dto.getFieldMappingJson(), Map.class));
        }

        // 7. 可选的输入输出名
        Map<String, Object> sourceOptionalParams = new HashMap<>();
        sourceOptionalParams.put("plugin_output", orginParams.get("plugin_output"));
        dataModel.put("source", sourceOptionalParams);

        Map<String, Object> transformOptionalParams = new HashMap<>();
        transformOptionalParams.put("plugin_input", orginParams.get("plugin_input"));
        transformOptionalParams.put("plugin_output", orginParams.get("plugin_output"));
        dataModel.put("transform", transformOptionalParams);

        Map<String, Object> sinkOptionalParams = new HashMap<>();
        sinkOptionalParams.put("plugin_input", destParams.get("plugin_input"));
        dataModel.put("sink", sinkOptionalParams);

         return dataModel;
    }

    /**
     * 根据数据模型处理 Freemarker 模板，返回生成的配置字符串
     * @param jobName 任务名 (用于日志)
     * @param dataModel 为模板准备的数据模型
     * @return 生成的配置字符串
     */
    private String processTemplate(String jobName, Map<String, Object> dataModel) {
        Template template;
        try {
            // CHANGED: Ensure this constant points to your new SeaTunnel "main.ftl"
            template = tempConfig.getTemplate(ConstantUtils.SEATUNNEL_TEMP_MAIN_NAME);
        } catch (IOException e) {
            throw new ScheduleFailureException("获取SeaTunnel同步配置文件模板失败", e);
        }

        // REMOVED: The hardcoded logic for MqType.XIMC is no longer needed.
        // All datasource details should now come from the `checkAndGetDataSource` method.

        try (ByteArrayOutputStream output = new ByteArrayOutputStream();
             Writer writer = new OutputStreamWriter(output, StandardCharsets.UTF_8)
        ) {
            template.process(dataModel, writer);
            return new String(output.toByteArray(), StandardCharsets.UTF_8);
        } catch (TemplateException e) {
            throw new ScheduleFailureException(
                    String.format("处理SeaTunnel数据同步任务[%s]模板异常", jobName), e);
        } catch (Exception e) {
            throw new ScheduleFailureException(
                    String.format("生成SeaTunnel数据同步任务[%s]文件内容异常", jobName), e);
        }
    }

    /**
     * 校验并获取数据源信息，并转换为 Map。
     * [已修正] 此版本会解析内嵌的 'options' JSON 字符串。
     * @param sourceType 来源类型描述 (用于日志)
     * @param dsId 数据源ID
     * @return 包含数据源连接细节的扁平化 Map
     */
    private Map<String, Object> checkAndGetDataSource(String sourceType, String dsId) {
        Assert.notNull(dsId, "请设置" + sourceType + "的ID");
        Datasource datasource = datasourceService.getDatasource(dsId);
        Assert.notNull(datasource, "未找到" + sourceType + "的数据源信息，ID: " + dsId);

        // 1. 先将 Datasource 对象转换为基础 Map
        Map<String, Object> datasourceMap = BeanUtil.beanToMap(datasource);
        datasourceMap.put("pluginName", StringUtils.isNotBlank(datasource.getCategory()) ? datasource.getCategory().toLowerCase() : "jdbc");
        // 2. 检查 'options' 字段是否存在且为非空字符串
        Object optionsObject = datasourceMap.get("options");
        if (optionsObject instanceof String && StringUtils.isNotEmpty((String) optionsObject)) {
            String optionsJsonString = (String) optionsObject;
            try {
                // 3. 将 'options' 的JSON字符串解析为一个新的 Map
                Map<String, Object> optionsMap = JSON.parseObject(optionsJsonString, Map.class);

                // 4. 将解析出的 optionsMap 合并到主 datasourceMap 中
                // 这会将 url, username, password 等提升为顶层键
                if (optionsMap != null) {
                    datasourceMap.putAll(optionsMap);
                }
            } catch (JSONException e) {
                LOG.error("解析数据源'options'字段的JSON时出错, dsId: {}", dsId, e);
                throw new BusinessException("数据源'options'字段的JSON格式错误");
            }
        }

        // 5. (可选但推荐) 移除原始的 options 字符串，保持 Map 清洁
        datasourceMap.remove("options");

        // 6. 根据 type 字段自动推断并添加 driver
        Object typeObject = datasourceMap.get("type");
        if (typeObject instanceof String) {
            String type = (String) typeObject;
            // 使用 JdbcType 枚举来查找 driver
            JdbcType jdbcType = JdbcType.fromTypeName(type);
            if (jdbcType != null) {
                String driver = jdbcType.getDriverClassName();
                datasourceMap.put("driver", driver);
                LOG.info("根据类型 '{}'，从 JdbcType 枚举成功设置 driver 为: {}", type, driver);
            } else {
                // 如果枚举中未找到，对于JDBC类型打印警告
                if ("jdbc".equalsIgnoreCase((String) datasourceMap.get("category"))) {
                    LOG.warn("未知的JDBC数据源类型 '{}'，无法从JdbcType枚举中找到对应的driver。", type);
                }
            }
        }
        return datasourceMap;
    }
    /**
     * 上传配置文件
     * @param jobName 任务名
     * @param dataModel 数据模型
     * @param filePath 文件路径
     */
    private void addFile(String jobName, Map<String, Object> dataModel, String filePath) {
        String configContent = processTemplate(jobName, dataModel);
        try (ByteArrayInputStream input = new ByteArrayInputStream(configContent.getBytes(StandardCharsets.UTF_8))) {
            fsTemplate.saveFile(new FileDesc.FileDescImpl(null, filePath), input);
        } catch (Exception e) {
            throw new ScheduleFailureException(
                    String.format("保存SeaTunnel数据同步任务[%s]文件异常", jobName), e);
        }
    }

    // --- 其他方法 (upload, removeFile, readerContext) 可以保持不变, 只需注意日志和异常信息中的 "Datax" 字样可酌情修改 ---

    public void upload(String filePath, String context) {
        try (ByteArrayInputStream input = new ByteArrayInputStream(context.getBytes(StandardCharsets.UTF_8))) {
            fsTemplate.saveFile(new FileDesc.FileDescImpl(null, filePath), input);
        } catch (Exception e) {
            throw new ScheduleFailureException(
                    String.format("保存SeaTunnel数据同步任务[%s]文件异常", filePath), e);
        }
    }

    public void removeFile(String filePath) {
        try {
            fsTemplate.deleteFile(filePath);
        } catch (Exception e) {
            LOG.error("删除文件【" + filePath + "】失败，失败原因：", e);
        }
    }

    public String readerContext(String filePath) {
        try (ByteArrayOutputStream output = new ByteArrayOutputStream()){
            filePath = StringUtils.replace(filePath, "\\", "/");
            fsTemplate.loadFile(filePath, output);
            return new String(output.toByteArray(), StandardCharsets.UTF_8);
        } catch (Exception ioException) {
            throw new BusinessException("读取文件失败, 可能文件不存在导致");
        }
    }

    /**
     * 测试用例 1: MySQL -> Transform -> MySQL
     */
    private void testMysqlToMysqlWithTransform() {
        // 准备DTO
        Datasync datasync = new Datasync();
        datasync.setIntgName("MySQL到MySQL带转换");
        datasync.setJobMode("BATCH");
        datasync.setOrginType("jdbc");
        datasync.setDestType("jdbc");
        datasync.setOrginDatasourceId("42594071741743104");
        datasync.setDestDatasourceId("42626867071827968");

        // 设置字段映射
        datasync.setFieldMappingJson("[{\"sourceField\":\"user_id\",\"targetField\":\"id\",\"targetType\":\"BIGINT\"},{\"sourceField\":\"user_name\",\"targetField\":\"name\",\"targetType\":\"STRING\"}]");

        DatasyncDto dto = new DatasyncDto(datasync);

        // 设置Source和Sink的详细参数 (Base/Adv/High JSON)
        dto.setOrginBaseJson("{\"table\":\"source_users\"}");
        dto.setOrginColumnJson("[{\"name\":\"user_id\"},{\"name\":\"user_name\"}]");

        dto.setDestBaseJson("{\"table\":\"target_users\",\"database\":\"sink_db\"}");
        dto.setDestHighJson("{\"generate_sink_sql\":true}"); // 使用自动生成SQL模式

        // 执行并打印结果
        DataxTempVo result = this.reader(dto);
        System.out.println(result.getContext());
    }

    /**
     * 测试用例 1.1: MySQL -> Transform -> MySQL (自动生成Query)
     * 这个方法专门测试当 DTO 未提供 querySql 时，模板自动拼接SQL语句的场景。
     */
    private void testMysqlToMysql_AutoGeneratedQuery() {
        // 准备DTO
        Datasync datasync = new Datasync();
        datasync.setIntgName("MySQL到MySQL(自动生成Query)");
        datasync.setJobMode("BATCH");
        datasync.setOrginType("jdbc");
        datasync.setDestType("jdbc");
        datasync.setOrginDatasourceId("42594071741743104");
        datasync.setDestDatasourceId("42626867071827968");

        // 设置字段映射
        datasync.setFieldMappingJson("[{\"sourceField\":\"user_id\",\"targetField\":\"id\",\"targetType\":\"BIGINT\"},{\"sourceField\":\"user_name\",\"targetField\":\"name\",\"targetType\":\"STRING\"}]");

        DatasyncDto dto = new DatasyncDto(datasync);

        // --- 为 jdbc_source.ftl 提供生成 SELECT 语句所需的内容 ---
        // 1. 提供 table 和可选的 where 条件
        dto.setOrginBaseJson("{\"table\":\"source_users\"}");
        dto.setOrginAdvJson("{\"where\":\"status = 'active'\"}"); // where 是可选的

        // 2. 提供需要查询的字段列表 (用于 <#list orginFieldNames>)
        dto.setOrginColumnJson("[{\"name\":\"user_id\"},{\"name\":\"user_name\"}]");
        // -------------------------------------------------------------

        // Sink 的配置保持不变
        dto.setDestBaseJson("{\"table\":\"target_users\",\"database\":\"sink_db\"}");
        dto.setDestHighJson("{\"generate_sink_sql\":true}");

        // 执行并打印结果
        DataxTempVo result = reader(dto);
        System.out.println(result.getContext());
    }

    private void testMysqlToMysql_CustomQuery() {
        // 准备DTO
        Datasync datasync = new Datasync();
        datasync.setIntgName("MySQL到MySQL(自定义Query)");
        datasync.setJobMode("BATCH");
        datasync.setOrginType("jdbc");
        datasync.setDestType("jdbc");
        datasync.setOrginDatasourceId("42594071741743104");
        datasync.setDestDatasourceId("42626867071827968");

        // 字段映射依然需要，因为 Transform 层需要知道如何处理从 Source 来的字段
        datasync.setFieldMappingJson("[{\"sourceField\":\"id\",\"targetField\":\"id\",\"targetType\":\"BIGINT\"},{\"sourceField\":\"username\",\"targetField\":\"name\",\"targetType\":\"STRING\"}]");

        DatasyncDto dto = new DatasyncDto(datasync);

        // --- 为 jdbc_source.ftl 提供自定义的 querySql ---
        // 直接在 BaseJson (或Adv/High) 中提供完整的 querySql
        dto.setOrginBaseJson("{\"querySql\":\"SELECT id, username FROM users_archive WHERE year = 2024\"}");
        // 此时，orginColumnJson 和 where 将被忽略
        // -------------------------------------------------------------

        // Sink 的配置保持不变
        dto.setDestBaseJson("{\"table\":\"target_users\",\"database\":\"sink_db\"}");
        dto.setDestHighJson("{\"generate_sink_sql\":true}");

        // 执行并打印结果
        DataxTempVo result = reader(dto);
        System.out.println(result.getContext());
    }

    private void testJdbcSink_ManualQuery_WithTransform() {
        // 准备DTO
        Datasync datasync = new Datasync();
        datasync.setIntgName("JDBC Sink手动Query测试");
        datasync.setJobMode("BATCH");
        datasync.setOrginType("jdbc");
        datasync.setDestType("jdbc");
        datasync.setOrginDatasourceId("42594071741743104");
        datasync.setDestDatasourceId("42626867071827968");

        // 1. 提供字段映射。Sink模板将使用这里的 "targetField" 来构建SQL。
        datasync.setFieldMappingJson("[{\"sourceField\":\"src_uuid\",\"targetField\":\"id\",\"targetType\":\"STRING\"},{\"sourceField\":\"src_name\",\"targetField\":\"user_name\",\"targetType\":\"STRING\"},{\"sourceField\":\"src_points\",\"targetField\":\"points_balance\",\"targetType\":\"INT\"}]");

        DatasyncDto dto = new DatasyncDto(datasync);

        // 2. 设置一个有效的Source端配置
        dto.setOrginBaseJson("{\"table\":\"source_data\"}");
        dto.setOrginColumnJson("[{\"name\":\"src_uuid\"},{\"name\":\"src_name\"},{\"name\":\"src_points\"}]");

        // 3. 设置Sink端配置
        //    - 在 destBaseJson 中提供 table 名，用于拼接 "INSERT INTO [table] ..."
        //    - 关键：不要在 destHighJson 中设置 "generate_sink_sql": true，以触发手动模式。
        dto.setDestBaseJson("{\"table\":\"manual_query_target_table\"}");
        // destHighJson 可以为空或不包含 generate_sink_sql
        dto.setDestHighJson("{}");

        // 执行并打印结果
        DataxTempVo result = reader(dto);
        System.out.println(result.getContext());
    }


}
