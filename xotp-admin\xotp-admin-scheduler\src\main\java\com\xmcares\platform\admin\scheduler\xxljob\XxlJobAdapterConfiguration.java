/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/8
 */
package com.xmcares.platform.admin.scheduler.xxljob;

import com.xmcares.framework.commons.context.UserDetails;
import com.xmcares.framework.commons.context.UserDetailsHolder;
import com.xmcares.platform.admin.scheduler.xxljob.error.XxlJobWebExceptionResolver;
import com.xxl.job.admin.XxlJobAdminApplication;
import com.xxl.job.admin.controller.interceptor.CookieInterceptor;
import com.xxl.job.admin.controller.interceptor.PermissionInterceptor;
import com.xxl.job.admin.controller.interceptor.WebMvcConfig;
import com.xxl.job.admin.controller.resolver.WebExceptionResolver;
import com.xxl.job.admin.core.model.XxlJobUser;
import com.xxl.job.admin.core.util.CookieUtil;
import com.xxl.job.admin.core.util.FtlUtil;
import com.xxl.job.admin.core.util.I18nUtil;
import com.xxl.job.admin.core.util.JacksonUtil;
import com.xxl.job.admin.dao.XxlJobUserDao;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcRegistrations;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.math.BigInteger;
import java.util.HashMap;

import static com.xxl.job.admin.service.impl.LoginService.LOGIN_IDENTITY_KEY;

/**
 * 去掉XxlJob的权限校验机制，让 xxl-job能在xcnf的权限验证机制下使用
 * <AUTHOR>
 * @since 2.1.0
 */
@ComponentScan(basePackages ={"com.xxl.job.admin"},
        //  exclude xxl-job Spring Boot's default WebMvcConfig
        excludeFilters = {
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
                        value = {CookieInterceptor.class, PermissionInterceptor.class,
                                WebMvcConfig.class, XxlJobAdminApplication.class, WebExceptionResolver.class
                })
        }
)
@MapperScan("com.xxl.job.admin.dao")
@Configuration
public class XxlJobAdapterConfiguration implements WebMvcConfigurer {


    @Resource
    private XxlJobUserDao xxlJobUserDao;

    /**
     * 替代xxl-job的WebExceptionResolver，避免与框架项目捕获异常机制冲突
     * @return
     */
    @Bean
    public HandlerExceptionResolver xxlJobWebExceptionResolver() {
        return new XxlJobWebExceptionResolver();
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * 无法完全去除xxl-job的权限校验机制，只能mock xxl-job 的 admin，让 xxl-job的权限校验机制正常运行
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
                // 非HandlerMethod直接通过
                if (!(handler instanceof HandlerMethod)) {
                    return true; // proceed with the next interceptor
                }

                HandlerMethod method = (HandlerMethod) handler;
                String packageName = method.getBeanType().getPackage().getName();

                // 对xxljob添加权限拦截解除
                if (!packageName.startsWith("com.xxl.job.admin")) {
                    // 不是xxljob的请求放行
                    return true;
                }

                String uri = request.getRequestURI();
                XxlJobUser loginUser;

                // api或者api走的是xxl-job accessToken机制
                if (isApiRequest(uri)) {
                    loginUser = createAdminUser();
                } else {
                    loginUser = handleWebRequest();
                    if (loginUser == null) {
                        response.setStatus(401);
                        return false;
                    }
                }

                // 设置登录信息
                setLoginInfo(request, response, loginUser);
                return true;
            }

            @Override
            public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
                if (!(handler instanceof HandlerMethod)) {
                    return;	// proceed with the next interceptor
                }
                HandlerMethod handlerMethod = (HandlerMethod) handler;
                Class<?> beanType = handlerMethod.getBeanType();
                String packageName = beanType.getPackage().getName();
                //对xxljob 添加权限拦截解除
                if (packageName.startsWith("com.xxl.job.admin")) {
                    // cookie
                    if (modelAndView!=null && request.getCookies()!=null && request.getCookies().length>0) {
                        HashMap<String, Cookie> cookieMap = new HashMap<String, Cookie>();
                        for (Cookie ck : request.getCookies()) {
                            cookieMap.put(ck.getName(), ck);
                        }
                        modelAndView.addObject("cookieMap", cookieMap);
                    }

                    // static method
                    if (modelAndView != null) {
                        modelAndView.addObject("I18nUtil", FtlUtil.generateStaticModel(I18nUtil.class.getName()));
                    }
                }
            }

            @Override
            public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
                if (!(handler instanceof HandlerMethod)) {
                    return;	// proceed with the next interceptor
                }
                HandlerMethod handlerMethod = (HandlerMethod) handler;
                Class<?> beanType = handlerMethod.getBeanType();
                String packageName = beanType.getPackage().getName();
                //对xxljob 添加权限拦截解除
                if (packageName.startsWith("com.xxl.job.admin")) {
                    request.removeAttribute(LOGIN_IDENTITY_KEY);
                }
            }

            /**
             * 判断是否为API请求
             */
            private boolean isApiRequest(String uri) {
                return uri.startsWith("/extend-api") ||
                        uri.startsWith("/api") ||
                        uri.startsWith("/jobinfo") ||
                        uri.startsWith("/jobgroup") ||
                        uri.startsWith("/joblog");
            }
            /**
             * 创建管理员用户
             */
            private XxlJobUser createAdminUser() {
                XxlJobUser loginUser = new XxlJobUser();
                loginUser.setUsername("admin");
                loginUser.setRole(1);
                //loginUser.setPassword(userDetails.getPassword());
                //loginUser.setPassword("21232f297a57a5a743894a0e4a801fc3");
                return loginUser;
            }
            /**
             * 处理Web请求的用户认证
             */
            private XxlJobUser handleWebRequest() {
                UserDetails userDetails = UserDetailsHolder.getUserDetails();
                if (userDetails == null) {
                    return null;
                }

                XxlJobUser loginUser = new XxlJobUser();
                loginUser.setUsername(userDetails.getUsername());
                //loginUser.setPassword(userDetails.getPassword());
                //loginUser.setPassword("21232f297a57a5a743894a0e4a801fc3");

                // 设置用户角色和权限
                setUserRoleAndPermission(loginUser, userDetails.getUsername());
                return loginUser;
            }
            /**
             * 设置用户角色和权限
             */
            private void setUserRoleAndPermission(XxlJobUser loginUser, String username) {
                if ("admin".equalsIgnoreCase(username)) {
                    loginUser.setRole(1);
                } else {
                    loginUser.setRole(0);
                    // 普通用户，查下是否xxl-job有该相应用户及权限
                    XxlJobUser xxlJobUser = xxlJobUserDao.loadByUserName(username);
                    if (xxlJobUser != null) {
                        loginUser.setRole(xxlJobUser.getRole());
                        loginUser.setPermission(xxlJobUser.getPermission());
                    }
                }
            }
            /**
             * 设置登录信息到请求和Cookie中
             */
            private void setLoginInfo(HttpServletRequest request, HttpServletResponse response, XxlJobUser loginUser) {
                request.setAttribute(LOGIN_IDENTITY_KEY, loginUser);
                String loginToken = makeToken(loginUser);
                // do login
                CookieUtil.set(response, LOGIN_IDENTITY_KEY, loginToken, false);
            }

            /**
             * 创建 xxljob token json
             * @param xxlJobUser
             * @return
             */
            private String makeToken(XxlJobUser xxlJobUser){
                String tokenJson = JacksonUtil.writeValueAsString(xxlJobUser);
                return new BigInteger(tokenJson.getBytes()).toString(16);
            }

        }).addPathPatterns("/**");


    }



    /**
     * 注册RequestMappingHandlerMapping,
     * xcnf默认的“/login” 与 xxl-job的“/login”冲突，去掉xxl-job的“/login”
     * @see com.xmcares.framework.rbac.reference.web.LoginWebReference#login
     * @see com.xxl.job.admin.controller.IndexController#loginDo
     */
    @Bean
    public WebMvcRegistrations webMvcRegistrations() {
        return new WebMvcRegistrations() {
            @Override
            public RequestMappingHandlerMapping getRequestMappingHandlerMapping() {
                return requestMappingHandlerMapping();
            }
        };
    }

    private RequestMappingHandlerMapping requestMappingHandlerMapping() {
        return new RequestMappingHandlerMapping() {
            @Override
            protected void registerHandlerMethod(Object handler, Method method, RequestMappingInfo mapping) {
                // 去掉xxl-job的“/login”，与xcnf默认的“/login” 冲突了
                if (method.getName().equals("loginDo") && method.getDeclaringClass().getName().equals("com.xxl.job.admin.controller.IndexController")) {
                    logger.info("Excluding xxl-job login path: " + method);
                    // 不注册此请求路径
                    return;
                }
                super.registerHandlerMethod(handler, method, mapping);
            }
        };
    }

}
