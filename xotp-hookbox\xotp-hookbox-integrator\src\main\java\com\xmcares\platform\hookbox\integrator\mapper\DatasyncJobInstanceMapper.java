package com.xmcares.platform.hookbox.integrator.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DatasyncJobInstanceMapper extends BaseMapper<DatasyncJobInstance> {

    /**
     * 查询所有 job instances 并内联查询其关联的 job.
     * @return 包含关联 job 信息的 job instance 列表
     */
    List<DatasyncJobInstance> selectAllJobInstancesWithJob(@Param(Constants.WRAPPER) QueryWrapper<DatasyncJobInstance> wrapper);

    /**
     * 根据 job instance ID 查询单个 job instance 并内联查询其关联的 job.
     * @param wrapper job instance wrapper
     * @return 包含关联 job 信息的单个 job instance 对象
     */
    DatasyncJobInstance selectJobInstanceWithJobByWrapper(@Param(Constants.WRAPPER) QueryWrapper<DatasyncJobInstance> wrapper);


}
