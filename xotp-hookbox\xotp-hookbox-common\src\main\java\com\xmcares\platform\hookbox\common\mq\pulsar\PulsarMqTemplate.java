/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/14
 */
package com.xmcares.platform.hookbox.common.mq.pulsar;

import com.xmcares.platform.admin.common.datasource.mq.MessageHeaders;
import com.xmcares.platform.admin.common.datasource.mq.pulsar.PulsarDataSource;
import com.xmcares.platform.admin.common.datasource.mq.pulsar.PulsarProperties;
import com.xmcares.platform.hookbox.common.mq.MqTemplate;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public class PulsarMqTemplate implements MqTemplate {
    private final PulsarDataSource dataSource;
    public PulsarMqTemplate(PulsarDataSource dataSource) {
        this.dataSource = dataSource;
    }
    @Override
    public void sendMessage(String topicName, MessageHeaders headers, byte[] message) {
        this.dataSource.sendMessage(topicName, headers, message);
    }

    public PulsarProperties getProperties() {
        return this.dataSource.getProperties();
    }
}
