/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.source;

import org.apache.seatunnel.api.source.SourceSplit;

import java.util.List;
import java.io.Serializable;
import java.util.Objects;

public class MqttSourceSplit implements SourceSplit, Serializable {

    private static final long serialVersionUID = 1L;

    /** 分片ID */
    private String splitId;

    /** 分配给此分片的Topic列表 */
    private List<String> topics;

    /** QoS等级 */
    private int qos;

    @Override
    public String splitId() {
        return splitId;
    }

    @Override
    public String toString() {
        return "MqttSourceSplit{"
                + "splitId='"
                + splitId
                + '\''
                + ", topics="
                + topics
                + ", qos="
                + qos
                + '}';
    }

    public String getSplitId() {
        return splitId;
    }

    public void setSplitId(String splitId) {
        this.splitId = splitId;
    }

    public List<String> getTopics() {
        return topics;
    }

    public void setTopics(List<String> topics) {
        this.topics = topics;
    }

    public int getQos() {
        return qos;
    }

    public void setQos(int qos) {
        this.qos = qos;
    }

    public MqttSourceSplit(String splitId, List<String> topics, int qos) {
        this.splitId = splitId;
        this.topics = topics;
        this.qos = qos;
    }

    public MqttSourceSplit() {
    }

    @Override
    public int hashCode() {
        return Objects.hash(splitId, topics, qos);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        MqttSourceSplit that = (MqttSourceSplit) obj;
        return qos == that.qos &&
                Objects.equals(splitId, that.splitId) &&
                Objects.equals(topics, that.topics);
    }

}
