{
  "plugin_name": "Jdbc",
  "plugin_output": "${source.plugin_output!'default_source_output'}",
  "url": "${orginDatasource.url}",
  "driver": "${orginDatasource.driver}",
  "user": "${orginDatasource.username}",
  "password": "${orginDatasource.password}",
  <#if orgin.querySql??>
  "query": "${orgin.querySql}"
  <#else>
  "query": "SELECT <#list orginFieldNames as field>${field}<#if field_has_next>,</#if></#list> FROM ${orgin.table} <#if orgin.where?has_content>WHERE ${orgin.where}</#if>"
  </#if>
}
