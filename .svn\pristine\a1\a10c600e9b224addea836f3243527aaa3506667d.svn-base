/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/17
 */
package com.xmcares.platform.hookbox.common.job.seatunnel;

import com.xmcares.platform.hookbox.common.job.JobContext;
import com.xmcares.platform.hookbox.common.job.JobContextService;
import com.xmcares.platform.hookbox.common.job.error.JobExecutionException;
import com.xmcares.platform.hookbox.common.util.ManagedProcess;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SeaTunnelJobService 是 HookBox 中用于管理 SeaTunnel 任务生命周期的核心服务类。
 *
 * 主要职责包括：
 * - 启动 SeaTunnel 任务（构建并运行命令行进程）
 * - 停止运行中的任务（封装进程停止逻辑）
 * - 维护运行中任务的状态（使用 ConcurrentHashMap 管理）
 * - 动态注入参数（通过 --variable 启动参数传递）
 * - 保存任务配置文件到本地
 *
 * <AUTHOR>
 * @since 2.1.0
 */
public class LocalSeaTunnelJobContextService implements JobContextService {
    private static final Logger logger = LoggerFactory.getLogger(LocalSeaTunnelJobContextService.class);

    // 运行中的 SeaTunnel 任务进程管理（key: jobInstanceId）
    private final Map<String, ManagedProcess> runningJobs = new ConcurrentHashMap<>();

    // SeaTunnel 安装路径，可配置
    private String seaTunnelHome;

    @Override
    public void startJob(JobContext jobContext) {
        String jobId = jobContext.getJobId();
        String jobName = jobContext.getJobName();
        String jobInstanceId = jobContext.getJobInstanceId();
        // 1. 幂等性检查：避免重复启动同一任务
        if (runningJobs.containsKey(jobInstanceId)) {
            logger.warn("任务 [jobId={}, jobInstanceId={})] 已经在运行中，跳过重复启动。", jobId, jobInstanceId);
            return;
        }

        // 2. 保存任务配置文件（任务 YAML）
        String jobFileAbsPath;
        try {
            jobFileAbsPath = saveJobContent(jobContext);
            logger.info("任务 [jobId={}] 的配置文件已保存至：{}", jobId, jobFileAbsPath);
        } catch (IOException e) {
            logger.error("保存任务 [{} (ID: {})] 配置失败：{}", jobName, jobId, e.getMessage(), e);
            throw new JobExecutionException(jobId, jobInstanceId, "保存配置失败，无法启动任务。", e);
        }

        // 3. 获取 SeaTunnel 启动脚本路径
        String scriptFileName = System.getProperty("os.name").toLowerCase().contains("win")
                ? "seatunnel.cmd" : "seatunnel.sh";

        Path seatunnelHomePath = getSeaTunnelHomePath();
        Path seatunnelScriptPath = seatunnelHomePath.resolve("bin").resolve(scriptFileName);

        if (!Files.exists(seatunnelScriptPath) || !Files.isExecutable(seatunnelScriptPath)) {
            logger.error("SeaTunnel 启动脚本不可用：{}", seatunnelScriptPath.toAbsolutePath());
            throw new JobExecutionException(jobId, jobInstanceId, "启动脚本不存在或不可执行：" + seatunnelScriptPath);
        }

        // 4. 构建命令行参数
        List<String> command = new ArrayList<>();
        command.add(seatunnelScriptPath.toAbsolutePath().toString());
        command.add("--config");
        command.add(jobFileAbsPath);

        // 添加动态参数作为 --variable key=value
        Map<String, Object> paramMap = jobContext.getJobParams().getParamForMap("/");
        paramMap.forEach((key, value) -> {
            if (key != null && value != null) {
                command.add("--variable");
                command.add(key + "=" + value);
            }
        });

        // 5. 设置工作目录（一般是 seatunnel 安装目录）
        File workingDir = seatunnelHomePath.toFile();

        try {
            // 6. 启动进程并交由 ManagedProcess 管理
            ManagedProcess managedProcess = new ManagedProcess(jobId, jobName, command, workingDir);

            // 注册进程完成回调
            managedProcess.setOnCompletionCallback(exitCode -> {
                runningJobs.remove(jobId);
                if (exitCode == 0) {
                    logger.info("任务 [{} (ID: {})] 正常完成，退出码={}。", jobName, jobId, exitCode);
                } else {
                    logger.error("任务 [{} (ID: {})] 执行失败，退出码={}。", jobName, jobId, exitCode);
                    // TODO: 记录失败状态到数据库或告警系统
                }
            });

            // 注册输出日志回调
            managedProcess.setOnOutputCallback(line ->
                    logger.info("[SeaTunnel Job Output][{}]: {}", jobId, line));

            runningJobs.put(jobId, managedProcess);
            logger.info("任务 [{} (ID: {})] 已异步启动，由 ManagedProcess 管理。", jobName, jobId);

        } catch (IOException e) {
            logger.error("启动任务 [{} (ID: {})] 失败：{}", jobName, jobId, e.getMessage(), e);
            runningJobs.remove(jobId);
            throw new JobExecutionException(jobId, jobInstanceId, "启动任务失败：" + jobName, e);
        }
    }

    @Override
    public void stopJob(JobContext jobContext) {
        String jobId = jobContext.getJobId();
        String jobInstanceId = jobContext.getJobInstanceId();
        ManagedProcess managedProcess = runningJobs.remove(jobInstanceId);
        if (managedProcess == null) {
            logger.warn("尝试停止不存在或已结束的任务[jobId={}, jobInstanceId={}", jobId, jobInstanceId);
            return;
        }
        managedProcess.stop(10); // 优雅停止
        logger.info("任务 [jobId={}, jobInstanceId={}] 已停止。", jobId, jobInstanceId);
    }

    /** 获取应用主目录（可通过 app.home 配置） */
    public String getAppHome() {
        String result = System.getProperty("app.home");
        if (result == null) {
            result = System.getProperty("user.dir");
            logger.warn("未设置 app.home，使用当前目录作为应用目录: {}", result);
        }
        return result;
    }

    /** 获取 SeaTunnel 安装目录路径 */
    public Path getSeaTunnelHomePath() {
        String result = getSeaTunnelHome();
        return result == null ? Paths.get(getAppHome(), "seatunnel") : Paths.get(result);
    }

    public String getSeaTunnelHome() {
        return this.seaTunnelHome;
    }

    public void setSeaTunnelHome(String seaTunnelHome) {
        this.seaTunnelHome = seaTunnelHome;
    }

    /**
     * 将任务内容保存为本地 job 文件（支持 Linux 和 Windows）
     * @param jobContext 任务上下文
     * @return 配置文件的绝对路径
     */
    private String saveJobContent(JobContext jobContext) throws IOException {
        String jobId = jobContext.getJobId();
        String jobContent = jobContext.getJobOptions();

        Path targetPath = Paths.get(getAppHome(), "seatunnel", "jobs", jobId+".json");
        Files.createDirectories(targetPath.getParent());

        Files.write(targetPath,
                jobContent.getBytes(StandardCharsets.UTF_8),
                StandardOpenOption.CREATE,
                StandardOpenOption.TRUNCATE_EXISTING,
                StandardOpenOption.WRITE);

        logger.debug("任务配置文件 [{}] 已写入。", targetPath.toAbsolutePath());
        return targetPath.toAbsolutePath().toString();
    }
}

