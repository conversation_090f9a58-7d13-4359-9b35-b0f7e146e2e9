// https://umijs.org/config/
import os from 'os';
import pageRoutes from './router/';
import webpackPlugin from './plugin.config';
import defaultSettings from '../src/defaultSettings';

import loadEnv from './loadEnv';

const isEnvProduction = process.env.NODE_ENV === 'production';
const plugins = [
  [
    'umi-plugin-react',
    {
      antd: true,

      dva: {
        hmr: true,
      },
      targets: {
        ie: 11,
      },
      locale: {
        enable: true, // default false
        default: 'zh-CN', // default zh-CN
        baseNavigator: true, // default true, when it is true, will use `navigator.language` overwrite default
      },
      dynamicImport: {
        loadingComponent: './components/PageLoading/index',
      },
      pwa: {
        workboxPluginMode: 'InjectManifest',
        workboxOptions: {
          importWorkboxFrom: 'local',
        },
      },
      // 生产环境去除console日志打印
      terserOptions: {
        compress: {
          drop_console: isEnvProduction,
        },
      },
      ...(!process.env.TEST && os.platform() === 'darwin'
        ? {
            dll: {
              include: ['dva', 'dva/router', 'dva/saga', 'dva/fetch'],
              exclude: ['@babel/runtime'],
            },
            hardSource: true,
          }
        : {}),
    },
  ],
];
const path = require('path');
const CompressionWebpackPlugin = require('compression-webpack-plugin');
const isEnvDevelopment = process.env.NODE_ENV === 'development';
const resolve = dir => path.join(__dirname, dir);
const assetDir = 'static';

// 针对 preview.pro.ant.design 的 GA 统计代码
// 业务上不需要这个
if (process.env.APP_TYPE === 'site') {
  plugins.push([
    'umi-plugin-ga',
    {
      code: '***********-6',
    },
  ]);
}
//10.83.3.74
// const proxyServer="http://10.83.5.254:9500";
const proxyServer = 'http://***********:9500';

export default {
  // add for transfer to umi
  plugins,
  context: {
    config: {
      publicPath: '',
    },
  },
  targets: {
    ie: 11,
  },

  define: {
    API_BASE: process.env.UMI_APP_API_BASE,
    APP_TYPE: process.env.APP_TYPE || '',
    HOME: '/privilege/user',
  },
  // 路由配置
  routes: pageRoutes,
  // Theme for antd
  // https://ant.design/docs/react/customize-theme-cn
  theme: {
    'primary-color': defaultSettings.primaryColor,
  },
  externals: {
    '@antv/data-set': 'DataSet',
  },
  manifest: {
    basePath: '/',
  },
  proxy: {
    '/api': {
      target: 'http://***********:8085',
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
    '/api/admin-service': {
      target: 'http://***********:8085',
      changeOrigin: true,
      pathRewrite: { '^/api/admin-service': '' },
    },
    '/xdtvui': {
      target: 'http://localhost:18083',
      changeOrigin: true,
      pathRewrite: { '^/xdtvui': '' },
    },
    // '/api/admin-service/metadataservice': {
    //   target: 'http://***********:8081',
    //   changeOrigin: true,
    //   pathRewrite: { '^/api/admin-service/metadataservice': '' },
    // },
    // '/api/admin-service/integratorservice': {
    //   target: 'http://***********:8081',
    //   changeOrigin: true,
    //   pathRewrite: { '^/api/admin-service/integratorservice': '' },
    // },
    // '/api/admin-service/metadataservice': {
    //   target: 'http://***********:8081',
    //   changeOrigin: true,
    //   pathRewrite: { '^/api/admin-service/metadataservice': '' },
    // },
    // '/api/dataservice': {
    //   target: 'http://127.0.0.1:8081',
    //   changeOrigin: true,
    //   pathRewrite: { '^/api': '' },
    // },
    // '/api': {
    //   target: 'http://127.0.0.1:8081',
    //   changeOrigin: true,
    // },
    '/loggingin.json': proxyServer,
    '/captcha': proxyServer,
    '/route/*': proxyServer,
    '/api/governace-service/*': {
      target: 'http://10.83.100.211:30170',
      changeOrigin: true,
      pathRewrite: { '^/api/governace-service': '' },
    },
    /*'/loggingin.json': 'http://**********:30107',
    '/captcha': 'http://**********:30107',
    '/route/!*': 'http://**********:30107',*/
    // '/route/sysParam/*': 'http://**********:9020',
    changeOrigin: true,
  },
  /*
  *'/loggingin.json': 'http://**********:30106',
    '/captcha': 'http://**********:30106',
    '/route/*': 'http://**************:9020',
   */
  ignoreMomentLocale: true,
  lessLoaderOptions: {
    javascriptEnabled: true,
  },
  disableRedirectHoist: true,
  cssLoaderOptions: {
    modules: true,
    getLocalIdent: (context, localIdentName, localName) => {
      if (
        context.resourcePath.includes('node_modules') ||
        context.resourcePath.includes('ant.design.pro.less') ||
        context.resourcePath.includes('global.less')
      ) {
        return localName;
      }
      const match = context.resourcePath.match(/src(.*)/);
      if (match && match[1]) {
        const antdProPath = match[1].replace('.less', '');
        const arr = antdProPath
          .split('/')
          .map(a => a.replace(/([A-Z])/g, '-$1'))
          .map(a => a.toLowerCase());
        return `antd-pro${arr.join('-')}-${localName}`.replace(/--/g, '-');
      }
      return localName;
    },
  },
  chainWebpack: webpackPlugin,
};
