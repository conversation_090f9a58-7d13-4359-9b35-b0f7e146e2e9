/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/28
 */
package com.xmcares.platform.hookbox.common.job.context;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xmcares.framework.commons.util.JacksonJsonUtils;
import com.xmcares.platform.hookbox.common.job.JobParams;

import java.io.IOException;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public class JsonJobParams implements JobParams {


    private final JsonNode root;

    // 构造函数，接受 JSON 字符串并解析为 JsonNode
    public JsonJobParams(String jsonStr){
        try {
            this.root = JacksonJsonUtils.readJson(jsonStr);
        } catch (IOException e) {
            throw new IllegalArgumentException("jsonStr不是合法的json字符串", e);
        }
    }

    public JsonJobParams(JsonNode root) {
        if (root == null) {
            throw new IllegalArgumentException("root cannot be null");
        }
        this.root = root;
    }

    /**
     * 获取指定路径的参数
     * @param path 路径: /a/b
     * @return String
     */
    public String getParam(String path) {
        JsonNode node = root.at(normalizePath(path));
        return node.isMissingNode() ? null : node.asText();
    }

    /**
     * 获取指定路径的参数并返回为 Map
     * @param path 路径: /a/b
     * @return Map<String, Object>
     */
    public Map<String, Object> getParamForMap(String path) {
        JsonNode node = root.at(normalizePath(path));
        return JacksonJsonUtils.convert(node, JacksonJsonUtils.getMapType(Map.class, String.class, Object.class));
    }

    /**
     * 获取指定路径的参数并返回为 Long
     * @param path 路径: /a/b
     * @return Long
     */
    @Override
    public Long getParamForLong(String path) {
        JsonNode node = root.at(normalizePath(path));
        return node.isMissingNode() ? null : node.asLong();
    }

    /**
     * 获取指定路径的参数并返回为 Boolean
     * @param path 路径: /a/b
     * @return Boolean
     */
    @Override
    public Boolean getParamForBoolean(String path) {
        JsonNode node = root.at(normalizePath(path));
        return node.isMissingNode() ? null : node.asBoolean();
    }

    public <T> T getParamForClass(String path, Class<T> clazz) {
        JsonNode node = root.at(normalizePath(path));
        return node.isMissingNode() ? null : JacksonJsonUtils.convert(node, clazz);
    }


    /**
     * 设置指定的参数
     * @param path 路径: /a/b
     * @param value 值
     */
    public void putParam(String path, Object value) {
        path = normalizePath(path);
        // Handle the root path directly
        if (path.equals("/")) {
            // Overwrite the entire root node
            throw new IllegalArgumentException("Cannot put into root path");
        }

        // Convert the input Java Object to a suitable JsonNode type
        JsonNode valueNode = JacksonJsonUtils.getObjectMapper().valueToTree(value);

        // Split the path into segments, ignoring the leading "/"
        String[] segments = path.substring(1).split("/");

        JsonNode current = this.root; // 从根开始遍历
        ObjectNode parent = null;     // 跟踪父节点以进行修改

        for (int i = 0; i < segments.length; i++) {
            String segment = segments[i];
            // 如果在最后一个segment
            if (i == segments.length - 1) {
                if (current.isObject()) {
                    // 这是我们将放置最终值的对象
                    ((ObjectNode) current).set(segment, valueNode);
                }
                // This 'else if' block handles cases where the immediate parent for the final
                // segment might be MissingNode or NullNode, indicating it needs to be created.
                // This would happen if the *parent* was just created in the previous iteration.
                else if (current.isMissingNode() || current.isNull()) {
                    if (parent != null) {
                        // parent must be an ObjectNode if we got here through object traversal
                        ((ObjectNode) parent).set(segment, valueNode);
                    } else {
                        // This case should ideally not be reachable if root is always an object,
                        // and path is not "/", and if intermediate nodes are created as objects.
                        throw new IllegalStateException("Unexpected state: Root is missing/null and path is not '/', but no parent was established.");
                    }
                }
                // If current is an ArrayNode or ValueNode (e.g., "/a/0", "/a/b"),
                // and this is the last segment, it implies an attempt to set a value *within* it,
                // but our current segment logic only handles object keys.
                // For array index updates like "/arr/0", special parsing of segment is needed.
                else {
                    throw new IllegalArgumentException("不能导航路径'"+path +"' 到'" + segment + "'处理，该节点不是对象类型："+ current.getNodeType());
                }
            }
            // 如果是中间segment，则需要判断当前segment对应的值是否为object
            else {
                if (current.isObject()) {
                    parent = (ObjectNode) current;
                    JsonNode next = parent.get(segment); // Try to get the next node
                    if (next == null || next.isMissingNode() || !next.isObject()) {
                        // If the next node doesn't exist, is missing, or is not an object,
                        // create a new ObjectNode for it.
                        ObjectNode newObject = JacksonJsonUtils.getObjectMapper().createObjectNode();
                        parent.set(segment, newObject);
                        current = newObject;
                    } else {
                        // Move to the existing object
                        current = next;
                    }
                } else if (current.isMissingNode() || current.isNull()) {
                    // If an intermediate current node is missing or null, we need to create an object for it
                    // and set it on its parent. This implies `parent` must not be null here.
                    if (parent == null) {
                        // This indicates a problem with the initial root or an invalid path
                        throw new IllegalStateException("Unexpected null parent for missing intermediate node during deep path traversal.");
                    }
                    ObjectNode newObject = JacksonJsonUtils.getObjectMapper().createObjectNode();
                    parent.set(segment, newObject);
                    parent = newObject; // New object becomes the new parent
                    current = newObject; //将当前对象移动到新对象
                }
                else {
                    // 如果中间节点是非对象类型（例如 ArrayNode、ValueNode）
                    throw new IllegalArgumentException("不能导航路径'"+path +"' 到'" + segment + "'处理，该节点不是对象类型："+ current.getNodeType());
                }
            }
        }
    }

    /**
     * 确保路径以 "/" 开头
     */
    private String normalizePath(String path) {
        if (path == null || path.isEmpty()) {
            throw new IllegalArgumentException("path cannot be null or empty");
        }
        return path.startsWith("/") ? path : "/" + path;
    }

    private String getLastFieldName(String path) {
        int lastSlashIndex = path.lastIndexOf('/');
        return lastSlashIndex == -1 ? path : path.substring(lastSlashIndex + 1);
    }

    @Override
    public String toString() {
        return root.toString();
    }

    @Override
    public String toJsonString() {
        try {
            return JacksonJsonUtils.writeObjectAsString(root);
        } catch (IOException e) {
            throw new IllegalArgumentException("JSON序列化失败", e);
        }
    }

    public static void main(String[] args) {
        JsonJobParams jsonJobParams = new JsonJobParams("{\"a\":{\"b\":{\"c\":\"d\"}}}");
        jsonJobParams.putParam("/a/b/node", new Date());

        System.out.println(jsonJobParams.getParamForClass("/a/b/node", Date.class));

    }
}
