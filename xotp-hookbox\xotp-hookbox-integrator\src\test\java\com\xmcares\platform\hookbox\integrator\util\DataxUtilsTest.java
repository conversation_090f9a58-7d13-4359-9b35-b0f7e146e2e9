package com.xmcares.platform.hookbox.integrator.util;

import com.xmcares.platform.admin.common.util.CommonConstants;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

class DataxUtilsTest {

    @Test
    void buildDataXParam() {

        Map<String, String> incParams = new HashMap<>();
        incParams.put(CommonConstants.DATAX_DISPATCH_PARAMS_INC_INCMODE, "TIME");
        incParams.put(CommonConstants.DATAX_DISPATCH_PARAMS_INC_REPLACEPARAM, "-DlastTime='%s' -DcurrentTime='%s'");
        incParams.put(CommonConstants.DATAX_DISPATCH_PARAMS_INC_REPLACEPARAMTYPE, "yyyy-MM-dd HH:mm:ss");
        incParams.put(CommonConstants.DATAX_DISPATCH_PARAMS_INC_INCSTARTTIME, "2022-08-18 15:06:11");
        incParams.put(CommonConstants.DATAX_DISPATCH_PARAMS_INC_TRIGGERTIME, "2022-08-19 15:06:11");

        Map<String, Map<String, String>> executeParams = new HashMap<>();
        executeParams.put(CommonConstants.DATAX_DISPATCH_PARAMS_INC_KEY,incParams);
        //System.out.println(DataxUtils.buildDataXParam(executeParams));


    }
}
