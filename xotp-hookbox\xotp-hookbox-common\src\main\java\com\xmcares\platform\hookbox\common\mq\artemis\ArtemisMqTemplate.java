/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/14
 */
package com.xmcares.platform.hookbox.common.mq.artemis;

import com.xmcares.platform.admin.common.datasource.mq.MessageHeaders;
import com.xmcares.platform.admin.common.datasource.mq.artemis.ArtemisDataSource;
import com.xmcares.platform.admin.common.datasource.mq.artemis.ArtemisProperties;
import com.xmcares.platform.hookbox.common.mq.MqTemplate;

/**
 * Artemis MQ模板
 * <AUTHOR>
 * @since 2.1.0
 */
public class ArtemisMqTemplate implements MqTemplate {

    private final ArtemisDataSource dataSource;

    public ArtemisMqTemplate(ArtemisDataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public void sendMessage(String topicName, MessageHeaders headers, byte[] message) {
        this.dataSource.sendMessage(topicName, headers, message);
    }

    public ArtemisProperties getProperties() {
        return this.dataSource.getProperties();
    }
}
