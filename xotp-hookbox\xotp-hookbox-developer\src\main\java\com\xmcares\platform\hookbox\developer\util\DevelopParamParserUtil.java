package com.xmcares.platform.hookbox.developer.util;

import com.xmcares.platform.admin.common.util.CommonConstants;
import com.xmcares.platform.hookbox.core.properties.HookboxProperties;
import com.xmcares.platform.hookbox.developer.vo.DevDataflowFileVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import static com.xmcares.platform.hookbox.developer.vo.DevDataflowFileVo.DevDataflowFileBody;
import static com.xmcares.platform.hookbox.developer.vo.DevDataflowFileVo.DevDataflowFileParam;

/**
 * 数据开发参数解析工具
 *
 * <AUTHOR>
 * @date 2022/6/23 10:40
 **/
@Component
public class DevelopParamParserUtil {

    private static DevelopParamParserUtil THIS;
    static Logger logger = LoggerFactory.getLogger(DevelopParamParserUtil.class);
    @Autowired
    public DevelopParamParserUtil(HookboxProperties properties) {
        THIS = this;
        this.jsonPath = properties.getLocalRoot() + File.separator + "DEVELOPER_JSON";
    }

    private final String jsonPath;
    private File jsonTempStorageRoute;

    private synchronized File getJsonTempStorageRouteFile() {
        if (jsonTempStorageRoute != null) {
            return jsonTempStorageRoute;
        }
        File ft = new File(jsonPath);
        if (!ft.exists()){
            if (!ft.mkdirs()){
                logger.info("create DEVELOPER_JSON error -{}-", this.jsonPath);
            }
        }else{
            if (!ft.isDirectory()){
                ft.deleteOnExit();
                if (!ft.mkdirs()){
                    logger.info("create DEVELOPER_JSON error -{}-", this.jsonPath);
                }
            }
        }
        jsonTempStorageRoute = ft;
        return jsonTempStorageRoute;
    }

    /**
     * 解析参数
     * 优先级如下：
     * 自身参数 > 局部参数 > 全局参数
     *
     * @param dispatchParam 系统参数 + 局部参数
     * @return Map
     */
    public static Map<String, Object> parserParam(String dispatchParam) {
        Map<String, Object> result = new HashMap<>();

        // 1. 处理系统参数
        Map<String, String> sysResult = XxlJobParamUtils.sysParams(dispatchParam);
        for (Map.Entry<String, String> sysEntry : sysResult.entrySet()) {
            result.put(CommonConstants.DATAX_DISPATCH_PARAMS_SYS_KEY + CommonConstants.SPLIT_POINT + sysEntry.getKey(),
                    sysEntry.getValue());
        }
        // 1.1. 下载与加载配置文件 ，  路径用 sys.filePath 获取
        File storageFile = THIS.getJsonTempStorageRouteFile();
        DevDataflowFileVo dataflow = ConfigurationFileUtil.downloadAndLoadContext(
                result.get(CommonConstants.buildSysParamKey(CommonConstants.DATAX_DISPATCH_PARAMS_PARAM_FILEPATH)).toString(),
                storageFile.getAbsolutePath());
        // 2. 处理全局参数
        if (CollectionUtils.isNotEmpty(dataflow.getGlobalParams())) {
            for (DevDataflowFileParam globalParam : dataflow.getGlobalParams()) {
                if ((!"originalName".equals(globalParam.getKey())) || (!"nodeName".equals(globalParam.getKey()))) {
                    result.put(globalParam.getKey(), globalParam.getValue());
                }
            }
        }
        // 3. 处理局部参数
        Map<String, String> parserResult = XxlJobParamUtils.findParams(dispatchParam, CommonConstants.DEVELOP_DISPATCH_PARAMS_PARTIAL_KEY);
        if (MapUtils.isNotEmpty(parserResult)) {
            result.putAll(parserResult);
        }
        // 3 + . 处理增量参数
        Map<String, String> incResult = XxlJobParamUtils.findParams(dispatchParam, CommonConstants.DEVELOP_DISPATCH_PARAMS_INC_KEY);
        if (MapUtils.isNotEmpty(incResult)) {
            result.putAll(incResult);
        }

        // 4. 处理自身参数
        // 4.1. 获取参数
        String nodeId = sysResult.get(CommonConstants.DEVELOP_DISPATCH_PARAMS_SYS_NODEID);
        Map<String, Object> thisParams = new HashMap<>();
        if (StringUtils.isNotEmpty(nodeId)) {
            DevDataflowFileBody findOne = (DevDataflowFileBody)CollectionUtils.find(dataflow.getBodies(),
                    o -> ((DevDataflowFileBody) o).getId().equals(nodeId));

            if (ObjectUtils.allNotNull(findOne) && CollectionUtils.isNotEmpty(findOne.getParams())) {
                for (DevDataflowFileParam taskParam : findOne.getParams()) {
                    thisParams.put(taskParam.getKey(), taskParam.getValue());
                }
            }
        }

        //自身参数过滤
        if (MapUtils.isNotEmpty(thisParams)) {

            HashMap<String, Object> userConfigs = new HashMap<>();

            thisParams.forEach((k, v) -> {
                if (k.startsWith("user.")) {
                    userConfigs.put(k, v);
                }
            });

            for (String k : userConfigs.keySet()) {
                result.put(k, userConfigs.get(k));
                thisParams.remove(k);
            }
        }

        // 4.2. 处理自身参数的动态参数
        if (MapUtils.isNotEmpty(thisParams)) {
            // 4.2.2. 解析值替换参数
            thisParams = DevelopParamUtils.replace(thisParams, result);
            // 4.2.3. 将参数加入返回结果集
            result.putAll(thisParams);
        }
        return result;
    }

}
