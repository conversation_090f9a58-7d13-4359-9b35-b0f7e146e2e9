package com.xmcares.platform.admin.integrator.datasync.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.framework.commons.util.CodeGenerator;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.integrator.common.error.IntegratorException;
import com.xmcares.platform.admin.integrator.common.util.ConstantUtils;
import com.xmcares.platform.admin.integrator.datasync.dto.JobLogPageListDto;
import com.xmcares.platform.admin.integrator.datasync.dto.JobLogPageVo;
import com.xmcares.platform.admin.integrator.datasync.dto.XxlJobLogWithInstanceVO;
import com.xmcares.platform.admin.integrator.datasync.model.*;
import com.xmcares.platform.admin.integrator.datasync.repository.SchedulerRepository;
import com.xmcares.platform.admin.integrator.datasync.repository.mapper.DatasyncJobMapper;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.XxljobClient;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobGroup;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobInfo;
import com.xmcares.platform.admin.integrator.datasync.vo.DisplayDataSyncTask;
import com.xmcares.platform.admin.integrator.datasync.vo.UpdateDatasyncTask;
import com.xxl.job.core.biz.model.LogResult;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> huzk
 * @date : 2025/7/16 9:21
 */
@Service
public class DatasyncJobService extends ServiceImpl<DatasyncJobMapper, DatasyncJob>{
    private static final Logger LOG = LoggerFactory.getLogger(DatasyncJobService.class);

    @Autowired
    private SchedulerRepository schedulerRepository;

    @Autowired
    private XxljobClient xxlJobClient;

//    @Autowired
//    private DataxFileRepository dataxFileRepository;


    public DatasyncJob checkExit(String id) {
        DatasyncJob datasync = getById(id);
        if(datasync == null) {
            throw new IntegratorException("数据同步任务不存在");
        }
        return datasync;
    }

    /**
     * 获取详细
     * @param id 任务表信息
     * @return
     */
    public DisplayDataSyncTask get(String id) {
        DatasyncJob datasync = checkExit(id);
        Map<String, SchedulerJob> schedulerJobMap = schedulerRepository.querySchedulerJobs(Arrays.asList(datasync.getScheduleId()));
        SchedulerJob schedulerJob = schedulerJobMap.get(datasync.getScheduleId());
        DisplayDataSyncTask result = DisplayDataSyncTask.createNewBaseFrom(datasync);
        result.buildDispatchInfo(schedulerJob);
        return result;
    }

    /**
     * 根据数据同步定义表ID获取同步任务表数据
     * @param parentId 同步定义表ID
     * @return 同步任务表 + 调度表数据
     */
    public List<DisplayDataSyncTask> findByParentId(String parentId) {
        QueryWrapper<DatasyncJob> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("datasync_id", parentId)
                .eq("deleted", YNEnum.NO.getIntCharCode())
                .orderByDesc("update_time");
        List<DatasyncJob> findResult = list(queryWrapper);
        if (findResult.isEmpty()) {
            return new ArrayList<>();
        }
        List<DisplayDataSyncTask> resultData = findResult.stream().map(DisplayDataSyncTask::createNewBaseFrom).collect(Collectors.toList());
        List<String> jobIds = findResult.stream().map(DatasyncJob::getScheduleId).collect(Collectors.toList());
        //获取调度任务信息

        if(!jobIds.isEmpty()) {
            Map<String, SchedulerJob> schedulerJobMap = schedulerRepository.querySchedulerJobs(jobIds);
            resultData.forEach((item) -> {
                String key = item.getDispatchId();
                if(schedulerJobMap.containsKey(key)) {
                    item.buildDispatchInfo(schedulerJobMap.get(key));
                }
            });
        }
        return resultData;
    }

    /**
     * 更新调度信息
     * @param datasyncTask 调度信息
     */
    public void update(UpdateDatasyncTask datasyncTask) {
        DatasyncJob oldTask = checkExit(datasyncTask.getId());
        if (isRun(oldTask.getScheduleId())) {
            throw new IntegratorException("数据同步运行中，不允许修改或删除");
        }
        Datasync datasync = UpdateDatasyncTask.toDatasync(datasyncTask);
        schedulerRepository.updateScheduler(oldTask.getScheduleId(), datasync);
        // TODO: 现在需要更新本地的调度信息，远程回调成功后，再修改本地的
        DatasyncJob updateJob = new DatasyncJob();
        BeanUtils.copyProperties(oldTask, updateJob);
        updateJob.setScheduleConf(datasyncTask.getSchedulerExpr());
        updateJob.setExecutorRouteStrategy(datasyncTask.getRouteStrategy());
        updateJob.setExecutorBlockStrategy(datasyncTask.getBlockStrategy());
        updateJob.setExecutorTimeout(datasyncTask.getExecutorTimeout());
        updateJob.setExecutorFailRetryCount(datasyncTask.getExecutorFailRetryCount());
        updateJob.setJobName(datasyncTask.getInstanceName());
        updateJob.setUpdateTime(DateUtil.date());
        updateById(updateJob);
    }

    /**
     * 开始运行调度任务
     * @param id 同步任务表ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void begin(String id) {
        //1. 校验状态等信息后才允许开始运行：启动就不要再启动
        DatasyncJob datasync = this.checkExit(id);
        if (isRun(datasync.getScheduleId())) {
            throw new IntegratorException("任务已运行，请先停止运行");
        }
        //2. 上傳一份文件到服務
        // dataxFileRepository.upload(datasync.getFilePath(), datasync.getTemplateContext());

        // 根据任务类型，判断是批类型还是流类型
        // 流类型执行trigger一次即可，并且update状态为 运行中
        //3. 開始運行
        if (StringUtils.equalsIgnoreCase(datasync.getScheduleType(), ConstantUtils.SCHEDULER_DEF_STREAMING_SCHEDULE_TYPE)) {
            schedulerRepository.triggerScheduler(datasync.getScheduleId());
        }else {
            schedulerRepository.beginScheduler(datasync.getScheduleId());
        }

        // 修改triggerStatus和updateTime
        UpdateWrapper<DatasyncJob> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id)
                .set("status", YNEnum.YES.getIntCode())
                .set("update_time", new Date());
        update(updateWrapper);
    }

    /**
     * 停止运行
     * @param id 同步任务表ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void end(String id) {
        //校验状态等信息后才允许开始运行：停止就不要再停止
        DatasyncJob datasync = this.checkExit(id);
        if (!isRun(datasync.getScheduleId())) {
            throw new IntegratorException("任务已停止，请勿重复停止");
        }
        schedulerRepository.endScheduler(datasync.getScheduleId());
        // 结束后，再次调用执行一次的DatasyncEndHandler
        xxlJobClient.triggerJob(XxlJobInfo.buildStopDatasyncTriggerJob(datasync));
        // 修改triggerStatus和updateTime
        UpdateWrapper<DatasyncJob> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id)
                .set("status", YNEnum.NO.getIntCode())
                .set("update_time", new Date());
        update(updateWrapper);
    }

    /**
     * 运行一次
     * @param id 同步任务表ID
     */
    public void triggerScheduler(String id) {
        //1. 校验状态等信息后才允许开始运行：启动就不要再启动
        DatasyncJob datasync = this.checkExit(id);
        if (isRun(datasync.getScheduleId())) {
            throw new IntegratorException("任务已运行，请先停止运行");
        }
        //2. 從新上傳一份文件到服務 不再需要上传的文件
        // dataxFileRepository.upload(datasync.getFilePath(), datasync.getTemplateContext());
        //3. 运行一次
        schedulerRepository.triggerScheduler(datasync.getScheduleId());
    }

    /**
     * 将状态变更成已经删除
     * @param id 同步信息定义ID
     */
    public Boolean transitionToDelete(String id) {
        // 1. 验证运行状态
        DatasyncJob datasync = this.checkExit(id);
        if (isRun(datasync.getScheduleId())) {
            throw new IntegratorException("任务已运行，请先停止运行");
        }
        // 1.先删除xxl-job处的数据，再删除本地存储的那份
        schedulerRepository.removeScheduler(datasync.getScheduleId());
        // 2. 执行假删除操作
        UpdateWrapper<DatasyncJob> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id).set("deleted", YNEnum.YES.getIntCharCode());
        return update(updateWrapper);
    }

    public com.xmcares.framework.commons.domain.Page<DatasyncJob> findNeedDeletePage(Pagination pagination) {
        QueryWrapper<DatasyncJob> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", YNEnum.YES.getIntCharCode());

        Page<DatasyncJob> mybatisPlusPage = new Page<>(pagination.getPageNo(), pagination.getPageSize());
        Page<DatasyncJob> result = page(mybatisPlusPage, queryWrapper);

        // 转换为框架的Page对象
        com.xmcares.framework.commons.domain.Page<DatasyncJob> frameworkPage = new com.xmcares.framework.commons.domain.Page<>();
        frameworkPage.setPageNo((int)result.getCurrent());
        frameworkPage.setPageSize((int)result.getSize());
        frameworkPage.setTotal((int)result.getTotal());
        frameworkPage.setData(result.getRecords());
        return frameworkPage;
    }

    public Boolean removeData(String id) {
        DatasyncJob datasync = getById(id);
        if (datasync == null) { return true; }
        if (StringUtils.isNotEmpty(datasync.getScheduleId())) {
            try {
                schedulerRepository.removeScheduler(datasync.getScheduleId());
            } catch (Exception e) {
                LOG.error("在删除调度记录【" + datasync.getScheduleId() + "::" + datasync.getJobName() + "】的过程中遇到异常", e);
            }
        }
//        if (StringUtils.isNotEmpty(datasync.getFilePath())) {
//            try {
//                dataxFileRepository.removeFile(datasync.getFilePath());
//            } catch (Exception e) {
//                LOG.error("在删除配置文件【" + datasync.getFilePath() + "::" + datasync.getInstanceName() + "】的过程中遇到异常", e);
//            }
//        }
        return removeById(id);
    }

    private boolean isRun(String dispatchId) {
        Map<String, SchedulerJob> schedulerJobMap = schedulerRepository.querySchedulerJobs(Arrays.asList(dispatchId));
        SchedulerJob schedulerJob = schedulerJobMap.get(dispatchId);
        return schedulerJob.getTriggerStatus() == YNEnum.YES.getIntCode();
    }


    public List<DisplayDataSyncTask> listDisplayDatasyncTask() {
        List<DatasyncJob> findResult = super.list();
        if (findResult.isEmpty()) {
            return new ArrayList<>();
        }
        List<DisplayDataSyncTask> resultData = findResult.stream().map(DisplayDataSyncTask::createNewBaseFrom).collect(Collectors.toList());

        return resultData;

    }

    public List<String> nextTriggerTime(String scheduleType, String scheduleConf) {
        return schedulerRepository.nextTriggerTime(scheduleType, scheduleConf);
    }

    public com.xmcares.framework.commons.domain.Page<JobLogPageVo> jobLogPageList(JobLogPageListDto jobLogPageListDto, int page, int rows) {
        Map<String, Object> resultMap = schedulerRepository.jobLogPageList(jobLogPageListDto);

        // package result
        com.xmcares.framework.commons.domain.Page<JobLogPageVo> result = new com.xmcares.framework.commons.domain.Page<>();
        result.setPageNo(page);
        result.setPageSize(rows);
        result.setTotal(MapUtil.getInt(resultMap, "recordsTotal"));
        List<JobLogPageVo> jobLogPageVos = new ArrayList<>();
        List<Map<String, Object>> mapListDataObject = MapUtil.get(resultMap, "data", List.class, new ArrayList<JobLogPageVo>());
        // TODO 封装
        if (!CollectionUtils.isEmpty(mapListDataObject)) {
            // 组装jobLogPageVos
            mapListDataObject.forEach(item -> {
                JobLogPageVo jobLogPageVo = new JobLogPageVo();
                jobLogPageVo.setId(MapUtil.getLong(item, "id"));
                jobLogPageVo.setJobName(MapUtil.getStr(item, "jobName"));
                jobLogPageVo.setJobGroup(MapUtil.getInt(item, "jobGroup"));
                jobLogPageVo.setJobId(MapUtil.getInt(item, "jobId"));
                jobLogPageVo.setJobTaskType(MapUtil.getStr(item, "jobTaskType"));
                jobLogPageVo.setExecutorAddress(MapUtil.getStr(item, "executorAddress"));
                jobLogPageVo.setExecutorHandler(MapUtil.getStr(item, "executorHandler"));
                jobLogPageVo.setExecutorParam(MapUtil.getStr(item, "executorParam"));
                jobLogPageVo.setExecutorShardingParam(MapUtil.getStr(item, "executorShardingParam"));
                jobLogPageVo.setExecutorFailRetryCount(MapUtil.getInt(item, "executorFailRetryCount"));
                jobLogPageVo.setTriggerTime(MapUtil.getDate(item, "triggerTime"));
                jobLogPageVo.setTriggerCode(MapUtil.getInt(item, "triggerCode"));
                jobLogPageVo.setTriggerMsg(MapUtil.getStr(item, "triggerMsg"));
                jobLogPageVo.setHandleTime(MapUtil.getDate(item, "handleTime"));
                jobLogPageVo.setHandleCode(MapUtil.getInt(item, "handleCode"));
                jobLogPageVo.setHandleMsg(MapUtil.getStr(item, "handleMsg"));
                jobLogPageVo.setAlarmStatus(MapUtil.getInt(item, "alarmStatus"));
                jobLogPageVo.setProcessId(MapUtil.getStr(item, "processId"));
                jobLogPageVo.setMaxId(MapUtil.getStr(item, "maxId"));
                jobLogPageVos.add(jobLogPageVo);
            });
            List<String> collect = jobLogPageVos.stream().map(item -> item.getJobId() + "").collect(Collectors.toList());
            List<DatasyncJob> datasyncJobs = listByIds(collect);
            if (!CollectionUtils.isEmpty(datasyncJobs)) {
                Map<String, List<DatasyncJob>> collectByDispatchId = datasyncJobs.stream().collect(Collectors.groupingBy(DatasyncJob::getScheduleId));
                // 防止本地不存在对应的日志
                if (MapUtil.isNotEmpty(collectByDispatchId)) {
                    jobLogPageVos.forEach(item -> {
                        List<DatasyncJob> datasyncJobs1 = collectByDispatchId.get(item.getJobId() + "");
                        if (!CollectionUtils.isEmpty(datasyncJobs1)) {
                            item.setJobName(datasyncJobs1.get(0).getJobName());
                        } else {
                            item.setJobName("jobId:" + item.getJobId() + "，对应的jobName不存在");
                        }
                    });
                    // result.setData(jobLogPageVos);
                }
            }
        }
        result.setData(jobLogPageVos);
        return result;
    }


    public Page<XxlJobLogWithInstanceVO> pageXxlJobLogWithInstance(JobLogPageListDto jobLogPageListDto, int page, int rows) {
        return schedulerRepository.pageDatasyncLog(jobLogPageListDto, page, rows);
    }


    public List<XxlJobGroup> jobGroupList() {
        return schedulerRepository.findAllJobGroup();
    }

    public List<XxlJobInfo> getJobsByGroup(int jobGroup) {
        return schedulerRepository.getJobsByGroup(jobGroup);
    }

    public Boolean clearLog(int jobGroup, int jobId, int type) {
        return schedulerRepository.clearLog(jobGroup, jobId, type);
    }

    public Boolean logKill(long jobId) {
        return schedulerRepository.logKill(jobId);
    }

    public LogResult logDetailCat(long logId, int fromLineNum) {
        return schedulerRepository.logDetailCat(logId, fromLineNum);
    }



    /**
     * 构建实例编码
     * @param datasyncId 同步定义表ID
     * @return 实例编码
     */
    public String buildJobCode(String datasyncId) {
        String code = CodeGenerator.getRandomStr(6);
        int max = 0;
        while (max < 3) {
            if (!existJobCode(datasyncId, code)) {
                return code;
            }
            max ++;
        }
        throw new BusinessException("生产实例编码失败，该实例编码已存在");
    }

    /**
     * 判断该实例编码是否存在
     * @param datasyncId 同步定义表ID
     * @param code 实例编码
     * @return true or false
     */
    public boolean existJobCode(String datasyncId, String code) {
        // DatasyncInstanceRepository.existInstanceCode
        List<DatasyncJob> list = this.list(new QueryWrapper<DatasyncJob>()
                .eq("datasync_id", datasyncId)
                .eq("deleted", YNEnum.NO.getIntCharCode())
                // TODO: 之前是有job编码的，现在去除了，待确认
                .eq("job_name", code)
        );
        return !CollectionUtils.isEmpty(list);
    }

    /**
     * 根据数据同步定义表ID获取同步任务表数据
     * @param datasyncId 同步定义表ID
     * @return 同步任务表数据
     */
    public List<DatasyncJob> listByParentId(String datasyncId) {
        // DatasyncInstanceRepository.listByParentId
        if (StringUtils.isBlank(datasyncId)) {
            throw new IllegalArgumentException("datasyncId can not be null");
        }
        return this.list(
                new QueryWrapper<DatasyncJob>()
                        .eq("datasync_id", datasyncId)
                        .eq("deleted", YNEnum.NO.getIntCharCode())
                        .orderByDesc("update_time")
        );


    }

    /**
     * 删除记录
     * @param datasyncId 同步定义表ID[动画表情]
     */
    public void removeByParentId(String datasyncId) {
        if (StringUtils.isBlank(datasyncId)) {
            throw new IllegalArgumentException("datasyncId can not be null");
        }
        // 根据 datasync_id 找到 所有的job 然后删除
        this.remove(new QueryWrapper<DatasyncJob>().eq("datasync_id", datasyncId));
    }

    /**
     * 根据 scheduleId 获取记录
     * @param scheduleId XXL-JOB表记录ID
     * @return 同步定义表记录
     */
    public DatasyncJob getByScheduleId(String scheduleId) {
        if (StringUtils.isBlank(scheduleId)) {
            throw new IllegalArgumentException("scheduleId can not be null");
        }
        return this.getOne(
                new QueryWrapper<DatasyncJob>()
                        .eq("schedule_id", scheduleId)
                        .eq("deleted", YNEnum.NO.getIntCharCode()));
    }

}
