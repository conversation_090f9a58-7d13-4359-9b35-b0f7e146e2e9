/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：4/24/23
 */
package com.xmcares.platform.admin.common.expression;

import java.util.Map;

/**
 * 伪函数
 * <AUTHOR>
 * @since 1.4.1
 */
public interface PseudoFunction {

    /**
     * 函数名
     * @return 函数名
     */
    String getName();

    /**
     * 执行
     * @param environment 环境变量
     * @param args 参数组
     * @return 函数执行结果
     */
    String execute(Map<String, Object> environment, String ... args);


}
