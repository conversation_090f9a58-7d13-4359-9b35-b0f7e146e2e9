/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/3/3
 */
package com.xmcares.platform.admin.integrator.datasync.repository.xxljob;

import com.xmcares.platform.admin.common.vo.ReturnT;
import com.xmcares.platform.admin.integrator.common.config.XxljobClientFeignConfiguration;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobInfo;
import com.xxl.job.core.biz.model.LogResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 1.1.0
 */
@FeignClient(name = "${xbdp.feign.scheduler-service.name:scheduler-service}",
        url = "${xbdp.feign.scheduler-service.url:}",
        // 指定配置类，用于添加XXL-JOB 鉴权 Header
        configuration = XxljobClientFeignConfiguration.class
)
public interface XxljobClient {

//    @GetMapping(value = "/xbdp/job/findJobGroup")
//    ReturnT<XxlJobGroup> findJobGroup(@RequestParam("name") String name);

//    @PostMapping(value = "/xbdp/job/add", consumes = {MediaType.APPLICATION_JSON_VALUE})
//    ReturnT<String> addXxljob(XxlJobInfo xxlJobInfo);

//    @PostMapping(value = "/xbdp/job/update", consumes = {MediaType.APPLICATION_JSON_VALUE})
//    public ReturnT<String> update(XxlJobInfo jobInfo);

//    @PostMapping(value = "/xbdp/job/list-query", consumes = {MediaType.APPLICATION_JSON_VALUE})
//    public ReturnT<List<XxlJobInfo>> queryList(List<String> ids);

//    @GetMapping(value = "/xbdp/job/remove")
//    ReturnT<String> removeXxlJob(@RequestParam(name = "id") String jobId);

//    @GetMapping(value = "/xbdp/job/stop", consumes = {MediaType.APPLICATION_JSON_VALUE})
//    public ReturnT<String> pause(@RequestParam("id") String jobId);
//
//    @GetMapping(value = "/xbdp/job/start", consumes = {MediaType.APPLICATION_JSON_VALUE})
//    public ReturnT<String> start(@RequestParam("id") String jobId);

//    @GetMapping(value = "/xbdp/job/findOne", consumes = {MediaType.APPLICATION_JSON_VALUE})
//    ReturnT<XxlJobInfo> findJob(@RequestParam("id") String dispatchId);

//    @PostMapping(value = "/xbdp/job/trigger", consumes = {MediaType.APPLICATION_JSON_VALUE})
//    public ReturnT<String> triggerJob(@RequestBody XxlJobInfo xxlJobInfo);

    // XOTP new scheduler. 基于官方推荐的扩展api方式实现，只作扩展，不修改源码，需要根据返回的类型自己解析
    // ============ start ============

    @PostMapping(value = "/extend-api/loadJobInfo", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<String> loadJobInfoById(int id);

    @PostMapping(value = "/extend-api/loadJobGroupInfo", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<String> loadXxlJobGroupById(int id);

    @PostMapping(value = "/extend-api/addJobInfo", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<String> addJobInfo(@RequestBody XxlJobInfo xxlJobInfo);

    @PostMapping(value = "/extend-api/updateJob", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<String> updateJob(@RequestBody XxlJobInfo jobInfo);

    @PostMapping(value = "/jobinfo/remove", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<String> removeJob(@RequestParam("id") int jobId);

    @PostMapping(value = "/extend-api/triggerJob", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<String> triggerJob(@RequestBody XxlJobInfo xxlJobInfo);

    @PostMapping(value = "/jobinfo/start", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<String> startJob(@RequestParam("id") int jobId);

    @PostMapping(value = "/jobinfo/stop", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<String> endJob(@RequestParam("id") int jobId);

    @PostMapping(value = "/jobinfo/nextTriggerTime", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<List<String>> nextTriggerTime(@RequestParam("scheduleType") String scheduleType, @RequestParam("scheduleConf") String scheduleConf);

    // job log调用
    @PostMapping(value = "/joblog/pageList", consumes = {MediaType.APPLICATION_JSON_VALUE})
    Map<String, Object> jobLogPageList(@RequestParam(value = "start", required = false, defaultValue = "0") int start,
                                       @RequestParam(value = "length", required = false, defaultValue = "10") int length,
                                       @RequestParam("jobGroup") int jobGroup,
                                       @RequestParam("jobId") int jobId,
                                       @RequestParam("logStatus") int logStatus,
                                       @RequestParam("filterTime") String filterTime);


    @PostMapping(value = "/extend-api/findAllJobGroup", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<String> findAllJobGroup();

    @PostMapping(value = "/joblog/getJobsByGroup", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<List<XxlJobInfo>> getJobsByGroup(@RequestParam("jobGroup") int jobGroup);

    @PostMapping(value = "/joblog/clearLog", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<String> clearLog(@RequestParam("jobGroup") int jobGroup, @RequestParam("jobId") int jobId, @RequestParam("type") int type);

    @PostMapping(value = "/joblog/logDetailCat", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<LogResult> logDetailCat(@RequestParam("logId") long logId, @RequestParam("fromLineNum") int fromLineNum);

    @PostMapping(value = "/joblog/logKill", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<String> logKill(@RequestParam("id") long id);

    @PostMapping(value = "/extend-api/datasyncLog", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<String> datasyncLog(long instanceId);

    @PostMapping(value = "/extend-api/pageDatasyncLog", consumes = {MediaType.APPLICATION_JSON_VALUE})
    ReturnT<String> pageDatasyncLog(@RequestBody Map<String, Object> pageRequest);

    // ============ end ============



}
