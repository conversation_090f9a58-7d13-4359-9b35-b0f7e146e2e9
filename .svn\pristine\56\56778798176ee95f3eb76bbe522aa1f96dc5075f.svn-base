package com.xmcares.platform.admin.integrator.datasync.service;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.integrator.common.error.IntegratorException;
import com.xmcares.platform.admin.integrator.common.util.IntegratorMod;
import com.xmcares.platform.admin.integrator.datasync.dto.DatasyncDto;
import com.xmcares.platform.admin.integrator.datasync.model.Datasync;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncModel;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncInstance;
import com.xmcares.platform.admin.integrator.datasync.model.SchedulerJob;
import com.xmcares.platform.admin.integrator.datasync.repository.*;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobInfo;
import com.xmcares.platform.admin.integrator.datasync.task.IRemoveDataService;
import com.xmcares.platform.admin.integrator.datasync.vo.DataxTempVo;
import com.xmcares.platform.admin.integrator.datasync.vo.QueryDatasync;
import com.xmcares.platform.admin.integrator.datasync.vo.SaveDatasync;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/2/23 09:59
 */
@PropertySource(value = "classpath:application.properties", encoding = "utf-8")
@Component
public class DatasyncService implements IRemoveDataService<Datasync> {

    private static final Logger LOG = LoggerFactory.getLogger(DatasyncService.class);

    private static final int DATASYNC_RUNNING = 1;

    private static final String INTEGRATION_WAY_PLUGIN = "1"; //0:内置 1:插件

    private static final String INTEGRATION_TYPE_READ = "0"; //读
    private static final String INTEGRATION_TYPE_WRITE = "1"; //写

    @Autowired
    private DatasyncRepository datasyncRepository;

    @Autowired
    private DatasyncInstanceRepository datasyncInstanceRepository;

//    @Autowired
//    private DatasyncJobService datasyncJobService;

    @Autowired
    private SchedulerRepository schedulerRepository;

//    @Autowired
//    private DataxFileRepository dataxFileRepository;

    @Autowired
    private SeaTunnelConfigRepository seaTunnelConfigRepository;

    private static final Set<String> PUBLISH_ASYNC_LOCK = new HashSet<>();
    private static final Object SAVE_LOCK = new Object();
    private static final Object PUSH_LOCK = new Object();

    /**
     * 获取分页列表信息
     * @param queryInfo 查询条件
     * @param pagination 分页信息
     * @return 分页列表信息
     */
    public Page<Datasync> findByPage(QueryDatasync queryInfo, Pagination pagination) {
        Page<Datasync> queryResult = datasyncRepository.queryPage(queryInfo, new Page<>(pagination.getPageNo()-1, pagination.getPageSize()));
        Page<Datasync> page = new Page<>();
        page.setData(Optional.of(queryResult.getData()).orElse(new ArrayList<>()).stream().peek(Datasync::resetPageListInfo).collect(Collectors.toList()));
        page.setTotal(queryResult.getTotal());
        page.setPageNo(pagination.getPageNo());
        page.setPageSize(pagination.getPageSize());
        return page;
    }

    /**
     * 使用分页获取需要删除的记录
     * @param pagination 跟也信息
     * @return 结果
     */
    @Override
    public Page<Datasync> findNeedDeletePage(Pagination pagination) {
        return datasyncRepository.queryNeedDeletePage(new Page<>(pagination.getPageNo() - 1, pagination.getPageSize()));
    }

    /**
     * 获取同步信息，再从调度服务中获取调度信息
     * @param id 同步定义ID
     * @return 同步定义信息
     */
    public Datasync get(String id) {
        return this.checkExit(id);
    }

    /**
     * 添加数据同步信息
     * @param saveDatasync 需要保存的信息
     */
    public void add(SaveDatasync saveDatasync) {
        // 1. 构建完整的SaveDataSync信息
        buildAllSaveDatasync(saveDatasync);
        synchronized (SAVE_LOCK) {
            if (datasyncRepository.existName(saveDatasync.getIntgName())) {
                throw new IntegratorException("已存在相同名称的任务");
            }
            // 2. 新增同步定义信息
            datasyncRepository.addDatasync(Datasync.createNewFrom(saveDatasync));
        }
    }

    /**
     * 修改数据同步信息
     * @param saveDatasync 修改的信息
     */
    public void update(SaveDatasync saveDatasync) {
        Datasync datasync = this.checkExit(saveDatasync.getId());
        saveDatasync.setOrginDatasourceName(datasync.getOrginDatasourceName());
        saveDatasync.setDestDatasourceName(datasync.getDestDatasourceName());
        this.buildAllSaveDatasync(saveDatasync);
        if(datasync.getIntgName().equals(saveDatasync.getIntgName())) {
            //直接更新
            datasyncRepository.updateDatasync(Datasync.updateFrom(saveDatasync));
        }else {
            synchronized (SAVE_LOCK) {
                //先判重
                if (datasyncRepository.existName(saveDatasync.getIntgName())) {
                    throw new IntegratorException("已存在相同名称的任务");
                }
                datasyncRepository.updateDatasync(Datasync.updateFrom(saveDatasync));
            }
        }
    }

    /**
     * 发布同步任务
     * @param id 同步信息定义ID
     * @param instanceName 实例名称
     */
    public void publish(String id, String instanceName) {
        synchronized (PUSH_LOCK) {
            if (PUBLISH_ASYNC_LOCK.contains(id)) {
                throw new BusinessException("该任务目前正在发布中， 请勿多次操作");
            }
            PUBLISH_ASYNC_LOCK.add(id);
        }
        long beginTimer = System.currentTimeMillis();
        try {
            // 1. 校验需要发布的记录是否存在
            DatasyncDto datasync = new DatasyncDto(this.checkExit(id));
            // 2. 生成实例编码
            String code = datasyncInstanceRepository.buildInstanceCode(datasync.getId());
            // 3. 生成文件路径
            String filePath = seaTunnelConfigRepository.buildJobConfigPath(id, code);
            // 4. TODO: 生成模板内容
            DataxTempVo context = seaTunnelConfigRepository.reader(datasync);
            // 5. 创建任务对象信息
            DatasyncInstance datasyncTask = DatasyncInstance.newDatasyncInstance(instanceName, code, context, filePath, datasync);
            // TODO: 去除重复性校验
            // 6. 校验模板是否存在重复
//            DatasyncInstance findResult = datasyncInstanceRepository.findExistTemplate(datasync.getId(), datasyncTask.getTemplateCode());
//            if (findResult != null) {
//                throw new BusinessException("该模板与已存在的【" + findResult.getInstanceName() + "::" + findResult.getTemplateCode() + "】重复");
//            }
            // 7. 添加任务
            datasync.setInstanceName(instanceName);
            String datasyncId = SnowflakeGenerator.getNextId() + "";
            XxlJobInfo dispatchId = schedulerRepository.addScheduler(datasync, datasyncId, filePath, context);
            // 用于datax 根据该任务ID查找模板，不再从ftp上获取
            datasyncTask.setId(datasyncId);

            // 设置 XXL-JOB相关信息
            datasyncTask.setDispatchId(dispatchId.getId() + "");
            datasyncTask.setScheduleConf(dispatchId.getScheduleConf());
            datasyncTask.setExecutorRouteStrategy(dispatchId.getExecutorRouteStrategy());
            datasyncTask.setExecutorBlockStrategy(dispatchId.getExecutorBlockStrategy());
            datasyncTask.setExecutorTimeout(dispatchId.getExecutorTimeout());
            datasyncTask.setExecutorFailRetryCount(dispatchId.getExecutorFailRetryCount());
            datasyncTask.setJobGroup(dispatchId.getJobGroup());
            datasyncTask.setTriggerStatus(dispatchId.getTriggerStatus());
            datasyncTask.setScheduleType(dispatchId.getScheduleType());
            datasyncTask.setMisfireStrategy(dispatchId.getMisfireStrategy());
            datasyncTask.setExecutorHandler(dispatchId.getExecutorHandler());
            datasyncTask.setExecutorParam(dispatchId.getExecutorParam());
            datasyncTask.setChildJobid(dispatchId.getChildJobId());

            // 8. 新增发布信息
            if (!datasyncInstanceRepository.save(datasyncTask)) {
                try {
                    schedulerRepository.removeScheduler(dispatchId.getId() + "");
                } catch (Exception e) {
                    LOG.error("新增发布信息异常，并且移除调度任务失败！", e);
                }
                throw new BusinessException("任务发布失败");
            }
            // 9. 写入运行日志
            LOG.info("任务【{}】【{}】发布成功，本次任务发布耗时：{}", datasyncTask.getId(), instanceName, (System.currentTimeMillis() - beginTimer));

            //10. 去除 huzk 上傳一份文件到服務器  linbk  2024.10.18
            // dataxFileRepository.upload(datasyncTask.getFilePath(), datasyncTask.getTemplateContext());
        } finally {
            synchronized (PUSH_LOCK) {
                PUBLISH_ASYNC_LOCK.remove(id);
            }
        }
    }

    /**
     * 将状态变更成已经删除
     * @param id 同步信息定义ID
     */
    public Boolean transitionToDelete(String id) {
        // 1. 验证运行状态
        checkTaskRunStatus(id);
        // 2. 执行假删除操作
        datasyncRepository.changeHasDelete(id, YNEnum.YES.getIntCharCode());
        return true;
    }

    /**
     * 读取Json信息
     * @param datasync 同步定义信息
     * @return JSON信息
     */
    public String readerJsonInfo(Datasync datasync) {
        return seaTunnelConfigRepository.reader(new DatasyncDto(datasync)).getContext();
    }

    /**
     * 真实移除同步信息定义
     * @param id 同步信息定义ID
     * @return 移除是否成功
     */
    @Override
    public Boolean removeData(String id) {
        List<DatasyncInstance> findResult = datasyncInstanceRepository.listByParentId(id);
        if (!findResult.isEmpty()) {
            for (DatasyncInstance datasyncTask : findResult) {
                removeSchedulerAndFile(datasyncTask);
            }
        }
        datasyncInstanceRepository.removeByParentId(id);
        datasyncRepository.removeDatasync(id);
        return true;
    }

    /**
     * 根据同步信息定义的ID校验子任务列表中是否存在正在运行中的任务
     * @param id 同步信息定义的ID
     */
    private void checkTaskRunStatus(String id) {
        List<DatasyncInstance> findResult = datasyncInstanceRepository.listByParentId(id);
        if (findResult.isEmpty()) {return;}
        List<String> dispatchIds = findResult.stream().map(DatasyncInstance::getDispatchId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        Map<String, SchedulerJob> schedulerJobMap = schedulerRepository.querySchedulerJobs(dispatchIds);
        if (null != schedulerJobMap && !schedulerJobMap.isEmpty()) {
            for (SchedulerJob value : schedulerJobMap.values()) {
                if(value.getTriggerStatus() == DATASYNC_RUNNING) {
                    throw new IntegratorException("数据同步运行中，不允许修改或删除");
                }
            }
        }
    }

    /**
     * 删除同步实例对应的调度信息与配置文件
     * @param datasyncTask 同步实例信息
     */
    private void removeSchedulerAndFile(DatasyncInstance datasyncTask) {
        if (StringUtils.isNotEmpty(datasyncTask.getDispatchId())) {
            try {
                schedulerRepository.removeScheduler(datasyncTask.getDispatchId());
            } catch (Exception e) {
                LOG.error("在删除调度记录【" + datasyncTask.getDispatchId() + "::" + datasyncTask.getInstanceName() + "】的过程中遇到异常", e);
            }
        }
        if (StringUtils.isNotEmpty(datasyncTask.getFilePath())) {
            seaTunnelConfigRepository.removeFile(datasyncTask.getFilePath());
        }
    }

    public Boolean isUsed(String datasourceId) {
        return datasyncRepository.isUsed(datasourceId);
    }

    private Datasync checkExit(String id) {
        Datasync datasync = datasyncRepository.get(id);
        if(datasync==null) {
            throw new IntegratorException("数据同步不存在");
        }
        return datasync;
    }

    /**
     * 设置数据集成模型id、设置运行模式(读为主)、插件地址
     * @param saveDatasync 需要构建的信息
     */
    private void buildAllSaveDatasync(SaveDatasync saveDatasync) {
        IntegratorMod fromMod = saveDatasync.getFromMod();
        IntegratorMod toMod = saveDatasync.getToMod();
        Assert.notNull(fromMod, "数据来源模式不正确");
        Assert.notNull(toMod, "数据去向模式不正确");
        // 1. 设置数据来源相关信息
        DatasyncModel orginDataModel;
        if(fromMod == IntegratorMod.SYS) {
            // 原有feign调用方式改成服务
            // orginDataModel = datasourceClient.getDatasyncModel(saveDatasync.getOrginDatasourceId(), INTEGRATION_TYPE_READ);
            orginDataModel = datasyncRepository.getDatasyncModel(saveDatasync.getOrginDatasourceId(), INTEGRATION_TYPE_READ);
        }else{
            // 原有feign调用方式改成服务
            // orginDataModel = datasourceClient.getDatasyncModel(saveDatasync.getOrginDatasourceId());
            orginDataModel = datasyncRepository.getDatasyncModel(saveDatasync.getOrginDatasourceId(), null);
            saveDatasync.setOrginPluginPath(orginDataModel.getPluginPath());
            saveDatasync.setOrginPluginName(orginDataModel.getPluginName());
        }
        saveDatasync.setOrginIntgModelId(orginDataModel.getId());
        saveDatasync.setOrginRunSchema(orginDataModel.getRunSchema());
        // 2. 设置数据去向相关信息
        DatasyncModel destDataModel;
        if(toMod == IntegratorMod.SYS) {
            // 原有feign调用方式改成服务
            // destDataModel = datasourceClient.getDatasyncModel(saveDatasync.getDestDatasourceId(), INTEGRATION_TYPE_WRITE);
            destDataModel = datasyncRepository.getDatasyncModel(saveDatasync.getDestDatasourceId(), INTEGRATION_TYPE_WRITE);
        }else{
            // 原有feign调用方式改成服务
            // destDataModel = datasourceClient.getDatasyncModel(saveDatasync.getDestDatasourceId());
            destDataModel = datasyncRepository.getDatasyncModel(saveDatasync.getDestDatasourceId(), null);
            saveDatasync.setDestPluginPath(destDataModel.getPluginPath());
            saveDatasync.setDestPluginName(destDataModel.getPluginName());
        }
        saveDatasync.setDestIntgModelId(destDataModel.getId());

    }






}
