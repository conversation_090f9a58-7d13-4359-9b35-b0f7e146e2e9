package com.xmcares.platform.admin.integrator.datasync.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.platform.admin.integrator.datasync.dto.JobLogPageListDto;
import com.xmcares.platform.admin.integrator.datasync.dto.JobLogPageVo;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncInstance;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncJob;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobGroup;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobInfo;
import com.xmcares.platform.admin.integrator.datasync.service.DatasyncJobService;
import com.xmcares.platform.admin.integrator.datasync.vo.DisplayDataSyncTask;
import com.xmcares.platform.admin.integrator.datasync.vo.UpdateDatasyncTask;
import com.xxl.job.core.biz.model.LogResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/17 9:08
 */
@Validated
@RestController
@RequestMapping("/datasync-job/instance")
public class DatasyncJobController {

//    @Autowired
//    private DatasyncInstanceService datasyncInstanceService;

    @Autowired
    private DatasyncJobService datasyncJobService;


    @GetMapping("/pid-list")
    @ResponseBody
    public List<DisplayDataSyncTask> list(@RequestParam(name = "pid") String pid) {
        return datasyncJobService.findByParentId(pid);
    }

    @GetMapping("/list")
    @ResponseBody
    public List<DisplayDataSyncTask> list() {
        return datasyncJobService.listDisplayDatasyncTask();
    }


    @GetMapping("/get")
    public DisplayDataSyncTask get(@RequestParam(name = "id") String id) {
        return datasyncJobService.get(id);
    }

    @GetMapping("/begin")
    @ResponseBody
    public Boolean begin(@RequestParam(name = "id") String id) {
        datasyncJobService.begin(id);
        return true;
    }

    @GetMapping("/stop")
    @ResponseBody
    public Boolean stop(@RequestParam(name = "id") String id) {
        datasyncJobService.end(id);
        return true;
    }

    @GetMapping("/trigger")
    @ResponseBody
    public Boolean triggerScheduler(@RequestParam(name = "id") String id) {
        datasyncJobService.triggerScheduler(id);
        return true;
    }

    @GetMapping("/delete")
    @ResponseBody
    public Boolean remove(@RequestParam(name = "id") String id) {
        return datasyncJobService.transitionToDelete(id);
    }

    @PostMapping("/update")
    @ResponseBody
    public Boolean update(@RequestBody @Validated UpdateDatasyncTask datasyncTask) {
        datasyncJobService.update(datasyncTask);
        return true;
    }

    @GetMapping("/displayJson")
    @ResponseBody
    public String displayJson(@RequestParam(name = "id") String id) {
        DatasyncJob datasync = datasyncJobService.checkExit(id);
        return datasync.getJobOptions();
    }

    @GetMapping("/nextTriggerTime")
    @ResponseBody
    public List<String> nextTriggerTime(@RequestParam(name = "scheduleType") String scheduleType, @RequestParam(name = "scheduleConf") String scheduleConf) {
        return datasyncJobService.nextTriggerTime(scheduleType, scheduleConf);
    }

    @GetMapping("/jobLog/clearLog")
    @ResponseBody
    public Boolean clearLog(@RequestParam("jobGroup") int jobGroup, @RequestParam("jobId") int jobId, @RequestParam("type") int type) {
        return datasyncJobService.clearLog(jobGroup, jobId, type);
    }

    @GetMapping("/jobLog/logKill")
    @ResponseBody
    public Boolean logKill(@RequestParam("id") long id) {
        return datasyncJobService.logKill(id);
    }


    @GetMapping("/jobLog/logDetailCat")
    @ResponseBody
    public LogResult logDetailCat(@RequestParam("logId") long logId, @RequestParam("fromLineNum") int fromLineNum) {
        return datasyncJobService.logDetailCat(logId, fromLineNum);
    }

    @GetMapping("/jobLog/getJobsByGroup")
    @ResponseBody
    public List<XxlJobInfo> getJobsByGroup(@RequestParam(name = "jobGroup") int jobGroup) {
        return datasyncJobService.getJobsByGroup(jobGroup);
    }

    @GetMapping("/jobLog/jobGroupList")
    @ResponseBody
    public List<XxlJobGroup> jobGroupList() {
        return datasyncJobService.jobGroupList();
    }

    @GetMapping("/jobLog/page-query")
    @ResponseBody
    public Page<JobLogPageVo> jobLogPageList(HttpServletRequest request,
                                             @RequestParam(required = false, defaultValue = "1") Integer page,
                                             @RequestParam(required = false, defaultValue = "10") Integer rows,
                                             Integer jobGroup, @RequestParam(required = false) Integer jobId, Integer logStatus, String filterTime, String jobName) {

        JobLogPageListDto jobLogPageListDto = new JobLogPageListDto();
        int start = (page - 1) * rows;
        int length = rows;
        jobLogPageListDto.setStart(start);
        jobLogPageListDto.setLength(length);
        jobLogPageListDto.setJobGroup(jobGroup);
        jobLogPageListDto.setJobId(null != jobId ? jobId : 0);
        jobLogPageListDto.setLogStatus(logStatus);
        jobLogPageListDto.setFilterTime(filterTime);
//        jobLogPageListDto.setJobName(jobName);
        return datasyncJobService.jobLogPageList(jobLogPageListDto, page, rows);
    }



}
