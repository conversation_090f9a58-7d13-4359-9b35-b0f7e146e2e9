<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="xotp@*************">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.53">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||mysql.session|localhost|ALTER|G
|root||root||ALTER|G
|root||test||ALTER|G
|root||user_replica||ALTER|G
|root||mysql.session|localhost|ALTER ROUTINE|G
|root||root||ALTER ROUTINE|G
|root||test||ALTER ROUTINE|G
|root||user_replica||ALTER ROUTINE|G
|root||mysql.session|localhost|CREATE|G
|root||root||CREATE|G
|root||test||CREATE|G
|root||user_replica||CREATE|G
|root||mysql.session|localhost|CREATE ROUTINE|G
|root||root||CREATE ROUTINE|G
|root||test||CREATE ROUTINE|G
|root||user_replica||CREATE ROUTINE|G
|root||root||CREATE TABLESPACE|G
|root||user_replica||CREATE TABLESPACE|G
|root||mysql.session|localhost|CREATE TEMPORARY TABLES|G
|root||root||CREATE TEMPORARY TABLES|G
|root||test||CREATE TEMPORARY TABLES|G
|root||user_replica||CREATE TEMPORARY TABLES|G
|root||mysql.session|localhost|CREATE USER|G
|root||root||CREATE USER|G
|root||test||CREATE USER|G
|root||user_replica||CREATE USER|G
|root||mysql.session|localhost|CREATE VIEW|G
|root||root||CREATE VIEW|G
|root||test||CREATE VIEW|G
|root||user_replica||CREATE VIEW|G
|root||mysql.session|localhost|DELETE|G
|root||root||DELETE|G
|root||test||DELETE|G
|root||user_replica||DELETE|G
|root||root||DROP|G
|root||test||DROP|G
|root||user_replica||DROP|G
|root||mysql.session|localhost|EVENT|G
|root||root||EVENT|G
|root||test||EVENT|G
|root||user_replica||EVENT|G
|root||mysql.session|localhost|EXECUTE|G
|root||root||EXECUTE|G
|root||test||EXECUTE|G
|root||user_replica||EXECUTE|G
|root||mysql.session|localhost|FILE|G
|root||root||FILE|G
|root||test||FILE|G
|root||user_replica||FILE|G
|root||mysql.session|localhost|INDEX|G
|root||root||INDEX|G
|root||test||INDEX|G
|root||user_replica||INDEX|G
|root||mysql.session|localhost|INSERT|G
|root||root||INSERT|G
|root||test||INSERT|G
|root||user_replica||INSERT|G
|root||mysql.session|localhost|LOCK TABLES|G
|root||root||LOCK TABLES|G
|root||test||LOCK TABLES|G
|root||user_replica||LOCK TABLES|G
|root||mysql.session|localhost|PROCESS|G
|root||root||PROCESS|G
|root||test||PROCESS|G
|root||user_replica||PROCESS|G
|root||mysql.session|localhost|REFERENCES|G
|root||root||REFERENCES|G
|root||test||REFERENCES|G
|root||user_replica||REFERENCES|G
|root||mysql.session|localhost|RELOAD|G
|root||root||RELOAD|G
|root||test||RELOAD|G
|root||user_replica||RELOAD|G
|root||mysql.session|localhost|REPLICATION CLIENT|G
|root||root||REPLICATION CLIENT|G
|root||test||REPLICATION CLIENT|G
|root||user_replica||REPLICATION CLIENT|G
|root||mysql.session|localhost|REPLICATION SLAVE|G
|root||root||REPLICATION SLAVE|G
|root||test||REPLICATION SLAVE|G
|root||user_replica||REPLICATION SLAVE|G
|root||mysql.session|localhost|SELECT|G
|root||root||SELECT|G
|root||test||SELECT|G
|root||user_replica||SELECT|G
|root||mysql.session|localhost|SHOW DATABASES|G
|root||root||SHOW DATABASES|G
|root||test||SHOW DATABASES|G
|root||user_replica||SHOW DATABASES|G
|root||mysql.session|localhost|SHOW VIEW|G
|root||root||SHOW VIEW|G
|root||test||SHOW VIEW|G
|root||user_replica||SHOW VIEW|G
|root||mysql.session|localhost|SHUTDOWN|G
|root||root||SHUTDOWN|G
|root||test||SHUTDOWN|G
|root||user_replica||SHUTDOWN|G
|root||mysql.session|localhost|SUPER|G
|root||root||SUPER|G
|root||test||SUPER|G
|root||user_replica||SUPER|G
|root||mysql.session|localhost|TRIGGER|G
|root||root||TRIGGER|G
|root||test||TRIGGER|G
|root||user_replica||TRIGGER|G
|root||mysql.session|localhost|UPDATE|G
|root||root||UPDATE|G
|root||test||UPDATE|G
|root||user_replica||UPDATE|G
|root||mysql.session|localhost|grant option|G
|root||root||grant option|G
|root||test||grant option|G
dolphinscheduler|schema||dolphinscheduler||ALTER|G
dolphinscheduler|schema||dolphinscheduler||ALTER ROUTINE|G
dolphinscheduler|schema||dolphinscheduler||CREATE|G
dolphinscheduler|schema||dolphinscheduler||CREATE ROUTINE|G
dolphinscheduler|schema||dolphinscheduler||CREATE TEMPORARY TABLES|G
dolphinscheduler|schema||dolphinscheduler||CREATE VIEW|G
dolphinscheduler|schema||dolphinscheduler||DELETE|G
dolphinscheduler|schema||dolphinscheduler||DROP|G
dolphinscheduler|schema||dolphinscheduler||EVENT|G
dolphinscheduler|schema||dolphinscheduler||EXECUTE|G
dolphinscheduler|schema||dolphinscheduler||INDEX|G
dolphinscheduler|schema||dolphinscheduler||INSERT|G
dolphinscheduler|schema||dolphinscheduler||LOCK TABLES|G
dolphinscheduler|schema||dolphinscheduler||REFERENCES|G
dolphinscheduler|schema||dolphinscheduler||SELECT|G
dolphinscheduler|schema||dolphinscheduler||SHOW VIEW|G
dolphinscheduler|schema||dolphinscheduler||TRIGGER|G
dolphinscheduler|schema||dolphinscheduler||UPDATE|G
dolphinscheduler|schema||dolphinscheduler||grant option|G
performance_schema|schema||mysql.session|localhost|SELECT|G
sys|schema||mysql.sys|localhost|TRIGGER|G
xcf2|schema||user_sys||ALTER|G
xcf2|schema||user_sys|localhost|ALTER|G
xcf2|schema||user_sys||ALTER ROUTINE|G
xcf2|schema||user_sys|localhost|ALTER ROUTINE|G
xcf2|schema||user_sys||CREATE|G
xcf2|schema||user_sys|localhost|CREATE|G
xcf2|schema||user_sys||CREATE ROUTINE|G
xcf2|schema||user_sys|localhost|CREATE ROUTINE|G
xcf2|schema||user_sys||CREATE TEMPORARY TABLES|G
xcf2|schema||user_sys|localhost|CREATE TEMPORARY TABLES|G
xcf2|schema||user_sys||CREATE VIEW|G
xcf2|schema||user_sys|localhost|CREATE VIEW|G
xcf2|schema||user_sys||DELETE|G
xcf2|schema||user_sys|localhost|DELETE|G
xcf2|schema||user_sys||DROP|G
xcf2|schema||user_sys|localhost|DROP|G
xcf2|schema||user_sys||EVENT|G
xcf2|schema||user_sys|localhost|EVENT|G
xcf2|schema||user_sys||EXECUTE|G
xcf2|schema||user_sys|localhost|EXECUTE|G
xcf2|schema||user_sys||INDEX|G
xcf2|schema||user_sys|localhost|INDEX|G
xcf2|schema||user_sys||INSERT|G
xcf2|schema||user_sys|localhost|INSERT|G
xcf2|schema||user_sys||LOCK TABLES|G
xcf2|schema||user_sys|localhost|LOCK TABLES|G
xcf2|schema||user_sys||REFERENCES|G
xcf2|schema||user_sys|localhost|REFERENCES|G
xcf2|schema||user_sys||SELECT|G
xcf2|schema||user_sys|localhost|SELECT|G
xcf2|schema||user_sys||SHOW VIEW|G
xcf2|schema||user_sys|localhost|SHOW VIEW|G
xcf2|schema||user_sys||TRIGGER|G
xcf2|schema||user_sys|localhost|TRIGGER|G
xcf2|schema||user_sys||UPDATE|G
xcf2|schema||user_sys|localhost|UPDATE|G
xcf3|schema||user_sys||ALTER|G
xcf3|schema||user_sys||ALTER ROUTINE|G
xcf3|schema||user_sys||CREATE|G
xcf3|schema||user_sys||CREATE ROUTINE|G
xcf3|schema||user_sys||CREATE TEMPORARY TABLES|G
xcf3|schema||user_sys||CREATE VIEW|G
xcf3|schema||user_sys||DELETE|G
xcf3|schema||user_sys||DROP|G
xcf3|schema||user_sys||EVENT|G
xcf3|schema||user_sys||EXECUTE|G
xcf3|schema||user_sys||INDEX|G
xcf3|schema||user_sys||INSERT|G
xcf3|schema||user_sys||LOCK TABLES|G
xcf3|schema||user_sys||REFERENCES|G
xcf3|schema||user_sys||SELECT|G
xcf3|schema||user_sys||SHOW VIEW|G
xcf3|schema||user_sys||TRIGGER|G
xcf3|schema||user_sys||UPDATE|G</Grants>
      <ServerVersion>5.7.32</ServerVersion>
    </root>
    <collation id="2" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="4" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="6" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="8" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="10" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="11" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="12" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="13" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="14" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="15" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="16" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="17" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="18" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="19" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="20" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="21" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="22" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="23" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="24" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="25" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="27" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="29" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="31" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="33" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="35" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="39" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="45" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="47" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="48" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="49" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="50" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="52" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="54" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="56" parent="1" name="utf8_general_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="utf8_bin">
      <Charset>utf8</Charset>
    </collation>
    <collation id="58" parent="1" name="utf8_unicode_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="59" parent="1" name="utf8_icelandic_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="60" parent="1" name="utf8_latvian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="61" parent="1" name="utf8_romanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="62" parent="1" name="utf8_slovenian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="63" parent="1" name="utf8_polish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="64" parent="1" name="utf8_estonian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="65" parent="1" name="utf8_spanish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="66" parent="1" name="utf8_swedish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="67" parent="1" name="utf8_turkish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="68" parent="1" name="utf8_czech_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="69" parent="1" name="utf8_danish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="70" parent="1" name="utf8_lithuanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="71" parent="1" name="utf8_slovak_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="72" parent="1" name="utf8_spanish2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="73" parent="1" name="utf8_roman_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="74" parent="1" name="utf8_persian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="75" parent="1" name="utf8_esperanto_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="76" parent="1" name="utf8_hungarian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="77" parent="1" name="utf8_sinhala_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="78" parent="1" name="utf8_german2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="79" parent="1" name="utf8_croatian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="80" parent="1" name="utf8_unicode_520_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="81" parent="1" name="utf8_vietnamese_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="82" parent="1" name="utf8_general_mysql500_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="83" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="85" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="86" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="87" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="111" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="112" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="113" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="114" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="115" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="116" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="118" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="119" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="120" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="121" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="122" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="123" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="124" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="125" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="126" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="127" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="128" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="129" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="130" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="131" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="132" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="133" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="134" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="135" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="136" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="137" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="138" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="139" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="140" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="141" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="142" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="143" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="144" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="145" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="146" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="147" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="148" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="149" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="150" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="151" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="152" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="153" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="154" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="155" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="156" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="157" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="158" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="159" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="160" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="161" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="162" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="163" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="164" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="165" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="166" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="167" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="168" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="169" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="170" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="171" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="172" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="173" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="174" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="175" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="176" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="177" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="178" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="179" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="180" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="181" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="182" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="183" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="184" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="185" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="186" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="187" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="188" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="189" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="190" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="191" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="192" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="193" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="194" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="195" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="196" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="197" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="198" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="199" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="200" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="201" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="202" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="203" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="204" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="205" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="206" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="207" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="208" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="209" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="210" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="211" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="212" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="213" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="214" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="215" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="216" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="217" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="218" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="219" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="220" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="221" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="222" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="223" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <schema id="224" parent="1" name="information_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="225" parent="1" name="all_bagdata_demo_202412">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="226" parent="1" name="bigdata_demo">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="227" parent="1" name="common">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="228" parent="1" name="dbgpt">
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="229" parent="1" name="dbgpt_linux">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="230" parent="1" name="dolphinscheduler">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="231" parent="1" name="imc">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="232" parent="1" name="imc20">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="233" parent="1" name="mock">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="234" parent="1" name="mysql">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="235" parent="1" name="nacos">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="236" parent="1" name="oximc">
      <CollationName>utf8mb4_bin</CollationName>
    </schema>
    <schema id="237" parent="1" name="passenger">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="238" parent="1" name="passenger_from">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="239" parent="1" name="performance_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="240" parent="1" name="seata_order">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="241" parent="1" name="seata_storage">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="242" parent="1" name="security_check_ads">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="243" parent="1" name="student">
      <CollationName>utf8mb4_bin</CollationName>
    </schema>
    <schema id="244" parent="1" name="sys">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="245" parent="1" name="szx_bigdata_dome_202412">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="246" parent="1" name="xbdp">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="247" parent="1" name="xcf2">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="248" parent="1" name="xcf2_peican_20241021">
      <CollationName>utf8mb4_bin</CollationName>
    </schema>
    <schema id="249" parent="1" name="xcf2o">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="250" parent="1" name="xcf3">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="251" parent="1" name="xcf3.2">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="252" parent="1" name="xcf-job">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="253" parent="1" name="xcf_fjjc">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="254" parent="1" name="xcf_lcx">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="255" parent="1" name="xcf_tmp">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="256" parent="1" name="xcnf_lowcode">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="257" parent="1" name="xcnf_lowcode_bak">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="258" parent="1" name="xcnf_lowcode_cqxianc">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="259" parent="1" name="xcnf_multi_tenant">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="260" parent="1" name="xcnf_sso_server">
      <CollationName>utf8mb4_bin</CollationName>
    </schema>
    <schema id="261" parent="1" name="xcnf_test">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="262" parent="1" name="xdtv">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="263" parent="1" name="xdtvtest">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="264" parent="1" name="xdtvtest1">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="265" parent="1" name="xdtvtestdata">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="266" parent="1" name="ximc">
      <CollationName>utf8mb4_bin</CollationName>
    </schema>
    <schema id="267" parent="1" name="ximc1">
      <CollationName>utf8_bin</CollationName>
    </schema>
    <schema id="268" parent="1" name="ximc2">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="269" parent="1" name="ximc2_l">
      <CollationName>utf8_bin</CollationName>
    </schema>
    <schema id="270" parent="1" name="ximc_new">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="271" parent="1" name="ximc_test">
      <CollationName>utf8_bin</CollationName>
    </schema>
    <schema id="272" parent="1" name="xipdb">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="273" parent="1" name="xipdb320">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="274" parent="1" name="xmn_bigdata_dome_202412">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="275" parent="1" name="xmqp">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="276" parent="1" name="xmqp_bak">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="277" parent="1" name="xoimc2_l">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="278" parent="1" name="xoimc_test">
      <CollationName>utf8_bin</CollationName>
    </schema>
    <schema id="279" parent="1" name="xotp">
      <Current>1</Current>
      <LastIntrospectionLocalTimestamp>2025-07-25.01:37:12</LastIntrospectionLocalTimestamp>
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="280" parent="1" name="xxl_job">
      <LastIntrospectionLocalTimestamp>2025-06-05.06:24:17</LastIntrospectionLocalTimestamp>
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="281" parent="1" name="xcnf_lowcode_ls">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="282" parent="1" name="xotp_test">
      <LastIntrospectionLocalTimestamp>2025-07-07.02:54:24</LastIntrospectionLocalTimestamp>
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <user id="283" parent="1" name="root"/>
    <user id="284" parent="1" name="mysql.session">
      <Host>localhost</Host>
    </user>
    <user id="285" parent="1" name="mysql.sys">
      <Host>localhost</Host>
    </user>
    <user id="286" parent="1" name="user_sys">
      <Host>localhost</Host>
    </user>
    <user id="287" parent="1" name="user_sys"/>
    <user id="288" parent="1" name="test"/>
    <user id="289" parent="1" name="dolphinscheduler"/>
    <user id="290" parent="1" name="user_replica"/>
    <table id="291" parent="279" name="bdp_api_dataset">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="292" parent="279" name="bdp_api_dataset_service">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="293" parent="279" name="bdp_api_dataset_service_query">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="294" parent="279" name="bdp_api_message_topic">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="295" parent="279" name="bdp_api_message_topic_app">
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="296" parent="279" name="bdp_asst_archive_datatable">
      <Comment>数据资产归档数据表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="297" parent="279" name="bdp_asst_archive_results">
      <Comment>数据资产归档档案（结果）</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="298" parent="279" name="bdp_asst_archive_scheduler">
      <Comment>数据资产归档调度</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="299" parent="279" name="bdp_asst_dataware">
      <Comment>数据资产仓库</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="300" parent="279" name="bdp_asst_stats_metrics">
      <Comment>数据资产统计指标记录</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="301" parent="279" name="bdp_asst_stats_scheduler">
      <Comment>数据资产统计调度</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="302" parent="279" name="bdp_dataflow_resource_mapper">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="303" parent="279" name="bdp_datasync_model_reader_mapper">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="304" parent="279" name="bdp_datasync_model_write_mapper">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="305" parent="279" name="bdp_dev_dataflow">
      <Comment>数据流程（定义）</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="306" parent="279" name="bdp_dev_dataflow_process">
      <Comment>数据流程作业</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="307" parent="279" name="bdp_dev_dataflow_process_instance">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="308" parent="279" name="bdp_dev_dataflow_process_job_instance">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="309" parent="279" name="bdp_dev_dataflow_process_resource">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="310" parent="279" name="bdp_dev_dataflow_resource">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="311" parent="279" name="bdp_intg_datasync">
      <Comment>数据同步定义</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="312" parent="279" name="bdp_intg_datasync_instance">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="313" parent="279" name="bdp_intg_datasync_job">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="314" parent="279" name="bdp_intg_datasync_job_instance">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="315" parent="279" name="bdp_intg_datasync_model">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="316" parent="279" name="bdp_meta_catalog">
      <Comment>元数据目录</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="317" parent="279" name="bdp_meta_datasource">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="318" parent="279" name="bdp_meta_datasource_model">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="319" parent="279" name="bdp_meta_datasource_resource">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="320" parent="279" name="bdp_meta_datasource_resource_sync_task">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="321" parent="279" name="bdp_meta_datatable">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="322" parent="279" name="bdp_meta_datatable_column">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="323" parent="279" name="bdp_meta_dataware">
      <Comment>元数据仓库</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="324" parent="279" name="bdp_qlty_assess_scheduler">
      <Comment>数据质量评分调度</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="325" parent="279" name="bdp_qlty_assess_scores">
      <Comment>数据质量评分得分记录</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="326" parent="279" name="bdp_qlty_dim">
      <Comment>数据质量维度</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="327" parent="279" name="bdp_qlty_rule">
      <Comment>数据质量规则</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="328" parent="279" name="bdp_qlty_rule_metrics">
      <Comment>数据质量规则指标记录</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="329" parent="279" name="bdp_qlty_rule_scheduler">
      <Comment>数据质量规则调度</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="330" parent="279" name="bdp_qlty_rule_tmpl">
      <Comment>数据质量规则模板</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="331" parent="279" name="oauth2_authorization">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="332" parent="279" name="oauth2_authorization_consent">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="333" parent="279" name="sys_app">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="334" parent="279" name="sys_app_device">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="335" parent="279" name="sys_app_service">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="336" parent="279" name="sys_dept">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="337" parent="279" name="sys_device">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="338" parent="279" name="sys_mapping">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="339" parent="279" name="sys_menu">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="340" parent="279" name="sys_menu_operation">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="341" parent="279" name="sys_oauth2_authorization">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="342" parent="279" name="sys_oauth2_registered_client">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="343" parent="279" name="sys_operation">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="344" parent="279" name="sys_role">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="345" parent="279" name="sys_role_menu">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="346" parent="279" name="sys_service">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="347" parent="279" name="sys_system">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="348" parent="279" name="sys_tenant">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="349" parent="279" name="sys_tlog">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="350" parent="279" name="sys_user">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="351" parent="279" name="sys_user_dept">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="352" parent="279" name="sys_user_password_history">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="353" parent="279" name="sys_user_role">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="354" parent="279" name="sys_user_system">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="355" parent="279" name="sys_xfield_config">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="356" parent="279" name="xxl_job_group">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="357" parent="279" name="xxl_job_info">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="358" parent="279" name="xxl_job_lock">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="359" parent="279" name="xxl_job_log">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="360" parent="279" name="xxl_job_log_report">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="361" parent="279" name="xxl_job_logglue">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="362" parent="279" name="xxl_job_registry">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="363" parent="279" name="xxl_job_user">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="364" parent="280" name="xxl_job_group">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="365" parent="280" name="xxl_job_increment">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="366" parent="280" name="xxl_job_info">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="367" parent="280" name="xxl_job_info_run_param">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="368" parent="280" name="xxl_job_lock">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="369" parent="280" name="xxl_job_log">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="370" parent="280" name="xxl_job_log_report">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="371" parent="280" name="xxl_job_logglue">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="372" parent="280" name="xxl_job_process_task">
      <Comment>流程（实例）任务</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="373" parent="280" name="xxl_job_process_task_execution">
      <Comment>流程（实例）任务执行</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="374" parent="280" name="xxl_job_process_task_execution_lock">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="375" parent="280" name="xxl_job_process_task_history">
      <Comment>流程（实例）任务执行</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="376" parent="280" name="xxl_job_registry">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="377" parent="280" name="xxl_job_user">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="378" parent="280" name="xxl_job_workflow-x">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="379" parent="282" name="ads_profit_summary">
      <Comment>ADS_利润表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="380" parent="282" name="dws_orders_summary">
      <Comment>订单日度汇总服务层表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="381" parent="282" name="ods_orders">
      <Comment>订单操作数据层表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="382" parent="282" name="ods_orders_2">
      <Comment>订单操作数据层表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="383" parent="282" name="orders">
      <Comment>原始订单表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="384" parent="282" name="ximc_mysql">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="385" parent="282" name="ximc_mysql_to">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <column id="386" parent="291" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="387" parent="291" name="create_time">
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="388" parent="291" name="update_time">
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="389" parent="291" name="deleted">
      <Comment>是否删除 0: 否 1: 是</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="390" parent="291" name="datasource_id">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="391" parent="291" name="dataset_name">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="392" parent="291" name="dataset_code">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="393" parent="291" name="dataset_sql">
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="394" parent="291" name="dataset_published">
      <Comment>0未发布 1已发布</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="395" parent="291" name="service_id">
      <Position>10</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="396" parent="291" name="dataset_parameter">
      <Position>11</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="397" parent="291" name="dataset_mode">
      <Comment>模型模式 0设计模式 1脚本模式</Comment>
      <Position>12</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="398" parent="291" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="399" parent="291" name="unique_dataset_code">
      <ColNames>dataset_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="400" parent="291" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="401" parent="291" name="unique_dataset_code">
      <UnderlyingIndexName>unique_dataset_code</UnderlyingIndexName>
    </key>
    <column id="402" parent="292" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="403" parent="292" name="create_time">
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="404" parent="292" name="update_time">
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="405" parent="292" name="deleted">
      <Comment>是否删除 0: 否 1: 是</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="406" parent="292" name="datasource_id">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="407" parent="292" name="dataset_id">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="408" parent="292" name="dataset_name">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="409" parent="292" name="dataset_code">
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="410" parent="292" name="dataset_sql">
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="411" parent="292" name="dataset_parameter">
      <Position>10</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="412" parent="292" name="dataset_mode">
      <Comment>模型模式 0设计模式 1脚本模式</Comment>
      <Position>11</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="413" parent="292" name="dataset_published">
      <Comment>0未发布 1已发布</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="414" parent="292" name="service_id">
      <Position>13</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <index id="415" parent="292" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="416" parent="292" name="unique_dataset_code">
      <ColNames>dataset_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="417" parent="292" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="418" parent="292" name="unique_dataset_code">
      <UnderlyingIndexName>unique_dataset_code</UnderlyingIndexName>
    </key>
    <column id="419" parent="293" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="420" parent="293" name="create_time">
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="421" parent="293" name="update_time">
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="422" parent="293" name="deleted">
      <Comment>是否删除 0: 否 1: 是</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="423" parent="293" name="service_id">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="424" parent="293" name="dataset_id">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="425" parent="293" name="dataset_hash">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="426" parent="293" name="app_key">
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="427" parent="293" name="client_info">
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="428" parent="293" name="start_time">
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="429" parent="293" name="running_time">
      <DefaultExpression>-1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="430" parent="293" name="result_rows">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="431" parent="293" name="result_error">
      <Position>13</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="432" parent="293" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="433" parent="293" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="434" parent="294" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="435" parent="294" name="topic_name">
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="436" parent="294" name="datasource_id">
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="437" parent="294" name="remark">
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="438" parent="294" name="create_user">
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="439" parent="294" name="create_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="440" parent="294" name="update_user">
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="441" parent="294" name="update_time">
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="442" parent="294" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="443" parent="294" name="bdp_api_message_topic_unique">
      <ColNames>topic_name
datasource_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="444" parent="294" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="445" parent="294" name="bdp_api_message_topic_unique">
      <UnderlyingIndexName>bdp_api_message_topic_unique</UnderlyingIndexName>
    </key>
    <column id="446" parent="295" name="ID">
      <Comment>记录ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="447" parent="295" name="APP_ID">
      <Comment>客户端ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(36)|0s</StoredType>
    </column>
    <column id="448" parent="295" name="TOPIC_ID">
      <Comment>主题ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(36)|0s</StoredType>
    </column>
    <column id="449" parent="295" name="OPER_TYPE">
      <Comment>SUB:订阅；PUB:发布</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>char(20)|0s</StoredType>
    </column>
    <column id="450" parent="295" name="TOPIC_TAGS">
      <Comment>标签</Comment>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="451" parent="295" name="REMARK">
      <Comment>备注</Comment>
      <Position>6</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="452" parent="295" name="CREATE_USER">
      <Comment>创建人</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="453" parent="295" name="CREATE_TIME">
      <Comment>创建时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="454" parent="295" name="UPDATE_USER">
      <Comment>更新人</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="455" parent="295" name="UPDATE_TIME">
      <Comment>更新时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="456" parent="295" name="PRIMARY">
      <ColNames>ID</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="457" parent="295" name="APP_ID">
      <ColNames>APP_ID
TOPIC_ID
OPER_TYPE</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="458" parent="295" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="459" parent="295" name="APP_ID">
      <UnderlyingIndexName>APP_ID</UnderlyingIndexName>
    </key>
    <column id="460" parent="296" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="461" parent="296" name="datatable_id">
      <Comment>表ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="462" parent="296" name="dataware_id">
      <Comment>库ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="463" parent="296" name="datatable_name">
      <Comment>表名（冗余）</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="464" parent="296" name="archive_scheduler_id">
      <Comment>归档调度ID</Comment>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="465" parent="296" name="archive_range_expr">
      <Comment>归档条件表达式</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="466" parent="296" name="datatable_reduced">
      <Comment>删除原表数据</Comment>
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="467" parent="296" name="nearlined">
      <Comment>启用近线归档（0：未启用，1：启用）</Comment>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="468" parent="296" name="nearline_datasource_id">
      <Comment>近线数据源ID</Comment>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="469" parent="296" name="nearline_datasource_name">
      <Comment>近线数据源名（冗余）</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="470" parent="296" name="nearline_datatable_name">
      <Comment>近线数据表名</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="471" parent="296" name="offlined">
      <Comment>启用离线归档（0：未启用，1：启用）</Comment>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="472" parent="296" name="offline_file_name">
      <Comment>离线文件名</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="473" parent="296" name="offline_file_lifedays">
      <Comment>离线留存天数</Comment>
      <Position>14</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="474" parent="296" name="create_user">
      <Comment>create_user</Comment>
      <Position>15</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="475" parent="296" name="create_time">
      <Comment>create_time</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="476" parent="296" name="update_user">
      <Comment>update_user</Comment>
      <Position>17</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="477" parent="296" name="update_time">
      <Comment>update_time</Comment>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="478" parent="296" name="archive_type">
      <Comment>归档类型</Comment>
      <Position>19</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="479" parent="296" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="480" parent="296" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="481" parent="297" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="482" parent="297" name="archive_scheduler_id">
      <Comment>调度ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="483" parent="297" name="archive_datatable_id">
      <Comment>归档表ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="484" parent="297" name="dataware_name">
      <Comment>库名（冗余）</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="485" parent="297" name="datatable_name">
      <Comment>数据表名（冗余）</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="486" parent="297" name="nearline_archive_data_count">
      <Comment>近线归档数据量</Comment>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="487" parent="297" name="offline_archive_data_count">
      <Comment>离线归档数据量</Comment>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="488" parent="297" name="nearline_status">
      <Comment>近线执行状态</Comment>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="489" parent="297" name="nearline_data_size">
      <Comment>近线数据大小</Comment>
      <Position>9</Position>
      <StoredType>float|0s</StoredType>
    </column>
    <column id="490" parent="297" name="nearline_datasource_name">
      <Comment>近线数据源名</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="491" parent="297" name="nearline_datatable_name">
      <Comment>近线数据源表名</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="492" parent="297" name="offline_status">
      <Comment>离线执行状态</Comment>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="493" parent="297" name="offline_file_size">
      <Comment>离线归档文件大小</Comment>
      <Position>13</Position>
      <StoredType>float|0s</StoredType>
    </column>
    <column id="494" parent="297" name="offline_file_path">
      <Comment>离线归档文件位置</Comment>
      <Position>14</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="495" parent="297" name="check_time">
      <Comment>归档执行时间</Comment>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="496" parent="297" name="create_time">
      <Comment>create_time</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="497" parent="297" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="498" parent="297" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="499" parent="298" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="500" parent="298" name="name">
      <Comment>名称(策略名称)</Comment>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="501" parent="298" name="cron_expr">
      <Comment>CRON表达式</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="502" parent="298" name="route_strategy">
      <Comment>路由策略</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="503" parent="298" name="block_strategy">
      <Comment>阻塞策略</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="504" parent="298" name="executor_timeout">
      <Comment>执行器超时</Comment>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="505" parent="298" name="executor_retry_count">
      <Comment>执行器失败重试</Comment>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="506" parent="298" name="started">
      <Comment>启动|停止</Comment>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="507" parent="298" name="dispatch_id">
      <Comment>调度器代理ID</Comment>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="508" parent="298" name="create_user">
      <Comment>create_user</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="509" parent="298" name="create_time">
      <Comment>create_time</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="510" parent="298" name="update_user">
      <Comment>update_user</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="511" parent="298" name="update_time">
      <Comment>update_time</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="512" parent="298" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="513" parent="298" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="514" parent="299" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="515" parent="299" name="name">
      <Comment>英文名</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="516" parent="299" name="code">
      <Comment>资产编码：DW-开头</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="517" parent="299" name="alias">
      <Comment>资产别名</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="518" parent="299" name="type">
      <Comment>资产类型：TAB：数据库表、IMG:图片资源、LOG：日志文件...</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="519" parent="299" name="sort_no">
      <Comment>排序</Comment>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="520" parent="299" name="remark">
      <Comment>备注</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="521" parent="299" name="datasource_options">
      <Comment>数据来源配置（当资产类型是TAB时,对应datasource_id）</Comment>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="522" parent="299" name="deleted">
      <Comment>是否删除：0:否；1:是</Comment>
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="523" parent="299" name="create_user">
      <Comment>create_user</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="524" parent="299" name="create_time">
      <Comment>create_time</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="525" parent="299" name="update_user">
      <Comment>update_user</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="526" parent="299" name="update_time">
      <Comment>update_time</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="527" parent="299" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="528" parent="299" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="529" parent="300" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="530" parent="300" name="stats_scheduler_id">
      <Comment>调度ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="531" parent="300" name="stats_targets">
      <Comment>资产统计对象</Comment>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="532" parent="300" name="metric_date">
      <Comment>资产指标日期</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="533" parent="300" name="metric_name">
      <Comment>资产指标名</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="534" parent="300" name="metric_labels">
      <Comment>资产指标标签</Comment>
      <Position>6</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="535" parent="300" name="metric_value">
      <Comment>资产指标值</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="536" parent="300" name="stats_time">
      <Comment>统计执行时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="537" parent="300" name="stats_status">
      <Comment>统计执行状态（0：失败，1:成功）</Comment>
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="538" parent="300" name="stats_report">
      <Comment>统计执行报告</Comment>
      <Position>10</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="539" parent="300" name="create_time">
      <Comment>create_time</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="540" parent="300" name="update_time">
      <Comment>update_time</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="541" parent="300" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="542" parent="300" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="543" parent="301" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="544" parent="301" name="name">
      <Comment>名称</Comment>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="545" parent="301" name="cron_expr">
      <Comment>CRON表达式</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="546" parent="301" name="route_strategy">
      <Comment>路由策略</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="547" parent="301" name="block_strategy">
      <Comment>阻塞策略</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="548" parent="301" name="executor_timeout">
      <Comment>执行器超时</Comment>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="549" parent="301" name="executor_retry_count">
      <Comment>执行器失败重试</Comment>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="550" parent="301" name="started">
      <Comment>启动|停止</Comment>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="551" parent="301" name="dispatch_id">
      <Comment>调度器代理ID</Comment>
      <Position>9</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="552" parent="301" name="create_user">
      <Comment>create_user</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="553" parent="301" name="create_time">
      <Comment>create_time</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="554" parent="301" name="update_user">
      <Comment>update_user</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="555" parent="301" name="update_time">
      <Comment>update_time</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="556" parent="301" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="557" parent="301" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="558" parent="302" name="id">
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="559" parent="302" name="upload_time">
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="560" parent="302" name="path">
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="561" parent="302" name="deleted">
      <Position>4</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="562" parent="302" name="synced">
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="563" parent="303" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="564" parent="303" name="pid">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="565" parent="303" name="upload_time">
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="566" parent="303" name="path">
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="567" parent="303" name="deleted">
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="568" parent="303" name="synced">
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <index id="569" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="570" parent="303" name="INDEX_RM_PID">
      <ColNames>pid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="571" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="572" parent="304" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="573" parent="304" name="pid">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="574" parent="304" name="upload_time">
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="575" parent="304" name="path">
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="576" parent="304" name="deleted">
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="577" parent="304" name="synced">
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <index id="578" parent="304" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="579" parent="304" name="INDEX_WM_PID">
      <ColNames>pid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="580" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="581" parent="305" name="id">
      <Comment>唯一标识列</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="582" parent="305" name="name">
      <Comment>流程图名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="583" parent="305" name="graph_options">
      <Comment>流程图绘信息</Comment>
      <Position>3</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="584" parent="305" name="deleted">
      <Comment>是否删除 0: 否 1: 是</Comment>
      <Position>4</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="585" parent="305" name="shelved">
      <Comment>是否上架 0: 否 1: 是</Comment>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="586" parent="305" name="create_user">
      <Comment>创建人</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="587" parent="305" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="588" parent="305" name="update_time">
      <Comment>更新时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="589" parent="305" name="remark">
      <Comment>备注说明</Comment>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="590" parent="305" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="591" parent="305" name="data_flow_name_unique">
      <ColNames>name</ColNames>
      <PrefixLengths>255</PrefixLengths>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="592" parent="305" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="593" parent="305" name="data_flow_name_unique">
      <UnderlyingIndexName>data_flow_name_unique</UnderlyingIndexName>
    </key>
    <column id="594" parent="306" name="id">
      <Comment>唯一标识列</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="595" parent="306" name="name">
      <Comment>流程（实例）名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="596" parent="306" name="dataflow_id">
      <Comment>（备案）流程图ID</Comment>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="597" parent="306" name="dataflow_graph_options">
      <Comment>流程图绘图信息</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="598" parent="306" name="process_options">
      <Comment>流程作业配置</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="599" parent="306" name="status">
      <Comment>调度状态（ 0: 未运行/停止, 1: 运行状态,  2: 暂停状态）</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="600" parent="306" name="path">
      <Comment>流程文件的路径</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="601" parent="306" name="create_user">
      <Comment>创建人</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="602" parent="306" name="create_time">
      <Comment>创建时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="603" parent="306" name="update_time">
      <Comment>更新时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="604" parent="306" name="remark">
      <Comment>备注信息</Comment>
      <Position>11</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="605" parent="306" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="606" parent="306" name="IDX_DATAFLOW_ID">
      <ColNames>dataflow_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="607" parent="306" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="608" parent="307" name="id">
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="609" parent="307" name="trigger_time">
      <Comment>启动时间</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="610" parent="307" name="process_id">
      <Comment>同步作业ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="611" parent="307" name="process_params">
      <Comment>流程参数</Comment>
      <Position>4</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="612" parent="307" name="status">
      <Comment>流程实例执行的状态：0-Dead Started; 1-Running; 2-Finished; 3-Failed；4-Canceled</Comment>
      <Position>5</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="613" parent="307" name="status_message">
      <Comment>状态消息</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="614" parent="307" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="615" parent="307" name="create_user">
      <Comment>创建人</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="616" parent="307" name="update_time">
      <Comment>更新时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="617" parent="307" name="update_user">
      <Comment>更新人</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="618" parent="307" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="619" parent="307" name="IDX_DATAFLOW_PROCESS_ID">
      <ColNames>process_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="620" parent="307" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="621" parent="308" name="id">
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="622" parent="308" name="trigger_time">
      <Comment>启动时间</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="623" parent="308" name="process_id">
      <Comment>同步作业ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="624" parent="308" name="process_instance_id">
      <Comment>同步作业实例ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="625" parent="308" name="job_params">
      <Comment>流程参数</Comment>
      <Position>5</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="626" parent="308" name="status">
      <Comment>流程实例执行的状态：0-Dead Started; 1-Running; 2-Finished; 3-Failed；4-Canceled</Comment>
      <Position>6</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="627" parent="308" name="status_message">
      <Comment>状态消息</Comment>
      <Position>7</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="628" parent="308" name="create_time">
      <Comment>创建时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="629" parent="308" name="create_user">
      <Comment>创建人</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="630" parent="308" name="update_time">
      <Comment>更新时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="631" parent="308" name="update_user">
      <Comment>更新人</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="632" parent="308" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="633" parent="308" name="IDX_DATAFLOW_PROCESS_INST_ID">
      <ColNames>process_id
process_instance_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="634" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="635" parent="309" name="id">
      <Comment>唯一标识</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="636" parent="309" name="process_id">
      <Comment>流程ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="637" parent="309" name="name">
      <Comment>资源名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="638" parent="309" name="upload_time">
      <Comment>上传时间</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="639" parent="309" name="update_time">
      <Comment>更新时间</Comment>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="640" parent="309" name="path">
      <Comment>资源路径</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="641" parent="309" name="upload_user">
      <Comment>上传人</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="642" parent="309" name="remark">
      <Comment>资源描述</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="643" parent="309" name="resource_id">
      <Comment>资源编号</Comment>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <index id="644" parent="309" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="645" parent="309" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="646" parent="310" name="id">
      <Comment>唯一标识</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="647" parent="310" name="name">
      <Comment>资源名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="648" parent="310" name="upload_time">
      <Comment>上传时间</Comment>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="649" parent="310" name="update_time">
      <Comment>更新时间</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="650" parent="310" name="path">
      <Comment>资源路径</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="651" parent="310" name="upload_user">
      <Comment>上传人</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="652" parent="310" name="remark">
      <Comment>资源描述</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="653" parent="310" name="dataflow_id">
      <Comment>流程定义表主键</Comment>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="654" parent="310" name="use_count">
      <Comment>使用数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="655" parent="310" name="deleted">
      <Comment>是否删除 0: 否 1: 是</Comment>
      <Position>10</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <index id="656" parent="310" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="657" parent="310" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="658" parent="311" name="id">
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="659" parent="311" name="create_time">
      <Comment>创建时间</Comment>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="660" parent="311" name="update_time">
      <Comment>更新时间</Comment>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="661" parent="311" name="create_user">
      <Comment>创建人/维护人</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="662" parent="311" name="intg_name">
      <Comment>同步任务名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="663" parent="311" name="orgin_type">
      <Comment>数据来源集成方式 0:内置 1:插件</Comment>
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="664" parent="311" name="orgin_intg_model_id">
      <Comment>数据来源集成模型ID</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="665" parent="311" name="orgin_datasource_name">
      <Comment>数据来源数据源(或插件)名称</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="666" parent="311" name="orgin_datasource_id">
      <Comment>数据来源数据源(或插件)ID</Comment>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="667" parent="311" name="orgin_plugin_path">
      <Comment>数据来源插件路径</Comment>
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="668" parent="311" name="orgin_base_json">
      <Comment>数据来源基础信息实际Json数据</Comment>
      <Position>11</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="669" parent="311" name="orgin_adv_json">
      <Comment>数据来源进阶信息实际Json数据</Comment>
      <Position>12</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="670" parent="311" name="orgin_high_json">
      <Comment>数据来源高级信息实际Json数据</Comment>
      <Position>13</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="671" parent="311" name="orgin_column_json">
      <Comment>数据来源列信息实际Json数据</Comment>
      <Position>14</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="672" parent="311" name="orgin_run_schema">
      <Comment>数据来源运行模式</Comment>
      <Position>15</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="673" parent="311" name="dest_type">
      <Comment>数据去向集成方式 0:内置 1：插件</Comment>
      <Position>16</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="674" parent="311" name="dest_intg_model_id">
      <Comment>数据去向集成模型ID</Comment>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="675" parent="311" name="dest_datasource_name">
      <Comment>数据去向数据源(或插件)名称</Comment>
      <Position>18</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="676" parent="311" name="dest_datasource_id">
      <Comment>数据去向数据源(或插件)ID</Comment>
      <Position>19</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="677" parent="311" name="dest_plugin_path">
      <Comment>数据去向插件路径</Comment>
      <Position>20</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="678" parent="311" name="dest_base_json">
      <Comment>数据去向基础信息实际Json数据</Comment>
      <Position>21</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="679" parent="311" name="dest_adv_json">
      <Comment>数据去向进阶信息实际Json数据</Comment>
      <Position>22</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="680" parent="311" name="scheduler_expr">
      <Comment>调度参数</Comment>
      <Position>23</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="681" parent="311" name="dest_high_json">
      <Comment>数据去向高级信息实际Json数据</Comment>
      <Position>24</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="682" parent="311" name="dest_column_json">
      <Comment>数据去向列信息实际Json数据</Comment>
      <Position>25</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="683" parent="311" name="route_strategy">
      <Comment>路由策略</Comment>
      <Position>26</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="684" parent="311" name="block_strategy">
      <Comment>阻塞处理策略</Comment>
      <Position>27</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="685" parent="311" name="child_jobid">
      <Comment>子任务ID组</Comment>
      <Position>28</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="686" parent="311" name="executor_timeout">
      <Comment>任务执行超时时间，单位秒</Comment>
      <Position>29</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="687" parent="311" name="executor_fail_retry_count">
      <Comment>失败重试次数</Comment>
      <Position>30</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="688" parent="311" name="has_delete">
      <Comment>是否删除 0：否 1：是</Comment>
      <Position>31</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="689" parent="311" name="job_mode">
      <Comment>数据集成同步模式（BATCH/STREAMING）</Comment>
      <NotNull>1</NotNull>
      <Position>32</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="690" parent="311" name="field_mapping_json">
      <Comment>数据集成同步字段映射</Comment>
      <Position>33</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="691" parent="311" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="692" parent="311" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="693" parent="312" name="id">
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="694" parent="312" name="datasync_id">
      <Comment>集成信息ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="695" parent="312" name="publish_time">
      <Comment>发布时间</Comment>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="696" parent="312" name="update_time">
      <Comment>更新时间</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="697" parent="312" name="publish_user">
      <Comment>发布人</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="698" parent="312" name="instance_name">
      <Comment>实例名称</Comment>
      <Position>6</Position>
      <StoredType>varchar(58)|0s</StoredType>
    </column>
    <column id="699" parent="312" name="instance_code">
      <Comment>实例编码</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="700" parent="312" name="template_context">
      <Comment>模板文件实际内容，每次运行时均要从新上传</Comment>
      <Position>8</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="701" parent="312" name="template_code">
      <Comment>模板文件校验码，在某个集成信息内唯一</Comment>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="702" parent="312" name="orgin_type">
      <Comment>数据来源集成方式 0:内置 1:插件</Comment>
      <Position>10</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="703" parent="312" name="orgin_datasource_name">
      <Comment>数据来源数据源(或插件)名称</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="704" parent="312" name="orgin_plugin_path">
      <Comment>数据来源插件路径</Comment>
      <Position>12</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="705" parent="312" name="dest_type">
      <Comment>数据去向集成方式 0:内置 1：插件</Comment>
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="706" parent="312" name="dest_datasource_name">
      <Comment>数据去向数据源(或插件)名称</Comment>
      <Position>14</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="707" parent="312" name="dest_plugin_path">
      <Comment>数据去向插件路径</Comment>
      <Position>15</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="708" parent="312" name="dispatch_id">
      <Comment>调度任务ID</Comment>
      <Position>16</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="709" parent="312" name="file_path">
      <Comment>JSON文件在服务器上的路径</Comment>
      <Position>17</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="710" parent="312" name="has_delete">
      <Comment>是否删除 0：否 1：是</Comment>
      <Position>18</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="711" parent="312" name="job_group">
      <Comment>执行器主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="712" parent="312" name="schedule_type">
      <Comment>调度类型</Comment>
      <DefaultExpression>&apos;NONE&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="713" parent="312" name="schedule_conf">
      <Comment>调度配置，值含义取决于调度类型</Comment>
      <Position>21</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="714" parent="312" name="misfire_strategy">
      <Comment>调度过期策略</Comment>
      <DefaultExpression>&apos;DO_NOTHING&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="715" parent="312" name="executor_route_strategy">
      <Comment>执行器路由策略</Comment>
      <Position>23</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="716" parent="312" name="executor_handler">
      <Comment>执行器任务handler</Comment>
      <Position>24</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="717" parent="312" name="executor_param">
      <Comment>执行器任务参数</Comment>
      <Position>25</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="718" parent="312" name="executor_block_strategy">
      <Comment>阻塞处理策略</Comment>
      <Position>26</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="719" parent="312" name="executor_timeout">
      <Comment>任务执行超时时间，单位秒</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>27</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="720" parent="312" name="executor_fail_retry_count">
      <Comment>失败重试次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>28</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="721" parent="312" name="child_jobid">
      <Comment>子任务ID，多个逗号分隔</Comment>
      <Position>29</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="722" parent="312" name="trigger_status">
      <Comment>调度状态：0-停止，1-运行</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>30</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <index id="723" parent="312" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="724" parent="312" name="INDEX_BIDS_PID_CODE">
      <ColNames>datasync_id
instance_code</ColNames>
      <Comment>&apos;父表ID+实例编码确定的唯一索引&apos;</Comment>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="725" parent="312" name="INDEX_BIDS_DID">
      <ColNames>dispatch_id</ColNames>
      <Comment>&apos;调度任务唯一索引ID&apos;</Comment>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="726" parent="312" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="727" parent="312" name="INDEX_BIDS_PID_CODE">
      <UnderlyingIndexName>INDEX_BIDS_PID_CODE</UnderlyingIndexName>
    </key>
    <key id="728" parent="312" name="INDEX_BIDS_DID">
      <UnderlyingIndexName>INDEX_BIDS_DID</UnderlyingIndexName>
    </key>
    <column id="729" parent="313" name="id">
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="730" parent="313" name="datasync_id">
      <Comment>集成信息ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="731" parent="313" name="create_time">
      <Comment>发布时间</Comment>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="732" parent="313" name="create_user">
      <Comment>发布人</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="733" parent="313" name="update_time">
      <Comment>更新时间</Comment>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="734" parent="313" name="update_user">
      <Comment>更新人</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="735" parent="313" name="job_name">
      <Comment>实例名称</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(58)|0s</StoredType>
    </column>
    <column id="736" parent="313" name="job_options">
      <Comment>同步作业运行配置</Comment>
      <Position>8</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="737" parent="313" name="orgin_type">
      <Comment>数据来源集成方式 0:内置 1:插件</Comment>
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="738" parent="313" name="orgin_datasource_name">
      <Comment>数据来源数据源(或插件)名称</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="739" parent="313" name="orgin_plugin_path">
      <Comment>数据来源插件路径</Comment>
      <Position>11</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="740" parent="313" name="dest_type">
      <Comment>数据去向集成方式 0:内置 1：插件</Comment>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="741" parent="313" name="dest_datasource_name">
      <Comment>数据去向数据源(或插件)名称</Comment>
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="742" parent="313" name="dest_plugin_path">
      <Comment>数据去向插件路径</Comment>
      <Position>14</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="743" parent="313" name="status">
      <Comment>状态：0-停止，1-运行</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="744" parent="313" name="deleted">
      <Comment>是否删除 0：否 1：是</Comment>
      <Position>16</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="745" parent="313" name="schedule_id">
      <Comment>调度任务ID</Comment>
      <Position>17</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="746" parent="313" name="schedule_group">
      <Comment>执行器主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="747" parent="313" name="schedule_type">
      <Comment>调度类型</Comment>
      <DefaultExpression>&apos;NONE&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="748" parent="313" name="schedule_conf">
      <Comment>调度配置，值含义取决于调度类型</Comment>
      <Position>20</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="749" parent="313" name="misfire_strategy">
      <Comment>调度过期策略</Comment>
      <DefaultExpression>&apos;DO_NOTHING&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="750" parent="313" name="executor_route_strategy">
      <Comment>执行器路由策略</Comment>
      <Position>22</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="751" parent="313" name="executor_handler">
      <Comment>执行器任务handler</Comment>
      <Position>23</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="752" parent="313" name="executor_params">
      <Comment>执行器任务参数</Comment>
      <Position>24</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="753" parent="313" name="executor_block_strategy">
      <Comment>阻塞处理策略</Comment>
      <Position>25</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="754" parent="313" name="executor_timeout">
      <Comment>任务执行超时时间，单位秒</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>26</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="755" parent="313" name="executor_fail_retry_count">
      <Comment>失败重试次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>27</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="756" parent="313" name="child_jobids">
      <Comment>子任务ID，多个逗号分隔</Comment>
      <Position>28</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="757" parent="313" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="758" parent="313" name="IDX_DATASYNC_ID">
      <ColNames>datasync_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="759" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="760" parent="314" name="id">
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="761" parent="314" name="job_id">
      <Comment>同步作业ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="762" parent="314" name="job_name">
      <Comment>任务名称</Comment>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="763" parent="314" name="job_mode">
      <Comment>任务执行类型（BATCH/STREAMING）</Comment>
      <Position>4</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="764" parent="314" name="job_params">
      <Position>5</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="765" parent="314" name="schedule_id">
      <Comment>作业调度ID（xxl-job id）</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="766" parent="314" name="schedule_log_id">
      <Comment>作业调度日志ID(xxl-job 日志ID)</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="767" parent="314" name="schedule_time">
      <Comment>调度时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="768" parent="314" name="trigger_time">
      <Comment>开始时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="769" parent="314" name="finish_time">
      <Comment>结束时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="770" parent="314" name="status">
      <Comment>作业实例执行的状态：0-Dead Started; 1-Running; 2-Finished; 3-Failed；4-Canceled/Stopped</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="771" parent="314" name="status_message">
      <Comment>状态消息</Comment>
      <Position>12</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="772" parent="314" name="received_count">
      <Position>13</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="773" parent="314" name="recevied_qps">
      <Position>14</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="774" parent="314" name="writed_count">
      <Position>15</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="775" parent="314" name="writed_qps">
      <Position>16</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="776" parent="314" name="create_time">
      <Comment>创建时间</Comment>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="777" parent="314" name="update_time">
      <Comment>更新时间</Comment>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="778" parent="314" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="779" parent="314" name="IDX_DATASYNC_JOB_ID">
      <ColNames>job_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="780" parent="314" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="781" parent="315" name="id">
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="782" parent="315" name="create_time">
      <Comment>创建时间</Comment>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="783" parent="315" name="update_time">
      <Comment>更新时间</Comment>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="784" parent="315" name="create_user">
      <Comment>维护人ID</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="785" parent="315" name="model_name">
      <Comment>集成模型名称</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="786" parent="315" name="run_schema">
      <Comment>运行模式 0: 永久运行 1:手动运行</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="787" parent="315" name="integration_type">
      <Comment>集成类型 0:读 1:写</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="788" parent="315" name="integration_way">
      <Comment>集成方式 0:内置 1:插件</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="789" parent="315" name="datasource_model_id">
      <Comment>数据源模型ID</Comment>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="790" parent="315" name="datasource_model_type">
      <Comment>数据源模型名称</Comment>
      <Position>10</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="791" parent="315" name="plugin_path">
      <Comment>插件路径</Comment>
      <Position>11</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="792" parent="315" name="plugin_name">
      <Comment>插件名称</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="793" parent="315" name="remark">
      <Comment>描述说明</Comment>
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="794" parent="315" name="base_json">
      <Comment>基础JSON模型数据</Comment>
      <Position>14</Position>
      <StoredType>varchar(5120)|0s</StoredType>
    </column>
    <column id="795" parent="315" name="adv_json">
      <Comment>进阶JSON数据</Comment>
      <Position>15</Position>
      <StoredType>varchar(5120)|0s</StoredType>
    </column>
    <column id="796" parent="315" name="high_json">
      <Comment>高级JSON数据</Comment>
      <Position>16</Position>
      <StoredType>varchar(5120)|0s</StoredType>
    </column>
    <column id="797" parent="315" name="column_json">
      <Comment>列描述JSON数据</Comment>
      <Position>17</Position>
      <StoredType>varchar(5120)|0s</StoredType>
    </column>
    <index id="798" parent="315" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="799" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="800" parent="316" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="801" parent="316" name="code">
      <Comment>资产编码</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="802" parent="316" name="name">
      <Comment>名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="803" parent="316" name="parent_id">
      <Comment>父级类目</Comment>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="804" parent="316" name="sort_no">
      <Comment>排序（预留）</Comment>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="805" parent="316" name="remark">
      <Comment>备注</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="806" parent="316" name="dataware_id">
      <Comment>仓库ID</Comment>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="807" parent="316" name="create_user">
      <Comment>create_user</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="808" parent="316" name="create_time">
      <Comment>create_time</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="809" parent="316" name="update_user">
      <Comment>update_user</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="810" parent="316" name="update_time">
      <Comment>update_time</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="811" parent="316" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="812" parent="316" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="813" parent="317" name="id">
      <Comment>逻辑主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="814" parent="317" name="name">
      <Comment>数据源名称</Comment>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="815" parent="317" name="model_id">
      <Comment>数据源模型ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="816" parent="317" name="type">
      <Comment>数据源类型 MySQL、Hive、Kafka...</Comment>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="817" parent="317" name="category">
      <Comment>数据源分类 NoSQL、RDB、MQ、BDATA</Comment>
      <Position>5</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="818" parent="317" name="options">
      <Comment>数据源连接配置</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="819" parent="317" name="remark">
      <Comment>描述</Comment>
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="820" parent="317" name="create_user">
      <Comment>创建人/维护人</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="821" parent="317" name="create_time">
      <Comment>创建时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="822" parent="317" name="update_time">
      <Comment>更新时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="823" parent="317" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="824" parent="317" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="825" parent="318" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="826" parent="318" name="create_time">
      <Comment>创建时间</Comment>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="827" parent="318" name="update_time">
      <Comment>更新时间</Comment>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="828" parent="318" name="create_user">
      <Comment>创建人</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="829" parent="318" name="remark">
      <Comment>备注说明</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="830" parent="318" name="type">
      <Comment>数据源类型</Comment>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="831" parent="318" name="template">
      <Comment>模板内容</Comment>
      <Position>7</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="832" parent="318" name="category">
      <Comment>数据源类目</Comment>
      <Position>8</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <index id="833" parent="318" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="834" parent="318" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="835" parent="319" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="836" parent="319" name="name">
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="837" parent="319" name="update_time">
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="838" parent="319" name="path">
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="839" parent="319" name="upload_user">
      <Position>5</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="840" parent="319" name="remark">
      <Position>6</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="841" parent="319" name="use_count">
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="842" parent="319" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="843" parent="319" name="IDX_DATASOURCE_RESOURCE_NAME">
      <ColNames>name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="844" parent="319" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="845" parent="319" name="IDX_DATASOURCE_RESOURCE_NAME">
      <UnderlyingIndexName>IDX_DATASOURCE_RESOURCE_NAME</UnderlyingIndexName>
    </key>
    <column id="846" parent="320" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="847" parent="320" name="type">
      <Comment>任务类型：\r\n1. 等待添加\r\n2. 等待删除3.已同步</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(4)|0s</StoredType>
    </column>
    <column id="848" parent="320" name="record">
      <Comment>操作的记录信息</Comment>
      <Position>3</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="849" parent="320" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="850" parent="320" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="851" parent="321" name="id">
      <Comment>记录ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="852" parent="321" name="name">
      <Comment>表名</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="853" parent="321" name="alias">
      <Comment>别名（中文）</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="854" parent="321" name="code">
      <Comment>资产编码</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="855" parent="321" name="datasource_id">
      <Comment>数据源ID</Comment>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="856" parent="321" name="remark">
      <Comment>备注</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="857" parent="321" name="catalog_id">
      <Comment>目录ID</Comment>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="858" parent="321" name="dataware_id">
      <Comment>仓库ID</Comment>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="859" parent="321" name="create_user">
      <Comment>create_user</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="860" parent="321" name="create_time">
      <Comment>create_time</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="861" parent="321" name="update_user">
      <Comment>update_user</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="862" parent="321" name="update_time">
      <Comment>update_time</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="863" parent="321" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="864" parent="321" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="865" parent="322" name="id">
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="866" parent="322" name="code">
      <Comment>资产编码</Comment>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="867" parent="322" name="name">
      <Comment>列名</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="868" parent="322" name="alisa">
      <Comment>列别名（中文名）</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="869" parent="322" name="datasource_id">
      <Comment>数据源ID（冗余）</Comment>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="870" parent="322" name="datatable_id">
      <Comment>表ID</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="871" parent="322" name="datatable_name">
      <Comment>表名称（冗余）</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="872" parent="322" name="type">
      <Comment>列类型</Comment>
      <Position>8</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="873" parent="322" name="length">
      <Comment>列长度</Comment>
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="874" parent="322" name="accuracy">
      <Comment>列精度(小数点)</Comment>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="875" parent="322" name="allow_empty">
      <Comment>允许空（0：不允许 1：允许）</Comment>
      <Position>11</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="876" parent="322" name="column_key">
      <Comment>是否键（0：不是 1：是）</Comment>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="877" parent="322" name="has_system">
      <Comment>是否同步字段（0：不是 1：是）</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="878" parent="322" name="remark">
      <Comment>备注</Comment>
      <Position>14</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="879" parent="322" name="create_user">
      <Comment>create_user</Comment>
      <Position>15</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="880" parent="322" name="create_time">
      <Comment>create_time</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="881" parent="322" name="update_user">
      <Comment>update_user</Comment>
      <Position>17</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="882" parent="322" name="update_time">
      <Comment>update_time</Comment>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="883" parent="322" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="884" parent="322" name="IDX_DATATABLE_COLUMN_DATATABLE_ID">
      <ColNames>datatable_id</ColNames>
      <Comment>&apos;&apos;&apos;外表ID&apos;&apos;&apos;</Comment>
      <Type>btree</Type>
    </index>
    <key id="885" parent="322" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="886" parent="323" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="887" parent="323" name="name">
      <Comment>库名</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="888" parent="323" name="code">
      <Comment>资产编码：DW-开头</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="889" parent="323" name="alias">
      <Comment>库别名</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="890" parent="323" name="type">
      <Comment>类型：TAB、IMG、LOG......</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="891" parent="323" name="sort_no">
      <Comment>排序</Comment>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="892" parent="323" name="remark">
      <Comment>备注</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="893" parent="323" name="datasource_type">
      <Comment>数据源类型（冗余）</Comment>
      <Position>8</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="894" parent="323" name="datasource_id">
      <Comment>数据源ID</Comment>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="895" parent="323" name="datasource_options">
      <Comment>数据源配置（冗余）</Comment>
      <Position>10</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="896" parent="323" name="deleted">
      <Comment>是否删除：0:否；1:是</Comment>
      <Position>11</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="897" parent="323" name="create_user">
      <Comment>create_user</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="898" parent="323" name="create_time">
      <Comment>create_time</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="899" parent="323" name="update_user">
      <Comment>update_user</Comment>
      <Position>14</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="900" parent="323" name="update_time">
      <Comment>update_time</Comment>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="901" parent="323" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="902" parent="323" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="903" parent="324" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="904" parent="324" name="name">
      <Comment>名称(调度策略名)</Comment>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="905" parent="324" name="cron_expr">
      <Comment>CRON表达式</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="906" parent="324" name="route_strategy">
      <Comment>路由策略</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="907" parent="324" name="block_strategy">
      <Comment>阻塞策略</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="908" parent="324" name="executor_timeout">
      <Comment>执行器超时</Comment>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="909" parent="324" name="executor_retry_count">
      <Comment>执行器失败重试</Comment>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="910" parent="324" name="started">
      <Comment>启动|停止</Comment>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="911" parent="324" name="dispatch_id">
      <Comment>调度器代理ID</Comment>
      <Position>9</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="912" parent="324" name="create_user">
      <Comment>create_user</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="913" parent="324" name="create_time">
      <Comment>create_time</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="914" parent="324" name="update_user">
      <Comment>update_user</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="915" parent="324" name="update_time">
      <Comment>update_time</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="916" parent="324" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="917" parent="324" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="918" parent="325" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="919" parent="325" name="assess_scheduler_id">
      <Comment>评分调度ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="920" parent="325" name="dataware_id">
      <Comment>库ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="921" parent="325" name="dataware_name">
      <Comment>库名（冗余）</Comment>
      <Position>4</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="922" parent="325" name="datatable_id">
      <Comment>表ID（DT-类型时为空）</Comment>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="923" parent="325" name="datatable_name">
      <Comment>表名（冗余）</Comment>
      <Position>6</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="924" parent="325" name="dim_code">
      <Comment>质量维度编码</Comment>
      <Position>7</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="925" parent="325" name="type">
      <Comment>类型（DW：库得分，DT：表得分，DW-DIM：库的维度，DT-DIM：表的维度）</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="926" parent="325" name="score_date">
      <Comment>质量得分日期</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="927" parent="325" name="score_value">
      <Comment>质量得分值</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="928" parent="325" name="assess_time">
      <Comment>实际评分时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="929" parent="325" name="assess_status">
      <Comment>实际评分状态（0：失败，1：成功）</Comment>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="930" parent="325" name="assess_report">
      <Comment>实际评分报告</Comment>
      <Position>13</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="931" parent="325" name="create_time">
      <Comment>create_time</Comment>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="932" parent="325" name="update_time">
      <Comment>update_time</Comment>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="933" parent="325" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="934" parent="325" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="935" parent="326" name="code">
      <Comment>编码</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="936" parent="326" name="name">
      <Comment>名称</Comment>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="937" parent="326" name="weight">
      <Comment>权重系数</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="938" parent="326" name="remark">
      <Comment>备注</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="939" parent="326" name="update_user">
      <Comment>update_user</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="940" parent="326" name="update_time">
      <Comment>update_time</Comment>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="941" parent="326" name="PRIMARY">
      <ColNames>code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="942" parent="326" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="943" parent="327" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="944" parent="327" name="dataware_id">
      <Comment>仓库ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="945" parent="327" name="datatable_id">
      <Comment>数据表ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="946" parent="327" name="datatable_name">
      <Comment>数据表名（冗余）</Comment>
      <Position>4</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="947" parent="327" name="rule_name">
      <Comment>规则名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="948" parent="327" name="rule_type">
      <Comment>规则类型（TMPL：系统模板，SQL：用户自定义SQL）</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="949" parent="327" name="rule_level">
      <Comment>规则级别（TAB：表级；COL：字段级）</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="950" parent="327" name="rule_tmpl_id">
      <Comment>规则模板ID</Comment>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="951" parent="327" name="rule_targets">
      <Comment>规则数据对象</Comment>
      <Position>9</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="952" parent="327" name="rule_options">
      <Comment>规则其他配置项</Comment>
      <Position>10</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="953" parent="327" name="rule_sql_expr">
      <Comment>规则自定义SQL表达式</Comment>
      <Position>11</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="954" parent="327" name="rule_range_expr">
      <Comment>规则数据范围表达式</Comment>
      <Position>12</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="955" parent="327" name="dim_code">
      <Comment>质量维度编码</Comment>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="956" parent="327" name="check_type">
      <Comment>检查(值比较)方式（FIXED：固定值，WAVED：波动值(区分正向、负向评分)）</Comment>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="957" parent="327" name="check_options">
      <Comment>检查的配置项</Comment>
      <Position>15</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="958" parent="327" name="remark">
      <Comment>备注</Comment>
      <Position>16</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="959" parent="327" name="disabled">
      <Comment>启用|禁用（0：启用，1：禁用）</Comment>
      <Position>17</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="960" parent="327" name="create_user">
      <Comment>create_user</Comment>
      <Position>18</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="961" parent="327" name="create_time">
      <Comment>create_time</Comment>
      <Position>19</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="962" parent="327" name="update_user">
      <Comment>update_user</Comment>
      <Position>20</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="963" parent="327" name="update_time">
      <Comment>update_time</Comment>
      <Position>21</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="964" parent="327" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="965" parent="327" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="966" parent="328" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="967" parent="328" name="rule_id">
      <Comment>质量规则ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="968" parent="328" name="dim_code">
      <Comment>质量维度编码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="969" parent="328" name="rule_scheduler_id">
      <Comment>质量规则调度ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="970" parent="328" name="dataware_id">
      <Comment>仓库ID</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="971" parent="328" name="datatable_id">
      <Comment>表ID</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="972" parent="328" name="rule_level">
      <Comment>规则级别</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="973" parent="328" name="rule_targets">
      <Comment>规则对象(冗余)</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="974" parent="328" name="rule_hashcode">
      <Comment>规则唯一标识</Comment>
      <Position>9</Position>
      <StoredType>varchar(36)|0s</StoredType>
    </column>
    <column id="975" parent="328" name="metric_date">
      <Comment>质量指标日期</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="976" parent="328" name="metric_value">
      <Comment>质量指标值</Comment>
      <Position>11</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="977" parent="328" name="warn_status">
      <Comment>规则告警状态(0:无，1：告警)</Comment>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="978" parent="328" name="check_time">
      <Comment>规则运行时间</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="979" parent="328" name="check_status">
      <Comment>规则运行状态（0,失败；1，成功）</Comment>
      <Position>14</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="980" parent="328" name="check_report">
      <Comment>规则运行报告</Comment>
      <Position>15</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="981" parent="328" name="create_time">
      <Comment>create_time</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="982" parent="328" name="update_time">
      <Comment>update_time</Comment>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="983" parent="328" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="984" parent="328" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="985" parent="329" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="986" parent="329" name="dataware_id">
      <Comment>仓库ID</Comment>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="987" parent="329" name="datatable_id">
      <Comment>数据表ID</Comment>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="988" parent="329" name="datatable_name">
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="989" parent="329" name="name">
      <Comment>名称(调度策略名)</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="990" parent="329" name="type">
      <Comment>类型（CRON：离线周期，TASK：关联生产任务）</Comment>
      <Position>6</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="991" parent="329" name="cron_expr">
      <Comment>CRON表达式</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="992" parent="329" name="task_id">
      <Comment>关联生产任务ID</Comment>
      <Position>8</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="993" parent="329" name="route_strategy">
      <Comment>路由策略</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="994" parent="329" name="block_strategy">
      <Comment>阻塞策略</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="995" parent="329" name="executor_timeout">
      <Comment>执行器超时</Comment>
      <Position>11</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="996" parent="329" name="executor_retry_count">
      <Comment>执行器失败重试</Comment>
      <Position>12</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="997" parent="329" name="started">
      <Comment>启动|停止（0：停止，1：启动）</Comment>
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="998" parent="329" name="dispatch_id">
      <Comment>调度器代理ID</Comment>
      <Position>14</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="999" parent="329" name="create_user">
      <Comment>create_user</Comment>
      <Position>15</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1000" parent="329" name="create_time">
      <Comment>create_time</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1001" parent="329" name="update_user">
      <Comment>update_user</Comment>
      <Position>17</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1002" parent="329" name="update_time">
      <Comment>update_time</Comment>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1003" parent="329" name="execute_params">
      <Comment>用户执行参数</Comment>
      <Position>19</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="1004" parent="329" name="call_back_params">
      <Comment>任务回调参数</Comment>
      <Position>20</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <index id="1005" parent="329" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1006" parent="329" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1007" parent="330" name="id">
      <Comment>模板ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1008" parent="330" name="type">
      <Comment>模板类型（SYS：系统，USR：用户自定义）</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1009" parent="330" name="rule_name">
      <Comment>规则名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1010" parent="330" name="rule_level">
      <Comment>规则级别（TAB：表级，COL：字段）</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1011" parent="330" name="rule_options_def">
      <Comment>规则配置项定义</Comment>
      <Position>5</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1012" parent="330" name="rule_func_expr">
      <Comment>函数调用表达式</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1013" parent="330" name="dim_code">
      <Comment>质量维度编码</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1014" parent="330" name="check_type">
      <Comment>合规检查(值比较)方式</Comment>
      <Position>8</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1015" parent="330" name="check_options_def">
      <Comment>合规检查配置项（扩展）</Comment>
      <Position>9</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1016" parent="330" name="remark">
      <Comment>备注</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1017" parent="330" name="create_user">
      <Comment>create_user</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1018" parent="330" name="create_time">
      <Comment>create_time</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1019" parent="330" name="update_user">
      <Comment>update_user</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1020" parent="330" name="update_time">
      <Comment>update_time</Comment>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1021" parent="330" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1022" parent="330" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1023" parent="331" name="id">
      <Position>1</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1024" parent="331" name="registered_client_id">
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1025" parent="331" name="principal_name">
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1026" parent="331" name="authorization_grant_type">
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1027" parent="331" name="authorized_scopes">
      <Position>5</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1028" parent="331" name="attributes">
      <Position>6</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1029" parent="331" name="state">
      <Position>7</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1030" parent="331" name="authorization_code_value">
      <Position>8</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1031" parent="331" name="authorization_code_issued_at">
      <Position>9</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1032" parent="331" name="authorization_code_expires_at">
      <Position>10</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1033" parent="331" name="authorization_code_metadata">
      <Position>11</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1034" parent="331" name="access_token_value">
      <Position>12</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1035" parent="331" name="access_token_issued_at">
      <Position>13</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1036" parent="331" name="access_token_expires_at">
      <Position>14</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1037" parent="331" name="access_token_metadata">
      <Position>15</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1038" parent="331" name="access_token_type">
      <Position>16</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1039" parent="331" name="access_token_scopes">
      <Position>17</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1040" parent="331" name="oidc_id_token_value">
      <Position>18</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1041" parent="331" name="oidc_id_token_issued_at">
      <Position>19</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1042" parent="331" name="oidc_id_token_expires_at">
      <Position>20</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1043" parent="331" name="oidc_id_token_metadata">
      <Position>21</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1044" parent="331" name="refresh_token_value">
      <Position>22</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1045" parent="331" name="refresh_token_issued_at">
      <Position>23</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1046" parent="331" name="refresh_token_expires_at">
      <Position>24</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1047" parent="331" name="refresh_token_metadata">
      <Position>25</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1048" parent="332" name="registered_client_id">
      <Position>1</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1049" parent="332" name="principal_name">
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1050" parent="332" name="authorities">
      <Position>3</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1051" parent="333" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1052" parent="333" name="app_key">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1053" parent="333" name="secret">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1054" parent="333" name="app_name">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1055" parent="333" name="app_status">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1056" parent="333" name="check_service">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1057" parent="333" name="check_device">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1058" parent="333" name="update_time">
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1059" parent="333" name="create_time">
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1060" parent="333" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1061" parent="333" name="uni_key">
      <ColNames>app_key</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1062" parent="333" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1063" parent="333" name="uni_key">
      <UnderlyingIndexName>uni_key</UnderlyingIndexName>
    </key>
    <column id="1064" parent="334" name="app_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1065" parent="334" name="device_id">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <index id="1066" parent="334" name="PRIMARY">
      <ColNames>app_id
device_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1067" parent="334" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1068" parent="335" name="app_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1069" parent="335" name="service_id">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <index id="1070" parent="335" name="PRIMARY">
      <ColNames>app_id
service_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1071" parent="335" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1072" parent="336" name="id">
      <Comment>部门表主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1073" parent="336" name="code">
      <Comment>部门编码</Comment>
      <Position>2</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1074" parent="336" name="name">
      <Comment>部门名称</Comment>
      <Position>3</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1075" parent="336" name="parent_id">
      <Comment>父部门ID</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1076" parent="336" name="create_time">
      <Comment>操作时间</Comment>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1077" parent="336" name="create_user">
      <Position>6</Position>
      <StoredType>varchar(35)|0s</StoredType>
    </column>
    <column id="1078" parent="336" name="update_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1079" parent="336" name="update_user">
      <Position>8</Position>
      <StoredType>varchar(35)|0s</StoredType>
    </column>
    <column id="1080" parent="336" name="sort_no">
      <Position>9</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1081" parent="336" name="remark">
      <Comment>描述</Comment>
      <Position>10</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1082" parent="336" name="airport">
      <Position>11</Position>
      <StoredType>varchar(4)|0s</StoredType>
    </column>
    <column id="1083" parent="336" name="tenant_id">
      <Comment>租户ID</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1084" parent="336" name="airlines">
      <Position>13</Position>
      <StoredType>varchar(2)|0s</StoredType>
    </column>
    <column id="1085" parent="336" name="xfield1">
      <Position>14</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1086" parent="336" name="xfield2">
      <Position>15</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1087" parent="336" name="xfield3">
      <Position>16</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1088" parent="336" name="xfield4">
      <Position>17</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1089" parent="336" name="xfield5">
      <Position>18</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1090" parent="336" name="xfield6">
      <Position>19</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1091" parent="336" name="xfield7">
      <Position>20</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1092" parent="336" name="xfield8">
      <Position>21</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1093" parent="336" name="xfield9">
      <Position>22</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1094" parent="336" name="xfield10">
      <Position>23</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1095" parent="336" name="xfield11">
      <Position>24</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1096" parent="336" name="xfield12">
      <Position>25</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="1097" parent="336" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1098" parent="336" name="Index_deptname">
      <ColNames>name</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1099" parent="336" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1100" parent="337" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1101" parent="337" name="device_code">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1102" parent="337" name="device_name">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1103" parent="337" name="remark">
      <Position>4</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="1104" parent="337" name="device_status">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1105" parent="337" name="update_time">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1106" parent="337" name="create_time">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1107" parent="337" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1108" parent="337" name="uni_code">
      <ColNames>device_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1109" parent="337" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1110" parent="337" name="uni_code">
      <UnderlyingIndexName>uni_code</UnderlyingIndexName>
    </key>
    <column id="1111" parent="338" name="id">
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1112" parent="338" name="tablename">
      <Comment>表名</Comment>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1113" parent="338" name="name">
      <Comment>属性名</Comment>
      <Position>3</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1114" parent="338" name="datatype">
      <Comment>属性类型</Comment>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1115" parent="338" name="mapping_name">
      <Comment>映射字段名</Comment>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <index id="1116" parent="338" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1117" parent="338" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1118" parent="339" name="id">
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1119" parent="339" name="system_id">
      <Comment>系统ID</Comment>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1120" parent="339" name="code">
      <Comment>资源编码</Comment>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1121" parent="339" name="path">
      <Comment>资源路径</Comment>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1122" parent="339" name="name">
      <Comment>资源名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1123" parent="339" name="parent_id">
      <Comment>父资源id</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1124" parent="339" name="type">
      <Comment>资源类型</Comment>
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1125" parent="339" name="status">
      <Comment>资源状态</Comment>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1126" parent="339" name="sort_no">
      <Comment>排序号</Comment>
      <Position>9</Position>
      <StoredType>varchar(300)|0s</StoredType>
    </column>
    <column id="1127" parent="339" name="remark">
      <Comment>资源描述</Comment>
      <Position>10</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1128" parent="339" name="expanded_icon">
      <Comment>展开图标</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1129" parent="339" name="unexpanded_icon">
      <Comment>关闭图标</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1130" parent="339" name="load_type">
      <Comment>0：全部，1：web  2：移动</Comment>
      <Position>13</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1131" parent="339" name="create_time">
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1132" parent="339" name="create_user">
      <Position>15</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1133" parent="339" name="update_time">
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1134" parent="339" name="update_user">
      <Position>17</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="1135" parent="339" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1136" parent="339" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1137" parent="340" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1138" parent="340" name="menu_id">
      <Comment>菜单id</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1139" parent="340" name="operation_id">
      <Comment>服务id</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="1140" parent="340" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1141" parent="340" name="uni_menu_opera">
      <ColNames>menu_id
operation_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1142" parent="340" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1143" parent="340" name="uni_menu_opera">
      <UnderlyingIndexName>uni_menu_opera</UnderlyingIndexName>
    </key>
    <column id="1144" parent="341" name="id">
      <Position>1</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1145" parent="341" name="registered_client_id">
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1146" parent="341" name="principal_name">
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1147" parent="341" name="authorization_grant_type">
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1148" parent="341" name="authorized_scopes">
      <Position>5</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1149" parent="341" name="attributes">
      <Position>6</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1150" parent="341" name="state">
      <Position>7</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1151" parent="341" name="authorization_code_value">
      <Position>8</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1152" parent="341" name="authorization_code_issued_at">
      <Position>9</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1153" parent="341" name="authorization_code_expires_at">
      <Position>10</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1154" parent="341" name="authorization_code_metadata">
      <Position>11</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1155" parent="341" name="access_token_value">
      <Position>12</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1156" parent="341" name="access_token_issued_at">
      <Position>13</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1157" parent="341" name="access_token_expires_at">
      <Position>14</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1158" parent="341" name="access_token_metadata">
      <Position>15</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1159" parent="341" name="access_token_type">
      <Position>16</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1160" parent="341" name="access_token_scopes">
      <Position>17</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1161" parent="341" name="oidc_id_token_value">
      <Position>18</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1162" parent="341" name="oidc_id_token_issued_at">
      <Position>19</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1163" parent="341" name="oidc_id_token_expires_at">
      <Position>20</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1164" parent="341" name="oidc_id_token_metadata">
      <Position>21</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1165" parent="341" name="refresh_token_value">
      <Position>22</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1166" parent="341" name="refresh_token_issued_at">
      <Position>23</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1167" parent="341" name="refresh_token_expires_at">
      <Position>24</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1168" parent="341" name="refresh_token_metadata">
      <Position>25</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1169" parent="342" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1170" parent="342" name="client_id">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1171" parent="342" name="client_id_issued_at">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1172" parent="342" name="client_secret">
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1173" parent="342" name="client_secret_expired_at">
      <Position>5</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1174" parent="342" name="client_name">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1175" parent="342" name="client_authentication_methods">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1176" parent="342" name="authorization_grant_types">
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1177" parent="342" name="redirect_uris">
      <Position>9</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1178" parent="342" name="scopes">
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1179" parent="342" name="client_settings">
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="1180" parent="342" name="token_settings">
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="1181" parent="342" name="remark">
      <Position>13</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="1182" parent="342" name="status">
      <Position>14</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1183" parent="342" name="deleted">
      <Position>15</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1184" parent="342" name="created_by">
      <Position>16</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1185" parent="342" name="created_at">
      <Position>17</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1186" parent="342" name="updated_by">
      <Position>18</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1187" parent="342" name="updated_at">
      <Position>19</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1188" parent="343" name="id">
      <Comment>主键id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1189" parent="343" name="code">
      <Comment>服务编码</Comment>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1190" parent="343" name="name">
      <Comment>服务名称</Comment>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1191" parent="343" name="path">
      <Comment>服务路径</Comment>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1192" parent="343" name="version">
      <Comment>版本号</Comment>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1193" parent="343" name="status">
      <Comment>状态：0禁用，1启用</Comment>
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <index id="1194" parent="343" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1195" parent="343" name="Index_rightname">
      <ColNames>version</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1196" parent="343" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1197" parent="344" name="id">
      <Comment>角色表主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1198" parent="344" name="code">
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1199" parent="344" name="name">
      <Comment>角色名称</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1200" parent="344" name="type">
      <Comment>角色类型</Comment>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1201" parent="344" name="create_time">
      <Comment>创建时间</Comment>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1202" parent="344" name="create_user">
      <Position>6</Position>
      <StoredType>varchar(35)|0s</StoredType>
    </column>
    <column id="1203" parent="344" name="remark">
      <Comment>描述</Comment>
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1204" parent="344" name="tenant_id">
      <Comment>租户id</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="1205" parent="344" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1206" parent="344" name="Index_rolename">
      <ColNames>name</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1207" parent="344" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1208" parent="345" name="id">
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1209" parent="345" name="role_id">
      <Comment>角色id</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1210" parent="345" name="menu_id">
      <Comment>菜单id</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1211" parent="345" name="sort_no">
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <index id="1212" parent="345" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1213" parent="345" name="uni_role_menu">
      <ColNames>role_id
menu_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1214" parent="345" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1215" parent="345" name="uni_role_menu">
      <UnderlyingIndexName>uni_role_menu</UnderlyingIndexName>
    </key>
    <column id="1216" parent="346" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1217" parent="346" name="category">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1218" parent="346" name="service_name">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1219" parent="346" name="uri">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="1220" parent="346" name="service_status">
      <Comment>0 未生效 / 1 生效</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1221" parent="346" name="update_time">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1222" parent="346" name="create_time">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1223" parent="346" name="visit_count">
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <index id="1224" parent="346" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1225" parent="346" name="uni_uri">
      <ColNames>uri</ColNames>
      <PrefixLengths>255</PrefixLengths>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1226" parent="346" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1227" parent="346" name="uni_uri">
      <UnderlyingIndexName>uni_uri</UnderlyingIndexName>
    </key>
    <column id="1228" parent="347" name="id">
      <Comment>主键id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1229" parent="347" name="code">
      <Comment>系统编码</Comment>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1230" parent="347" name="remark">
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1231" parent="347" name="name">
      <Comment>系统名称</Comment>
      <Position>4</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1232" parent="347" name="parent_id">
      <Comment>父级id</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1233" parent="347" name="type">
      <Comment>类型</Comment>
      <Position>6</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1234" parent="347" name="status">
      <Comment>系统状态 1：启用，0：禁用</Comment>
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <index id="1235" parent="347" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1236" parent="347" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1237" parent="348" name="id">
      <Comment>租户表id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1238" parent="348" name="code">
      <Comment>租户编码</Comment>
      <Position>2</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1239" parent="348" name="name">
      <Comment>租户名称</Comment>
      <Position>3</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1240" parent="348" name="remark">
      <Comment>描述</Comment>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1241" parent="348" name="status">
      <Comment>租户状态(0.禁用，1.启用)</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1242" parent="348" name="tenant_admin_account">
      <Comment>租户初始管理员账号</Comment>
      <Position>6</Position>
      <StoredType>varchar(35)|0s</StoredType>
    </column>
    <index id="1243" parent="348" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1244" parent="348" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1245" parent="349" name="id">
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1246" parent="349" name="type">
      <Comment>操作模块类型</Comment>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1247" parent="349" name="target_id">
      <Comment>操作对象id</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1248" parent="349" name="old_value">
      <Comment>旧值</Comment>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1249" parent="349" name="new_value">
      <Comment>新值</Comment>
      <Position>5</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1250" parent="349" name="operator">
      <Comment>操作人</Comment>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1251" parent="349" name="operate_time">
      <Comment>操作时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1252" parent="349" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1253" parent="349" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1254" parent="350" name="id">
      <Comment>用户表主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1255" parent="350" name="username">
      <Comment>用户名</Comment>
      <Position>2</Position>
      <StoredType>varchar(35)|0s</StoredType>
    </column>
    <column id="1256" parent="350" name="fullname">
      <Position>3</Position>
      <StoredType>varchar(25)|0s</StoredType>
    </column>
    <column id="1257" parent="350" name="password">
      <Comment>密码</Comment>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1258" parent="350" name="telephone">
      <Comment>电话</Comment>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1259" parent="350" name="mail">
      <Comment>邮箱</Comment>
      <Position>6</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1260" parent="350" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1261" parent="350" name="create_user">
      <Position>8</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1262" parent="350" name="update_time">
      <Comment>操作时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1263" parent="350" name="update_user">
      <Position>10</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1264" parent="350" name="tenant_id">
      <Comment>租户id</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1265" parent="350" name="admin">
      <Comment>是否超级管理员</Comment>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1266" parent="350" name="status">
      <Comment>状态</Comment>
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1267" parent="350" name="xfield1">
      <Position>14</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1268" parent="350" name="xfield2">
      <Position>15</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1269" parent="350" name="xfield3">
      <Position>16</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1270" parent="350" name="xfield4">
      <Position>17</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1271" parent="350" name="xfield5">
      <Position>18</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1272" parent="350" name="xfield6">
      <Position>19</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1273" parent="350" name="xfield7">
      <Position>20</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1274" parent="350" name="xfield8">
      <Position>21</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1275" parent="350" name="xfield9">
      <Position>22</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1276" parent="350" name="xfield10">
      <Position>23</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1277" parent="350" name="xfield11">
      <Position>24</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1278" parent="350" name="xfield12">
      <Position>25</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1279" parent="350" name="PWD_INVALID_TIME">
      <Position>26</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1280" parent="350" name="PWD_MIN_USAGE_TIME">
      <Position>27</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1281" parent="350" name="LOCKED">
      <Position>28</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1282" parent="350" name="LOCKED_TIME">
      <Position>29</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1283" parent="350" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1284" parent="350" name="Index_username">
      <ColNames>username</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1285" parent="350" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1286" parent="350" name="Index_username">
      <UnderlyingIndexName>Index_username</UnderlyingIndexName>
    </key>
    <column id="1287" parent="351" name="id">
      <Comment>主键id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1288" parent="351" name="user_id">
      <Comment>用户id</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1289" parent="351" name="dept_id">
      <Comment>部门id</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="1290" parent="351" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1291" parent="351" name="uni_user_dept">
      <ColNames>user_id
dept_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1292" parent="351" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1293" parent="351" name="uni_user_dept">
      <UnderlyingIndexName>uni_user_dept</UnderlyingIndexName>
    </key>
    <column id="1294" parent="352" name="ID">
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(36)|0s</StoredType>
    </column>
    <column id="1295" parent="352" name="USER_ID">
      <Comment>用户表主键</Comment>
      <Position>2</Position>
      <StoredType>varchar(36)|0s</StoredType>
    </column>
    <column id="1296" parent="352" name="USER_NAME">
      <Comment>用户名</Comment>
      <Position>3</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1297" parent="352" name="USER_PWD">
      <Comment>用户表密码</Comment>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1298" parent="352" name="CREATE_USER">
      <Comment>创建人</Comment>
      <Position>5</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1299" parent="352" name="CREATE_TIME">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1300" parent="352" name="PRIMARY">
      <ColNames>ID</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1301" parent="352" name="IDX_USER_ID">
      <ColNames>USER_ID</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1302" parent="352" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1303" parent="353" name="id">
      <Comment>主键id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1304" parent="353" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1305" parent="353" name="role_id">
      <Comment>角色id</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="1306" parent="353" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1307" parent="353" name="uni_user_role">
      <ColNames>user_id
role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1308" parent="353" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1309" parent="353" name="uni_user_role">
      <UnderlyingIndexName>uni_user_role</UnderlyingIndexName>
    </key>
    <column id="1310" parent="354" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1311" parent="354" name="username">
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1312" parent="354" name="user_id">
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1313" parent="354" name="system_id">
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1314" parent="354" name="system_code">
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1315" parent="354" name="update_by">
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1316" parent="354" name="update_time">
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1317" parent="354" name="create_time">
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1318" parent="354" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1319" parent="354" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1320" parent="355" name="ID">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(36)|0s</StoredType>
    </column>
    <column id="1321" parent="355" name="XFIELD_NAME">
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1322" parent="355" name="XATTR_NAME">
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1323" parent="355" name="COMP_TYPE">
      <Position>4</Position>
      <StoredType>varchar(2)|0s</StoredType>
    </column>
    <column id="1324" parent="355" name="DATA_OPTIONS">
      <Position>5</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1325" parent="355" name="VALIDATOR_OPTIONS">
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1326" parent="355" name="DEFAULT_VALUE">
      <Position>7</Position>
      <StoredType>varchar(250)|0s</StoredType>
    </column>
    <column id="1327" parent="355" name="READONLY">
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1328" parent="355" name="HIDDEN">
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1329" parent="355" name="FILTERABLE">
      <Position>10</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1330" parent="355" name="FILTER_OPTIONS">
      <Position>11</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1331" parent="355" name="LISTABLE">
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1332" parent="355" name="LIST_OPTIONS">
      <Position>13</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1333" parent="355" name="XFIELD_TABLE">
      <Position>14</Position>
      <StoredType>varchar(250)|0s</StoredType>
    </column>
    <column id="1334" parent="355" name="SORT_NO">
      <Position>15</Position>
      <StoredType>decimal(8)|0s</StoredType>
    </column>
    <index id="1335" parent="355" name="PRIMARY">
      <ColNames>ID</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1336" parent="355" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1337" parent="356" name="id">
      <AutoIncrement>4</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1338" parent="356" name="app_name">
      <Comment>执行器AppName</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1339" parent="356" name="title">
      <Comment>执行器名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(12)|0s</StoredType>
    </column>
    <column id="1340" parent="356" name="address_type">
      <Comment>执行器地址类型：0=自动注册、1=手动录入</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1341" parent="356" name="address_list">
      <Comment>执行器地址列表，多地址逗号分隔</Comment>
      <Position>5</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1342" parent="356" name="update_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1343" parent="356" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1344" parent="356" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1345" parent="357" name="id">
      <AutoIncrement>65</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1346" parent="357" name="job_group">
      <Comment>执行器主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1347" parent="357" name="job_desc">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1348" parent="357" name="add_time">
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1349" parent="357" name="update_time">
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1350" parent="357" name="author">
      <Comment>作者</Comment>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1351" parent="357" name="alarm_email">
      <Comment>报警邮件</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1352" parent="357" name="schedule_type">
      <Comment>调度类型</Comment>
      <DefaultExpression>&apos;NONE&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1353" parent="357" name="schedule_conf">
      <Comment>调度配置，值含义取决于调度类型</Comment>
      <Position>9</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="1354" parent="357" name="misfire_strategy">
      <Comment>调度过期策略</Comment>
      <DefaultExpression>&apos;DO_NOTHING&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1355" parent="357" name="executor_route_strategy">
      <Comment>执行器路由策略</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1356" parent="357" name="executor_handler">
      <Comment>执行器任务handler</Comment>
      <Position>12</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1357" parent="357" name="executor_param">
      <Comment>执行器任务参数</Comment>
      <Position>13</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="1358" parent="357" name="executor_block_strategy">
      <Comment>阻塞处理策略</Comment>
      <Position>14</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1359" parent="357" name="executor_timeout">
      <Comment>任务执行超时时间，单位秒</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1360" parent="357" name="executor_fail_retry_count">
      <Comment>失败重试次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1361" parent="357" name="glue_type">
      <Comment>GLUE类型</Comment>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1362" parent="357" name="glue_source">
      <Comment>GLUE源代码</Comment>
      <Position>18</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1363" parent="357" name="glue_remark">
      <Comment>GLUE备注</Comment>
      <Position>19</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="1364" parent="357" name="glue_updatetime">
      <Comment>GLUE更新时间</Comment>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1365" parent="357" name="child_jobid">
      <Comment>子任务ID，多个逗号分隔</Comment>
      <Position>21</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1366" parent="357" name="trigger_status">
      <Comment>调度状态：0-停止，1-运行</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1367" parent="357" name="trigger_last_time">
      <Comment>上次调度时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>bigint(13)|0s</StoredType>
    </column>
    <column id="1368" parent="357" name="trigger_next_time">
      <Comment>下次调度时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>bigint(13)|0s</StoredType>
    </column>
    <index id="1369" parent="357" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1370" parent="357" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1371" parent="358" name="lock_name">
      <Comment>锁名称</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="1372" parent="358" name="PRIMARY">
      <ColNames>lock_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1373" parent="358" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1374" parent="359" name="id">
      <AutoIncrement>74380</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1375" parent="359" name="job_group">
      <Comment>执行器主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1376" parent="359" name="job_id">
      <Comment>任务，主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1377" parent="359" name="executor_address">
      <Comment>执行器地址，本次执行的地址</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1378" parent="359" name="executor_handler">
      <Comment>执行器任务handler</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1379" parent="359" name="executor_param">
      <Comment>执行器任务参数</Comment>
      <Position>6</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="1380" parent="359" name="executor_sharding_param">
      <Comment>执行器任务分片参数，格式如 1/2</Comment>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1381" parent="359" name="executor_fail_retry_count">
      <Comment>失败重试次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1382" parent="359" name="trigger_time">
      <Comment>调度-时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1383" parent="359" name="trigger_code">
      <Comment>调度-结果</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1384" parent="359" name="trigger_msg">
      <Comment>调度-日志</Comment>
      <Position>11</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1385" parent="359" name="handle_time">
      <Comment>执行-时间</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1386" parent="359" name="handle_code">
      <Comment>执行-状态</Comment>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1387" parent="359" name="handle_msg">
      <Comment>执行-日志</Comment>
      <Position>14</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1388" parent="359" name="alarm_status">
      <Comment>告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <index id="1389" parent="359" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1390" parent="359" name="I_jobid_jobgroup">
      <ColNames>job_id
job_group</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1391" parent="359" name="I_job_id">
      <ColNames>job_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1392" parent="359" name="I_trigger_time">
      <ColNames>trigger_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1393" parent="359" name="I_handle_code">
      <ColNames>handle_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1394" parent="359" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1395" parent="360" name="id">
      <AutoIncrement>76</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1396" parent="360" name="trigger_day">
      <Comment>调度-时间</Comment>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1397" parent="360" name="running_count">
      <Comment>运行中-日志数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1398" parent="360" name="suc_count">
      <Comment>执行成功-日志数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1399" parent="360" name="fail_count">
      <Comment>执行失败-日志数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1400" parent="360" name="update_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1401" parent="360" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1402" parent="360" name="i_trigger_day">
      <ColNames>trigger_day</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1403" parent="360" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1404" parent="360" name="i_trigger_day">
      <UnderlyingIndexName>i_trigger_day</UnderlyingIndexName>
    </key>
    <column id="1405" parent="361" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1406" parent="361" name="job_id">
      <Comment>任务，主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1407" parent="361" name="glue_type">
      <Comment>GLUE类型</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1408" parent="361" name="glue_source">
      <Comment>GLUE源代码</Comment>
      <Position>4</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1409" parent="361" name="glue_remark">
      <Comment>GLUE备注</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="1410" parent="361" name="add_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1411" parent="361" name="update_time">
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1412" parent="361" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1413" parent="361" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1414" parent="362" name="id">
      <AutoIncrement>352360</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1415" parent="362" name="registry_group">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1416" parent="362" name="registry_key">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1417" parent="362" name="registry_value">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1418" parent="362" name="update_time">
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1419" parent="362" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1420" parent="362" name="i_g_k_v">
      <ColNames>registry_group
registry_key
registry_value</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1421" parent="362" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1422" parent="362" name="i_g_k_v">
      <UnderlyingIndexName>i_g_k_v</UnderlyingIndexName>
    </key>
    <column id="1423" parent="363" name="id">
      <AutoIncrement>2</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1424" parent="363" name="username">
      <Comment>账号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1425" parent="363" name="password">
      <Comment>密码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1426" parent="363" name="role">
      <Comment>角色：0-普通用户、1-管理员</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1427" parent="363" name="permission">
      <Comment>权限：执行器ID列表，多个逗号分割</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="1428" parent="363" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1429" parent="363" name="i_username">
      <ColNames>username</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1430" parent="363" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1431" parent="363" name="i_username">
      <UnderlyingIndexName>i_username</UnderlyingIndexName>
    </key>
    <column id="1432" parent="364" name="id">
      <AutoIncrement>11</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1433" parent="364" name="app_name">
      <Comment>执行器AppName</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1434" parent="364" name="title">
      <Comment>执行器名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(12)|0s</StoredType>
    </column>
    <column id="1435" parent="364" name="address_type">
      <Comment>执行器地址类型：0=自动注册、1=手动录入</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1436" parent="364" name="address_list">
      <Comment>执行器地址列表，多地址逗号分隔</Comment>
      <Position>5</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1437" parent="364" name="update_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1438" parent="364" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1439" parent="364" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1440" parent="365" name="id">
      <AutoIncrement>70</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1441" parent="365" name="xxl_job_info_id">
      <Comment>调度任务表ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1442" parent="365" name="inc_mode">
      <Comment>增量的方式</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1443" parent="365" name="primary_key">
      <Comment>增量使用的键</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1444" parent="365" name="inc_start_time">
      <Comment>增量初始时间，时间增量</Comment>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1445" parent="365" name="replace_param_type">
      <Comment>增量时间格式</Comment>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1446" parent="365" name="inc_start_value">
      <Comment>增量初始值，ID值增量</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1447" parent="365" name="partition_info">
      <Comment>分区信息，Hive</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1448" parent="365" name="replace_param">
      <Comment>Datax动态参数</Comment>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1449" parent="365" name="reader_table">
      <Comment>增量表</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1450" parent="365" name="datasource_id">
      <Comment>数据源ID</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1451" parent="365" name="max_id">
      <Comment>最大值</Comment>
      <Position>12</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="1452" parent="365" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1453" parent="365" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1454" parent="366" name="id">
      <AutoIncrement>2144727778</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="1455" parent="366" name="job_group">
      <Comment>执行器主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1456" parent="366" name="job_desc">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1457" parent="366" name="job_task_type">
      <Comment>Job任务类型</Comment>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1458" parent="366" name="add_time">
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1459" parent="366" name="update_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1460" parent="366" name="author">
      <Comment>作者</Comment>
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1461" parent="366" name="alarm_email">
      <Comment>报警邮件</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1462" parent="366" name="schedule_type">
      <Comment>调度类型</Comment>
      <DefaultExpression>&apos;NONE&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1463" parent="366" name="schedule_conf">
      <Comment>调度配置，值含义取决于调度类型</Comment>
      <Position>10</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="1464" parent="366" name="misfire_strategy">
      <Comment>调度过期策略</Comment>
      <DefaultExpression>&apos;DO_NOTHING&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1465" parent="366" name="executor_route_strategy">
      <Comment>执行器路由策略</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1466" parent="366" name="executor_handler">
      <Comment>执行器任务handler</Comment>
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1467" parent="366" name="executor_param">
      <Comment>执行器任务参数</Comment>
      <Position>14</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1468" parent="366" name="executor_block_strategy">
      <Comment>阻塞处理策略</Comment>
      <Position>15</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1469" parent="366" name="executor_timeout">
      <Comment>任务执行超时时间，单位秒</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1470" parent="366" name="executor_fail_retry_count">
      <Comment>失败重试次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1471" parent="366" name="glue_type">
      <Comment>GLUE类型</Comment>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1472" parent="366" name="glue_source">
      <Comment>GLUE源代码</Comment>
      <Position>19</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1473" parent="366" name="glue_remark">
      <Comment>GLUE备注</Comment>
      <Position>20</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="1474" parent="366" name="glue_updatetime">
      <Comment>GLUE更新时间</Comment>
      <Position>21</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1475" parent="366" name="child_jobid">
      <Comment>子任务ID，多个逗号分隔</Comment>
      <Position>22</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1476" parent="366" name="trigger_status">
      <Comment>调度状态：0-停止，1-运行</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1477" parent="366" name="trigger_last_time">
      <Comment>上次调度时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1478" parent="366" name="trigger_next_time">
      <Comment>下次调度时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>25</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1479" parent="366" name="jvm_param">
      <Comment>Jvm参数信息</Comment>
      <Position>26</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <index id="1480" parent="366" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1481" parent="366" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1482" parent="367" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="1483" parent="367" name="last_end_time">
      <Comment>最后一次运行结束的时间</Comment>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1484" parent="367" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1485" parent="367" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1486" parent="368" name="lock_name">
      <Comment>锁名称</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="1487" parent="368" name="PRIMARY">
      <ColNames>lock_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1488" parent="368" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1489" parent="369" name="id">
      <AutoIncrement>21805368</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1490" parent="369" name="job_group">
      <Comment>执行器主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1491" parent="369" name="job_name">
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1492" parent="369" name="job_id">
      <Comment>任务，主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1493" parent="369" name="job_task_type">
      <Comment>任务的类型，从任务表冗余过来</Comment>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1494" parent="369" name="executor_address">
      <Comment>执行器地址，本次执行的地址</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1495" parent="369" name="executor_handler">
      <Comment>执行器任务handler</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1496" parent="369" name="executor_param">
      <Comment>执行器任务参数</Comment>
      <Position>8</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1497" parent="369" name="executor_sharding_param">
      <Comment>执行器任务分片参数，格式如 1/2</Comment>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1498" parent="369" name="executor_fail_retry_count">
      <Comment>失败重试次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1499" parent="369" name="trigger_time">
      <Comment>调度-时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1500" parent="369" name="trigger_code">
      <Comment>调度-结果</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1501" parent="369" name="trigger_msg">
      <Comment>调度-日志</Comment>
      <Position>13</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1502" parent="369" name="handle_time">
      <Comment>执行-时间</Comment>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1503" parent="369" name="handle_code">
      <Comment>执行-状态</Comment>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1504" parent="369" name="handle_msg">
      <Comment>执行-日志</Comment>
      <Position>16</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1505" parent="369" name="alarm_status">
      <Comment>告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1506" parent="369" name="process_id">
      <Comment>任务在执行服务器上的进程ID或者某些特别的JSON信息</Comment>
      <Position>18</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1507" parent="369" name="max_id">
      <Comment>增量任务为ID增量时保留的最大ID</Comment>
      <Position>19</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="1508" parent="369" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1509" parent="369" name="I_job_id">
      <ColNames>job_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1510" parent="369" name="I_job_task_type">
      <ColNames>job_task_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1511" parent="369" name="I_trigger_time">
      <ColNames>trigger_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1512" parent="369" name="I_handle_code">
      <ColNames>handle_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1513" parent="369" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1514" parent="370" name="id">
      <AutoIncrement>1174</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1515" parent="370" name="trigger_day">
      <Comment>调度-时间</Comment>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1516" parent="370" name="running_count">
      <Comment>运行中-日志数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1517" parent="370" name="suc_count">
      <Comment>执行成功-日志数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1518" parent="370" name="fail_count">
      <Comment>执行失败-日志数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1519" parent="370" name="update_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1520" parent="370" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1521" parent="370" name="i_trigger_day">
      <ColNames>trigger_day</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1522" parent="370" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1523" parent="370" name="i_trigger_day">
      <UnderlyingIndexName>i_trigger_day</UnderlyingIndexName>
    </key>
    <column id="1524" parent="371" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1525" parent="371" name="job_id">
      <Comment>任务，主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1526" parent="371" name="glue_type">
      <Comment>GLUE类型</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1527" parent="371" name="glue_source">
      <Comment>GLUE源代码</Comment>
      <Position>4</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1528" parent="371" name="glue_remark">
      <Comment>GLUE备注</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="1529" parent="371" name="add_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1530" parent="371" name="update_time">
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1531" parent="371" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1532" parent="371" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1533" parent="372" name="id">
      <Comment>唯一标识</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1534" parent="372" name="name">
      <Comment>任务名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1535" parent="372" name="node_id">
      <Comment>节点ID</Comment>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1536" parent="372" name="group_id">
      <Comment>分组ID</Comment>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1537" parent="372" name="process_id">
      <Comment>流程ID</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1538" parent="372" name="type">
      <Comment>任务类型：batch-sql, stream-flink，start，end</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1539" parent="372" name="scheduler_type">
      <Comment>调度类型：cron,  pretask, stream</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1540" parent="372" name="scheduler_options">
      <Comment>调度配置信息</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1541" parent="372" name="job_info_id">
      <Comment>XxlJob调度信息ID</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1542" parent="372" name="pre_task_ids">
      <Comment>前置任务ID列表</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1543" parent="372" name="post_task_Ids">
      <Comment>后置任务ID列表</Comment>
      <Position>11</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1544" parent="372" name="deleted">
      <Comment>是否删除 0: 否 1: 是</Comment>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1545" parent="372" name="path">
      <Comment>流程文件路径</Comment>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="1546" parent="372" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1547" parent="372" name="INDEX_XJPNT_DID">
      <ColNames>job_info_id</ColNames>
      <Comment>调度信息ID索引</Comment>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1548" parent="372" name="INDEX_XJPNT_PID">
      <ColNames>process_id
group_id</ColNames>
      <Comment>外键ID</Comment>
      <Type>btree</Type>
    </index>
    <key id="1549" parent="372" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1550" parent="372" name="INDEX_XJPNT_DID">
      <UnderlyingIndexName>INDEX_XJPNT_DID</UnderlyingIndexName>
    </key>
    <column id="1551" parent="373" name="id">
      <Comment>唯一标识</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1552" parent="373" name="task_id">
      <Comment>流程任务ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1553" parent="373" name="node_id">
      <Comment>（冗余）节点ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1554" parent="373" name="task_name">
      <Comment>（冗余）流程任务名称</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1555" parent="373" name="task_type">
      <Comment>（冗余）任务类型：batch-sql, stream-flink</Comment>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1556" parent="373" name="scheduler_type">
      <Comment>（冗余）调度类型：cron,  pretask, stream</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1557" parent="373" name="process_id">
      <Comment>（冗余）流程ID</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1558" parent="373" name="group_id">
      <Comment>分组ID，与任务表中的分租ID是不相同的</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1559" parent="373" name="job_log_id">
      <Comment>Xxl调度日志ID</Comment>
      <Position>9</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1560" parent="373" name="job_group_id">
      <Comment>Xxl调度执行器ID</Comment>
      <Position>10</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1561" parent="373" name="job_info_Id">
      <Comment>（冗余）Xxl调度信息ID</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1562" parent="373" name="pre_task_ids">
      <Comment>（冗余）前置任务ID列表</Comment>
      <Position>12</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1563" parent="373" name="post_task_ids">
      <Comment>（冗余）后置任务ID列表</Comment>
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1564" parent="373" name="execute_param">
      <Comment>调度任务参数信息</Comment>
      <Position>14</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="1565" parent="373" name="begin_time">
      <Comment>开始运行时间</Comment>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1566" parent="373" name="end_time">
      <Comment>运行结束时间</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1567" parent="373" name="create_time">
      <Comment>创建时间</Comment>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1568" parent="373" name="update_time">
      <Comment>更新时间</Comment>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1569" parent="373" name="status">
      <Comment>运行状态 &#xd;
01：准备运行&#xd;
02：运行中&#xd;
03：准备暂停&#xd;
04：准备停止&#xd;
05：已暂停&#xd;
06：已停止&#xd;
07：已完成&#xd;
08：失败</Comment>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>varchar(2)|0s</StoredType>
    </column>
    <column id="1570" parent="373" name="pre_error_count">
      <Comment>本任务的前置任务运行失败总数</Comment>
      <Position>20</Position>
      <StoredType>int(3)|0s</StoredType>
    </column>
    <column id="1571" parent="373" name="remark">
      <Comment>运行描述信息</Comment>
      <Position>21</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1572" parent="373" name="pre_ok_count">
      <Comment>本任务的前置任务运行成功总数</Comment>
      <Position>22</Position>
      <StoredType>int(3)|0s</StoredType>
    </column>
    <column id="1573" parent="373" name="pre_need_count">
      <Comment>本任务的前置任务总数</Comment>
      <Position>23</Position>
      <StoredType>int(3)|0s</StoredType>
    </column>
    <index id="1574" parent="373" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1575" parent="373" name="INDEX_XJPNR_WGI">
      <ColNames>process_id
id</ColNames>
      <Comment>主要用来加速根据分组ID查询时的速度</Comment>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1576" parent="373" name="INDEX_XJPNR_TID">
      <ColNames>task_id</ColNames>
      <Comment>任务ID外键</Comment>
      <Type>btree</Type>
    </index>
    <key id="1577" parent="373" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1578" parent="373" name="INDEX_XJPNR_WGI">
      <UnderlyingIndexName>INDEX_XJPNR_WGI</UnderlyingIndexName>
    </key>
    <column id="1579" parent="374" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1580" parent="374" name="create_time">
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1581" parent="374" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1582" parent="374" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1583" parent="375" name="id">
      <Comment>唯一标识</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1584" parent="375" name="task_id">
      <Comment>流程任务ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1585" parent="375" name="node_id">
      <Comment>（冗余）节点ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1586" parent="375" name="task_name">
      <Comment>（冗余）流程任务名称</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1587" parent="375" name="task_type">
      <Comment>（冗余）任务类型：batch-sql, stream-flink</Comment>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1588" parent="375" name="scheduler_type">
      <Comment>（冗余）调度类型：cron,  pretask, stream</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1589" parent="375" name="process_id">
      <Comment>（冗余）流程ID</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1590" parent="375" name="group_id">
      <Comment>分组ID，与任务表中的分租ID是不相同的</Comment>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1591" parent="375" name="job_log_id">
      <Comment>Xxl调度日志ID</Comment>
      <Position>9</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1592" parent="375" name="job_group_id">
      <Comment>Xxl调度执行器ID</Comment>
      <Position>10</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1593" parent="375" name="job_info_Id">
      <Comment>（冗余）Xxl调度信息ID</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1594" parent="375" name="pre_task_ids">
      <Comment>（冗余）前置任务ID列表</Comment>
      <Position>12</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1595" parent="375" name="post_task_ids">
      <Comment>（冗余）后置任务ID列表</Comment>
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1596" parent="375" name="execute_param">
      <Comment>执行参数信息</Comment>
      <Position>14</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="1597" parent="375" name="begin_time">
      <Comment>开始运行时间</Comment>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1598" parent="375" name="end_time">
      <Comment>运行结束时间</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1599" parent="375" name="create_time">
      <Comment>创建时间</Comment>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1600" parent="375" name="update_time">
      <Comment>更新时间</Comment>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1601" parent="375" name="status">
      <Comment>运行状态 &#xd;
01：准备运行&#xd;
02：运行中&#xd;
03：准备暂停&#xd;
04：准备停止&#xd;
05：已暂停&#xd;
06：已停止&#xd;
07：已完成&#xd;
08：失败</Comment>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>varchar(2)|0s</StoredType>
    </column>
    <column id="1602" parent="375" name="backup_status">
      <Comment>备份状态 &#xd;
01：准备运行&#xd;
02：运行中&#xd;
03：准备暂停&#xd;
04：准备停止&#xd;
05：已暂停&#xd;
06：已停止&#xd;
07：已完成&#xd;
08：失败</Comment>
      <Position>20</Position>
      <StoredType>varchar(2)|0s</StoredType>
    </column>
    <column id="1603" parent="375" name="pre_error_count">
      <Comment>本任务的前置任务运行失败总数</Comment>
      <Position>21</Position>
      <StoredType>int(3)|0s</StoredType>
    </column>
    <column id="1604" parent="375" name="remark">
      <Comment>运行描述信息</Comment>
      <Position>22</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1605" parent="375" name="pre_ok_count">
      <Comment>本任务的前置任务运行成功总数</Comment>
      <Position>23</Position>
      <StoredType>int(3)|0s</StoredType>
    </column>
    <column id="1606" parent="375" name="pre_need_count">
      <Comment>本任务的前置任务总数</Comment>
      <Position>24</Position>
      <StoredType>int(3)|0s</StoredType>
    </column>
    <column id="1607" parent="375" name="disabled">
      <Comment>是否禁用 0: 否 1: 是</Comment>
      <Position>25</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <index id="1608" parent="375" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1609" parent="375" name="INDEX_XJPNR_WGI">
      <ColNames>process_id
id</ColNames>
      <Comment>主要用来加速根据分组ID查询时的速度</Comment>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1610" parent="375" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1611" parent="375" name="INDEX_XJPNR_WGI">
      <UnderlyingIndexName>INDEX_XJPNR_WGI</UnderlyingIndexName>
    </key>
    <column id="1612" parent="376" name="id">
      <AutoIncrement>2007</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1613" parent="376" name="registry_group">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1614" parent="376" name="registry_key">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1615" parent="376" name="registry_value">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1616" parent="376" name="update_time">
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1617" parent="376" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1618" parent="376" name="i_g_k_v">
      <ColNames>registry_group
registry_key
registry_value</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1619" parent="376" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1620" parent="377" name="id">
      <AutoIncrement>2</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1621" parent="377" name="username">
      <Comment>账号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1622" parent="377" name="password">
      <Comment>密码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1623" parent="377" name="role">
      <Comment>角色：0-普通用户、1-管理员</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1624" parent="377" name="permission">
      <Comment>权限：执行器ID列表，多个逗号分割</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="1625" parent="377" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1626" parent="377" name="i_username">
      <ColNames>username</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1627" parent="377" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1628" parent="377" name="i_username">
      <UnderlyingIndexName>i_username</UnderlyingIndexName>
    </key>
    <column id="1629" parent="378" name="id">
      <Comment>唯一标识列</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1630" parent="378" name="create_time">
      <Comment>创建时间</Comment>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1631" parent="378" name="update_time">
      <Comment>更新时间</Comment>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1632" parent="378" name="create_user">
      <Comment>创建人</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1633" parent="378" name="run_status">
      <Comment>流程调度运行状态 0: 未运行 1: 运行中 2: 已暂停</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1634" parent="378" name="name">
      <Comment>流程名称</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1635" parent="378" name="json_context">
      <Comment>流程信息</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="1636" parent="378" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1637" parent="378" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1638" parent="379" name="order_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1639" parent="379" name="product_id">
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1640" parent="379" name="user_id">
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1641" parent="379" name="profit">
      <Position>4</Position>
      <StoredType>decimal(20,2 digit)|0s</StoredType>
    </column>
    <column id="1642" parent="379" name="tag">
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1643" parent="379" name="processed_at">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1644" parent="380" name="product_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1645" parent="380" name="summary_date">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1646" parent="380" name="total_sales">
      <Position>3</Position>
      <StoredType>decimal(20,2 digit)|0s</StoredType>
    </column>
    <column id="1647" parent="380" name="unique_customers">
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="1648" parent="380" name="PRIMARY">
      <ColNames>product_id
summary_date</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1649" parent="380" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1650" parent="381" name="order_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1651" parent="381" name="product_id">
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1652" parent="381" name="user_id">
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1653" parent="381" name="price">
      <Position>4</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1654" parent="381" name="quantity">
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1655" parent="381" name="order_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1656" parent="381" name="PRIMARY">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1657" parent="381" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1658" parent="382" name="order_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1659" parent="382" name="product_id">
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1660" parent="382" name="user_id">
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1661" parent="382" name="price">
      <Position>4</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1662" parent="382" name="quantity">
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1663" parent="382" name="order_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1664" parent="382" name="PRIMARY">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1665" parent="382" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1666" parent="383" name="order_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1667" parent="383" name="product_id">
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1668" parent="383" name="user_id">
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1669" parent="383" name="price">
      <Position>4</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1670" parent="383" name="quantity">
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1671" parent="383" name="cost_price">
      <Position>6</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1672" parent="383" name="is_active">
      <DefaultExpression>1</DefaultExpression>
      <Position>7</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1673" parent="383" name="order_time">
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1674" parent="383" name="PRIMARY">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1675" parent="383" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1676" parent="384" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(36)|0s</StoredType>
    </column>
    <column id="1677" parent="384" name="epoch">
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1678" parent="384" name="model_name">
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="1679" parent="384" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1680" parent="384" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1681" parent="385" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(36)|0s</StoredType>
    </column>
    <column id="1682" parent="385" name="epoch">
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1683" parent="385" name="epoch_2">
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1684" parent="385" name="model_name">
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1685" parent="385" name="create_time">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1686" parent="385" name="update_time">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1687" parent="385" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1688" parent="385" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>