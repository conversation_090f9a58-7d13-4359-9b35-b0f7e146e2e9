/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/20
 */
package com.xmcares.platform.hookbox.integrator.handler;


import com.xmcares.platform.hookbox.common.job.seatunnel.SeatunnelEventType;
import com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance;
import com.xmcares.platform.hookbox.integrator.service.DatasyncJobInstanceService;
import com.xmcares.platform.hookbox.integrator.service.SeaTunnelJobEventService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * SeaTunnel Job 事件处理控制器
 * <AUTHOR>
 * @since 2.1.0
 */
@RestController
public class SeaTunnelJobEventHandler{

    private final Logger logger = LoggerFactory.getLogger(SeaTunnelJobEventHandler.class);

    private final String EXPECTED_AUTH_TOKEN = "Aa123456!";

    @Resource
    private SeaTunnelJobEventService seaTunnelJobEventService;


    @PostMapping("/seatunnel/events")
    public ResponseEntity<String> receiveEventBatch(
            @RequestHeader(name = "Authorization") String authHeader,
            @RequestBody List<SeaTunnelEvent> events) {
        //  验证 Authorization 请求头
//        if (!EXPECTED_AUTH_TOKEN.equals(authHeader)) {
//            logger.warn("Unauthorized request received. Invalid token: {}", authHeader);
//            return new ResponseEntity<>("Unauthorized", HttpStatus.UNAUTHORIZED);
//        }

        // 处理事件
        if (events == null || events.isEmpty()) {
            logger.warn("Received an empty event batch.");
            return ResponseEntity.badRequest().body("Event batch cannot be empty.");
        }

        logger.info("Received a batch of {} events from SeaTunnel.", events.size());

        // 开始处理
        seaTunnelJobEventService.handleEvents(events);

        return ResponseEntity.ok("Event batch received successfully");

    }

    private void handleTaskStart(SeaTunnelEvent event) {
        // --- 在这里编写任务开始时的处理逻辑 ---
        logger.warn("========== TASK START DETECTED ==========");
        logger.warn("Job '{}' has started. First event time: {}.", event.getJobId(),formatTimestamp(event.getCreatedTime()));
        // 例如：更新数据库状态、发送一个开始通知等
        System.out.println("Executing start logic for job: " + event.getJobId());
        logger.warn("=========================================");
    }

    private void handleTaskRunning(SeaTunnelEvent event) {
        // --- 在这里编写任务开始时的处理逻辑 ---
        logger.warn("========== TASK RUNNING DETECTED ==========");
        logger.warn("Job '{}' is running. First event time: {}.", event.getJobId(),formatTimestamp(event.getCreatedTime()));
        // 例如：更新数据库状态、发送一个开始通知等
        System.out.println("Executing start logic for job: " + event.getJobId());
        logger.warn("=========================================");
    }


    private void handleTaskEnd(SeaTunnelEvent event) {
        // --- 在这里编写任务结束时的处理逻辑 ---
        logger.warn("========== TASK END DETECTED ==========");
        logger.warn("Job '{}' has ended. Last event time: {}.", event.getJobId(), formatTimestamp(event.getCreatedTime()));
        // 例如：计算任务耗时、触发下游依赖、发送成功或失败的报告等
        System.out.println("Executing end logic for job: " + event.getJobId());
        logger.warn("=======================================");
    }

    public static String formatTimestamp(long timestampMillis) {
        // 1. 定义你想要的输出格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 2. 将毫秒时间戳转换为一个 Instant 对象 (这是一个UTC时间点)
        Instant instant = Instant.ofEpochMilli(timestampMillis);

        // 3. 将 Instant 对象根据系统默认时区转换为 LocalDateTime 对象
        //    LocalDateTime 不包含时区信息，正好符合 "yyyy-MM-dd HH:mm:ss" 格式的需求
        //    你也可以指定一个时区，例如 ZoneId.of("Asia/Shanghai")
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());

        // 4. 使用定义好的格式化器将 LocalDateTime 转换为字符串
        return formatter.format(localDateTime);
    }

}
