<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmcares.platform.hookbox.integrator.mapper.IntegratorXxlJobLogMapper">

  <!-- 原有的BaseResultMap保持不变 -->
  <resultMap id="BaseResultMap" type="com.xmcares.platform.hookbox.integrator.model.IntegratorXxlJobLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="job_group" jdbcType="INTEGER" property="jobGroup" />
    <result column="job_id" jdbcType="INTEGER" property="jobId" />
    <result column="executor_address" jdbcType="VARCHAR" property="executorAddress" />
    <result column="executor_handler" jdbcType="VARCHAR" property="executorHandler" />
    <result column="executor_param" jdbcType="VARCHAR" property="executorParam" />
    <result column="executor_sharding_param" jdbcType="VARCHAR" property="executorShardingParam" />
    <result column="executor_fail_retry_count" jdbcType="INTEGER" property="executorFailRetryCount" />
    <result column="trigger_time" jdbcType="TIMESTAMP" property="triggerTime" />
    <result column="trigger_code" jdbcType="INTEGER" property="triggerCode" />
    <result column="trigger_msg" jdbcType="LONGVARCHAR" property="triggerMsg" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="handle_code" jdbcType="INTEGER" property="handleCode" />
    <result column="handle_msg" jdbcType="LONGVARCHAR" property="handleMsg" />
    <result column="alarm_status" jdbcType="TINYINT" property="alarmStatus" />
  </resultMap>

  <!-- 联表查询的ResultMap -->
  <resultMap id="JobLogWithInstanceResultMap" type="com.xmcares.platform.hookbox.integrator.model.XxlJobLogWithInstanceVO">
    <!-- XxlJobLog 字段 -->
    <id column="log_id" jdbcType="BIGINT" property="id" />
    <result column="job_group" jdbcType="INTEGER" property="jobGroup" />
    <result column="log_job_id" jdbcType="INTEGER" property="jobId" />
    <result column="executor_address" jdbcType="VARCHAR" property="executorAddress" />
    <result column="executor_handler" jdbcType="VARCHAR" property="executorHandler" />
    <result column="executor_param" jdbcType="VARCHAR" property="executorParam" />
    <result column="executor_sharding_param" jdbcType="VARCHAR" property="executorShardingParam" />
    <result column="executor_fail_retry_count" jdbcType="INTEGER" property="executorFailRetryCount" />
    <result column="trigger_time" jdbcType="TIMESTAMP" property="triggerTime" />
    <result column="trigger_code" jdbcType="INTEGER" property="triggerCode" />
    <result column="trigger_msg" jdbcType="LONGVARCHAR" property="triggerMsg" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="handle_code" jdbcType="INTEGER" property="handleCode" />
    <result column="handle_msg" jdbcType="LONGVARCHAR" property="handleMsg" />
    <result column="alarm_status" jdbcType="TINYINT" property="alarmStatus" />

    <!-- DatasyncJobInstance 字段 -->
    <result column="instance_id" jdbcType="VARCHAR" property="instanceId" />
    <result column="instance_job_id" jdbcType="VARCHAR" property="instanceJobId" />
    <result column="job_name" jdbcType="VARCHAR" property="jobName" />
    <result column="job_params" jdbcType="LONGVARCHAR" property="jobParams" />
    <result column="schedule_id" jdbcType="VARCHAR" property="scheduleId" />
    <result column="schedule_log_id" jdbcType="VARCHAR" property="scheduleLogId" />
    <result column="schedule_time" jdbcType="TIMESTAMP" property="scheduleTime" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="instance_status" jdbcType="TINYINT" property="status" />
    <result column="status_message" jdbcType="LONGVARCHAR" property="statusMessage" />
    <result column="received_count" jdbcType="INTEGER" property="receivedCount" />
    <result column="recevied_qps" jdbcType="DOUBLE" property="receviedQps" />
    <result column="writed_count" jdbcType="INTEGER" property="writedCount" />
    <result column="writed_qps" jdbcType="DOUBLE" property="writedQps" />
    <result column="instance_create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="instance_update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <!-- 原有的Base_Column_List保持不变 -->
  <sql id="Base_Column_List">
    id, job_group, job_id, executor_address, executor_handler, executor_param, executor_sharding_param,
    executor_fail_retry_count, trigger_time, trigger_code, trigger_msg, handle_time,
    handle_code, handle_msg, alarm_status
  </sql>

  <!-- 联表查询的列 -->
  <sql id="JobLogWithInstance_Column_List">
    xl.id as log_id,
    xl.job_group,
    xl.job_id as log_job_id,
    xl.executor_address,
    xl.executor_handler,
    xl.executor_param,
    xl.executor_sharding_param,
    xl.executor_fail_retry_count,
    xl.trigger_time,
    xl.trigger_code,
    xl.trigger_msg,
    xl.handle_time,
    xl.handle_code,
    xl.handle_msg,
    xl.alarm_status,
    dji.id as instance_id,
    dji.job_id as instance_job_id,
    dji.job_name,
    dji.job_params,
    dji.schedule_id,
    dji.schedule_log_id,
    dji.schedule_time,
    dji.finish_time,
    dji.status as instance_status,
    dji.status_message,
    dji.received_count,
    dji.recevied_qps,
    dji.writed_count,
    dji.writed_qps,
    dji.create_time as instance_create_time,
    dji.update_time as instance_update_time
  </sql>


  <!-- 分页查询方法，使用正确的连接条件 xl.id = dji.schedule_log_id -->
  <select id="selectJobLogWithInstancePageByLogId" resultMap="JobLogWithInstanceResultMap">
    SELECT
    <include refid="JobLogWithInstance_Column_List" />
    FROM xxl_job_log xl
    LEFT JOIN bdp_intg_datasync_job_instance dji ON xl.id = dji.schedule_log_id
    <if test="ew != null and ew.sqlSegment != null">
      ${ew.customSqlSegment}
    </if>
  </select>

</mapper>
