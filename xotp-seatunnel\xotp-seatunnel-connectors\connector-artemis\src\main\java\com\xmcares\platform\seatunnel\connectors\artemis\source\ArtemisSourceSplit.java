/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/24
 */
package com.xmcares.platform.seatunnel.connectors.artemis.source;

import org.apache.seatunnel.api.source.SourceSplit;

import java.util.Objects;

/** */
/**
 * 表示 Artemis MQ 源的拆分。 对于消息队列，拆分通常表示数据流的逻辑段， 或只是读者可以使用的工作单元。 在并行设置中，每个读取器实例通常会得到一个拆分。 由于 Artemis 没有像
 * Kafka 那样固有的“分区”，因此这种拆分是 主要用于 SeaTunnel 内的并行性管理。
 *
 * <AUTHOR>
 * @since 2.1.0
 */
public class ArtemisSourceSplit implements SourceSplit {

    private String splitId; // Unique ID for this split (e.g., "artemis-split-0", "artemis-split-1")

    // 无参构造函数
    public ArtemisSourceSplit() {}

    // 全参构造函数
    public ArtemisSourceSplit(String splitId) {
        this.splitId = splitId;
    }

    @Override
    public String splitId() {
        return splitId;
    }

    // Getter 方法
    public String getSplitId() {
        return splitId;
    }

    // Setter 方法 (如果需要，通常在 SourceSplit 中可能不需要 setter，但为了完整性提供)
    public void setSplitId(String splitId) {
        this.splitId = splitId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ArtemisSourceSplit that = (ArtemisSourceSplit) o;
        return Objects.equals(splitId, that.splitId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(splitId);
    }

    @Override
    public String toString() {
        return "ArtemisSourceSplit{" + "splitId='" + splitId + '\'' + '}';
    }
}
