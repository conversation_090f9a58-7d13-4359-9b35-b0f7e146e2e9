/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.sink;

import com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttSinkConfig;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.sink.SinkWriter;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.connectors.seatunnel.common.sink.AbstractSimpleSink;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.io.IOException;
import java.util.Optional;

/** MQTT Sink主类 负责将SeaTunnel数据写入MQTT消息队列 */
public class MqttSink extends AbstractSimpleSink<SeaTunnelRow, Void> {

    private static final Logger log = LoggerFactory.getLogger(MqttSink.class);


    private final MqttSinkConfig config;
    private final CatalogTable catalogTable;

    public MqttSink(CatalogTable catalogTable, ReadonlyConfig options) {
        this.catalogTable = catalogTable;
        this.config = MqttSinkConfig.buildConfig(options, catalogTable);
        this.config.validate();
        log.info(
                "Initialized MQTT Sink with config: brokers={}, topic={}, qos={}",
                config.getBrokerUrls(),
                config.isDynamicTopic() ? config.getTopicPattern() : config.getTopic(),
                config.getQos());
    }

    @Override
    public MqttSinkWriter createWriter(SinkWriter.Context context) throws IOException {
        log.info("Creating MQTT sink writer for subtask: {}", context.getIndexOfSubtask());
        return new MqttSinkWriter(config, context);
    }

    @Override
    public String getPluginName() {
        return "Mqtt";
    }

    @Override
    public Optional<CatalogTable> getWriteCatalogTable() {
        return Optional.ofNullable(catalogTable);
    }
}
