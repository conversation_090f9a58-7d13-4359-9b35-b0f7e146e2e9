<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="10 seconds">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <springProperty name="app.name" scope="context" source="spring.application.name" defaultValue="xotp-hookbox"/>

    <contextName>${app.name}</contextName>

    <property name="log.console.pattern" value="%clr(%d{HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(-){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(%4line){cyan} %clr(:){faint} %m%n%wEx"/>

    <property name="log.file.path" value="${app.home:-./}/logs/${app.name:-run}.log"/>
    <property name="log.file.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %level - [%thread] [%logger{36} %line] : %msg%n"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.console.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <!-- 将日志发送到 XXL-JOB 的 Appender -->
    <appender name="xxl-job" class="com.xmcares.platform.hookbox.common.util.XxlJobLogAppender">
        <filter class="com.xmcares.platform.hookbox.common.util.XxlJobMDCFilter">
            <key>XXL_JOB_EXECUTOR</key>
            <value>true</value>
        </filter>
    </appender>

    <!-- 针对特定类设置日志级别 -->
    <logger name="io.debezium" level="error"/>
    <logger name="org.apache.kafka.clients" level="error"/>
    <logger name="org.apache.flink" level="error"/>
    <logger name="o.a.kafka.connect" level="error"/>
    <logger name="c.v.cdc.debezium" level="error"/>
    <logger name="com.xmcares.framework.fsclient.ftp.FtpClientFactory" level="WARN" />

    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="xxl-job"/>
    </root>

</configuration>

