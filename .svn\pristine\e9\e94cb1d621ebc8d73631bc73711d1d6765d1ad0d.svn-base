import {
    EditableProTable
} from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors, KeyboardSensor } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { DragOutlined } from '@ant-design/icons';

// 添加拖拽样式
const DragStyles = () => (
    <style jsx global>{`
    .drag-handle {
      color: #bfbfbf;
      font-size: 16px;
      padding: 0 4px;
      transition: color 0.3s, transform 0.2s;
    }
    .drag-handle:hover {
      color: #1890ff;
      transform: scale(1.2);
    }
    .dragging-row td {
      background-color: #e6f7ff !important;
      border-top: 1px dashed #1890ff !important;
      border-bottom: 1px dashed #1890ff !important;
    }
    .drag-over-row {
      background-color: #f0f5ff !important;
      border-bottom: 2px dashed #1890ff !important;
    }
    .ant-modal .editable-pro-table .ant-table {
      overflow: visible;
    }
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.2); }
      70% { box-shadow: 0 0 0 6px rgba(24, 144, 255, 0); }
      100% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0); }
    }
    .dragging-row {
      animation: pulse 1.5s infinite;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      position: relative;
      z-index: 999;
    }
  `}</style>
);

// 拖动处理器组件
const DragHandler = ({ id }) => {
    const { setNodeRef, listeners, attributes, isDragging } = useSortable({ id });
    return (
        <>
            <DragOutlined
                ref={setNodeRef}
                {...listeners}
                {...attributes}
                className={`drag-handle ${isDragging ? 'dragging' : ''}`}
                style={{
                    cursor: isDragging ? "grabbing" : "grab",
                    color: isDragging ? "#1890ff" : undefined
                }}
            />
        </>
    );
};

// 自定义行组件
const TableRow = ({ children, ...props }) => {
    const id = props["data-row-key"];
    const {
        isDragging,
        setNodeRef,
        transform,
        transition,
        attributes,
        over
    } = useSortable({ id });

    const isOver = over?.id === id;

    const rowStyle = {
        transform: CSS.Transform.toString(transform && { ...transform, scaleY: 1 }),
        transition: transition || 'all 200ms ease',
        opacity: isDragging ? 0.6 : 1,
        cursor: isDragging ? 'grabbing' : 'inherit',
        position: isDragging ? 'relative' : undefined,
        zIndex: isDragging ? 1050 : undefined, // 确保在Modal内显示正常（Modal的z-index通常是1000）
        boxShadow: isDragging ? '0 2px 10px rgba(0, 0, 0, 0.2)' : 'none',
        borderLeft: isDragging ? '3px solid #1890ff' : 'none',
        backgroundColor: isDragging ? '#e6f7ff' : (isOver ? '#f0f5ff' : undefined)
    };

    const className = `${isDragging ? 'dragging-row' : ''} ${isOver ? 'drag-over-row' : ''}`;

    return (
        <tr
            {...props}
            ref={setNodeRef}
            style={{...props.style, ...rowStyle}}
            {...attributes}
            className={`${props.className || ''} ${className}`}
        >
            {children}
        </tr>
    );
};

// 可编辑拖放表格组件
const EditableDragTable = ({ dataSource, formColumns, onChange, editableKeys, onValuesChange }) => {
    const [columns, setColumns] = useState([]);
    const [activeId, setActiveId] = useState(null);

    useEffect(() => {
        const cls = [
            {
                key: 'sort',
                title: '排序',
                dataIndex: 'sort',
                editable: false,
                width: 60,
                align: 'center',
                className: 'drag-handle-column',
                render: (_, record) => <DragHandler key="DragHandler" id={record.ownId} />
            },
            ...formColumns,
        ];
        setColumns(cls);
    }, [formColumns, dataSource, activeId]);

    const onDragStart = (event) => {
        const { active } = event;
        setActiveId(active.id);

        // 拖拽开始时应用一些额外样式到Modal
        const modalContent = document.querySelector('.ant-modal-content');
        if (modalContent) {
            modalContent.style.overflow = 'visible';
        }
    };

    const onDragEnd = (event) => {
        const { active, over } = event;
        if (over && active.id !== over.id) {
            const oldIndex = dataSource.findIndex((item) => item.ownId === active.id);
            const newIndex = dataSource.findIndex((item) => item.ownId === over.id);
            const newData = arrayMove(dataSource, oldIndex, newIndex);
            onValuesChange(newData);
        }
        setActiveId(null);

        // 恢复Modal样式
        const modalContent = document.querySelector('.ant-modal-content');
        if (modalContent) {
            modalContent.style.overflow = '';
        }
    };

    // 使用更灵敏的传感器配置
    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 3, // 减小触发距离，使Modal内操作更灵敏
            },
        }),
        useSensor(KeyboardSensor)
    );

    return (
        <div className="modal-drag-table-container" style={{ position: 'relative' }}>
            <DragStyles />
            <DndContext
                onDragStart={onDragStart}
                onDragEnd={onDragEnd}
                sensors={sensors}
                collisionDetection={closestCenter}
                modifiers={[restrictToVerticalAxis]}
            >
                <SortableContext items={dataSource.map((item) => item.ownId)} strategy={verticalListSortingStrategy}>
                    <EditableProTable
                        rowKey="ownId"
                        scroll={{ y: 400 }} // 适应Modal高度
                        toolBarRender={false}
                        columns={columns}
                        value={dataSource}
                        recordCreatorProps={{
                            newRecordType: 'dataSource',
                            position: 'top',
                            record: () => ({
                                ownId: (Math.random() * 1000000000).toFixed(0),
                                title: '',
                            }),
                        }}
                        editable={{
                            type: 'multiple',
                            editableKeys,
                            onChange,
                            actionRender: (row, config, defaultDoms) => {
                                return [defaultDoms.delete];
                            },
                            onValuesChange: (record, recordList) => {
                                onValuesChange(recordList);
                            },
                        }}
                        components={{
                            body: {
                                row: TableRow,
                            },
                        }}
                    />
                </SortableContext>
            </DndContext>
        </div>
    );
};

export default EditableDragTable;
