/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/9
 */
package com.xmcares.platform.hookbox.integrator.manager;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xmcares.platform.admin.common.datasource.DataSourceOptions;
import com.xmcares.platform.admin.common.datasource.mq.MqClient;
import com.xmcares.platform.admin.common.datasource.mq.MqDataSource;
import com.xmcares.platform.admin.common.datasource.mq.MqDataSourceManager;
import com.xmcares.platform.admin.common.datasource.mq.artemis.ArtemisProperties;
import com.xmcares.platform.admin.common.datasource.mq.pulsar.PulsarProperties;
import com.xmcares.platform.admin.common.datasource.mq.ximc.XimcDataSource;
import com.xmcares.platform.hookbox.common.mq.MqTemplate;
import com.xmcares.platform.hookbox.common.mq.artemis.ArtemisMqTemplate;
import com.xmcares.platform.hookbox.common.mq.pulsar.PulsarMqTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
@Component
public class XimcReaderBridgeManager {

    private static final Logger logger = LoggerFactory.getLogger(XimcReaderBridgeManager.class);

    private final Map<SourceKey, SourceReaderBridge> ximcSources = new ConcurrentHashMap<>();

    @Resource
    private MqDataSourceManager dataSourceManager;

    @Resource
    private MqTemplate mqTemplate;

    /**
     * 注册一个任务使用某个数据源，如果首次注册，则初始化并注册监听器
     */
    public void addXimcReaderIfAbsent(DataSourceOptions ximcSource, String readerJobId) {
        SourceKey key = getUniqueKey(ximcSource);
        SourceReaderBridge reader = ximcSources.computeIfAbsent(key, k -> {
            logger.info("Creating new data source bridge for key: {}", k);
            MqDataSource ds = dataSourceManager.getOrCreateDataSource(ximcSource);
            SourceReaderBridge bridge = new SourceReaderBridge(k, ds);
            bridge.start();
            return bridge;
        });
        reader.addJob(readerJobId);
        logger.info("Added ximc reader job [{}] to data source [{}]", readerJobId, key);
    }

    /**
     * 移除任务对某数据源的依赖；原子地判断是否可移除
     */
    public void removeXimcReaderIfPresent(DataSourceOptions ximcSource, String readerJobId) {
        SourceKey key = getUniqueKey(ximcSource);
        ximcSources.compute(key, (k, bridge) -> {
            if (bridge == null) {
                logger.warn("Attempted to remove ximc reader job [{}] from nonexistent data source key: {}", readerJobId, k);
                return null;
            }
            bridge.removeJob(readerJobId);
            logger.info("Removed  ximc reader job [{}] from data source [{}]", readerJobId, k);

            if (bridge.hasNoJob()) {
                bridge.stop();
                dataSourceManager.destroyDataSource(bridge.getDataSourceName());
                logger.info("No more ximc reader jobs using data source [{}], destroyed it", k);
                return null;
            }
            return bridge;
        });
    }


    public void changeToBridgeSource(ObjectNode ximcSource) {
        if (this.mqTemplate instanceof ArtemisMqTemplate) {
            ArtemisProperties properties = ((ArtemisMqTemplate) this.mqTemplate).getProperties();
            String topic = ximcSource.get("topic").textValue();
            String clientId = "client_"+ximcSource.get("plugin_name").textValue();
            String subscription = ximcSource.get("username").textValue();

            ximcSource.put("plugin_name", "Artemis");
            ximcSource.put("topic", topic);
            ximcSource.put("client_id", clientId);
            ximcSource.put("subscription", subscription);

            ximcSource.put("broker_url", properties.getBrokerUrl());
            ximcSource.put("username", properties.getUsername());
            ximcSource.put("password", properties.getPassword());
        } else if (this.mqTemplate instanceof PulsarMqTemplate) {
            //            topic
//            topic-pattern
//            topic-discovery.interval
//            subscription.name

//            client.service-url
//            admin.service-url
//            auth.plugin-class
//            auth.params
//            poll.timeout
//            poll.interval
//            poll.batch.size
//            cursor.startup.mode
//            cursor.startup.timestamp
//            cursor.reset.mod
//            cursor.stop.mode
//            cursor.stop.timestamp
//            schema
//            common-options
//            format
            PulsarProperties properties = ((PulsarMqTemplate) this.mqTemplate).getProperties();

            String topic = ximcSource.get("topic").textValue();
            String subscription = ximcSource.get("username").textValue();
            ximcSource.put("plugin_name", "Pulsar");
            ximcSource.put("topic", topic);
            ximcSource.put("subscription.name", subscription);

            ximcSource.put("client.service_url", properties.getServiceUrl());
            ximcSource.put("admin.service_url", properties.getAdminUrl());
            String authType = properties.getAuthType();
            if (PulsarProperties.AuthType.TOKEN.equalsIgnoreCase(authType)) {
                ximcSource.put("auth.plugin-class", "org.apache.pulsar.client.impl.auth.AuthenticationToken");
                ximcSource.put("auth.params", "token:" + properties.getToken());
            }
        }
    }


    private SourceKey getUniqueKey(DataSourceOptions options) {
        return new SourceKey(options.getUrl(), options.getUsername());
    }

    // -------- 内部结构 --------

    static class SourceKey {
        private final String url;
        private final String username;

        public SourceKey(String url, String username) {
            this.url = url;
            this.username = username;
        }

        public String getUrl() {
            return url;
        }

        public String getUsername() {
            return username;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SourceKey sourceKey = (SourceKey) o;
            return Objects.equals(url, sourceKey.url) && Objects.equals(username, sourceKey.username);
        }

        @Override
        public int hashCode() {
            return Objects.hash(url, username);
        }

        @Override
        public String toString() {
            return this.getUrl() + "@" + this.getUsername();
        }
    }


    class SourceReaderBridge {
        private final Set<String> jobIds = ConcurrentHashMap.newKeySet();
        private final SourceKey sourceKey;
        private final MqDataSource dataSource;
        private final AtomicBoolean listenerRegistered = new AtomicBoolean(false);
        private static final String ALL_TOPICS = "";
        private final MqClient.MqMessageListener listener;
        private String bridgeTopicPrefix;

        public SourceReaderBridge(SourceKey sourceKey, MqDataSource dataSource) {
            if (dataSource == null) {
                throw new IllegalArgumentException("dataSource cannot be null");
            }
            this.sourceKey = sourceKey;
            this.dataSource = dataSource;
            listener = (headers, message) -> {
                try {
                    String bridgeTopicName = getBridgeTopicName(headers.get(XimcDataSource.HEADER_KEY_TOPIC, String.class));
                    mqTemplate.sendMessage(bridgeTopicName, headers, message);
                } catch (Exception e) {
                    // 避免异常影响主线程或导致连接断开
                    logger.error("Error while processing message in SourceReaderListener", e);
                }
            };
        }

        public void addJob(String jobId) {
            jobIds.add(jobId);
        }

        public void removeJob(String jobId) {
            jobIds.remove(jobId);
        }

        public boolean hasNoJob() {
            return jobIds.isEmpty();
        }

        public void start() {
            if (listenerRegistered.compareAndSet(false, true)) {
                dataSource.addMessageListener(ALL_TOPICS, null, listener);
            }
        }

        public void stop() {
            if (listenerRegistered.compareAndSet(true, false)) {
                dataSource.removeMessageListener(ALL_TOPICS, null, listener);
            }
        }

        public String getDataSourceName() {
            return dataSource.getName();
        }

        private String getBridgeTopicName(String topicName) {
            return "/public/" + this.sourceKey.getUrl() + "/" + this.sourceKey.getUsername() + "/" + topicName;
        }

        private String getBridgeTopicPrefix() {
            return bridgeTopicPrefix;
        }

    }


}

