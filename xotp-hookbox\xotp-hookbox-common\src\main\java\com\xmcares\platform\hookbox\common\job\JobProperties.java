/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/20
 */
package com.xmcares.platform.hookbox.common.job;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 *
 * <AUTHOR>
 * @since 2.1.0
 */
@ConfigurationProperties(prefix = "xbdp.hookbox.job")
public class JobProperties {
    /**
     * job engine type，default seatunnel
     */
    private String engine = "seatunnel";

    @NestedConfigurationProperty
    private Seatunnel seatunnel = new Seatunnel();


    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public Seatunnel getSeatunnel() {
        return seatunnel;
    }

    public void setSeatunnel(Seatunnel seatunnel) {
        this.seatunnel = seatunnel;
    }

    class Seatunnel {
        /**
         * sea-tunnel api endpoint
         */
        private String apiEndpoint = "http://localhost:8080";

        public String getApiEndpoint() {
            return apiEndpoint;
        }

        public void setApiEndpoint(String apiEndpoint) {
            this.apiEndpoint = apiEndpoint;
        }
    }





}
