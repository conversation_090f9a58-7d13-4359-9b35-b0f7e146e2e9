package com.xmcares.platform.hookbox.developer.processor;

import com.xmcares.platform.hookbox.developer.util.ProcessUtil;
import com.xxl.job.core.context.XxlJobHelper;
import org.apache.flink.api.common.JobID;
import org.apache.flink.client.deployment.ClusterDeploymentException;
import org.apache.flink.client.deployment.ClusterSpecification;
import org.apache.flink.client.deployment.StandaloneClusterId;
import org.apache.flink.client.deployment.application.ApplicationConfiguration;
import org.apache.flink.client.program.ClusterClient;
import org.apache.flink.client.program.ClusterClientProvider;
import org.apache.flink.client.program.PackagedProgram;
import org.apache.flink.client.program.PackagedProgramUtils;
import org.apache.flink.client.program.rest.RestClusterClient;
import org.apache.flink.configuration.*;
import org.apache.flink.runtime.jobgraph.JobGraph;
import org.apache.flink.runtime.jobgraph.SavepointRestoreSettings;
import org.apache.flink.yarn.YarnClientYarnClusterInformationRetriever;
import org.apache.flink.yarn.YarnClusterDescriptor;
import org.apache.flink.yarn.YarnClusterInformationRetriever;
import org.apache.flink.yarn.configuration.YarnConfigOptions;
import org.apache.flink.yarn.configuration.YarnDeploymentTarget;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.yarn.api.records.ApplicationId;
import org.apache.hadoop.yarn.client.api.YarnClient;
import org.apache.hadoop.yarn.conf.YarnConfiguration;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.apache.flink.configuration.MemorySize.MemoryUnit.MEGA_BYTES;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/6/15 09:52
 */
public class FlinkStreamTaskProcessor {

    RestTemplate restTemplate = new RestTemplate();
    HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();

    /**
     * 通过任务名称查找jobId
     *
     * @param url     get=>http://flink-rest-ip:port/jobs/overview
     * @param jobName
     * @return jobIds
     */
    public List<String> getJobIdByJobName(String url, String jobName) {
        List<String> jobIds = new ArrayList<>();
        HashMap<String, ArrayList<HashMap<String, Object>>> jobsMap = restTemplate.getForObject(url, HashMap.class);
        for (HashMap<String, Object> job : jobsMap.get("jobs")) {
            String state = String.valueOf(job.get("state"));
            switch (state) {
                //"INITIALIZING", "CREATED", "RUNNING",
                // "FAILING", "FAILED", "CANCELLING",
                // "CANCELED", "FINISHED", "RESTARTING",
                // "SUSPENDED", "RECONCILING"
                case "INITIALIZING":
                case "CREATED":
                case "RUNNING":
                    if (jobName.equals(String.valueOf(job.get("name")))) {
                        jobIds.add(String.valueOf(job.get("jid")));
                    }
                    break;
                default:
            }
        }
        return jobIds;
    }



    /**
     * 通过jobId关闭job
     *
     * @param url patch=>http://ip:port/jobs/7cf4ae7a94d04b2f901dad20be7ee484
     */
    public void cancelJobByJobId(String url) {
        try {
            ClientHttpRequest request = httpRequestFactory.createRequest(new URI(url), HttpMethod.PATCH);
            ClientHttpResponse execute = request.execute();
            execute.close();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }
    }

    /**
     * 命令行执行flink命令
     *
     * @param command flink运行命令
     */
    public String processSubmit(String command) {
        try {
            Process p = Runtime.getRuntime().exec(command);
            String processId = ProcessUtil.getProcessId(p);
            InputStream is = p.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(is));

            //打印日志
            String line = null;
            try {
                while ((line = br.readLine()) != null) {
                    XxlJobHelper.log(line);
                }
            } catch (Exception e) {
                XxlJobHelper.log(e);
            } finally {
                try {
                    is.close();
                    br.close();
                } catch (IOException e) {
                    XxlJobHelper.log(e);
                }
            }
            return processId;
        } catch (Exception e) {
            XxlJobHelper.log(e);
        }
        return null;
    }


    /**
     * 提交任务到flink本地集群
     *
     * @param jarFilePath         执行任务的jar包路径
     * @param entryPointClassName 执行jar的主类
     * @param jobManagerAddr      job manager 地址
     * @param jobManagerPort      job manager 端口
     * @param restPort            rest api 端口
     * @param parallelism         并行度
     */
    public void standaloneSubmit(String jarFilePath, String entryPointClassName, String jobManagerAddr, int jobManagerPort, int restPort, int parallelism) {
        RestClusterClient<StandaloneClusterId> client = null;
        try {
            // 集群信息
            Configuration configuration = new Configuration();
            configuration.setString(JobManagerOptions.ADDRESS, jobManagerAddr);
            configuration.setInteger(JobManagerOptions.PORT, jobManagerPort);
            configuration.setInteger(RestOptions.PORT, restPort);
            client = new RestClusterClient<>(configuration, StandaloneClusterId.getInstance());
            File jarFile = new File(jarFilePath);
            SavepointRestoreSettings savepointRestoreSettings = SavepointRestoreSettings.none();
            PackagedProgram program = PackagedProgram.newBuilder()
                    .setConfiguration(configuration)
                    .setEntryPointClassName(entryPointClassName)
                    .setJarFile(jarFile)
                    .setSavepointRestoreSettings(savepointRestoreSettings).build();
            JobGraph jobGraph = PackagedProgramUtils.createJobGraph(program, configuration, parallelism, false);
            CompletableFuture<JobID> result = client.submitJob(jobGraph);
            JobID jobId = result.get();
            XxlJobHelper.log("提交完成");
            XxlJobHelper.log("jobId:" + jobId.toString());
        } catch (Exception e) {
            XxlJobHelper.log(e);
        }

    }

    /**
     * 提交到yarn
     *
     * @param yarnApplicationName    yarn应用名称
     * @param args                   用户jar参数
     * @param hadoopConfs            hadoop配置文件位置yarn-site...
     * @param configurationDirectory flink的yarn配置文件目录
     * @param flinkLibs              flink的依赖目录
     * @param userJarPath            用户执行flink jar目录
     * @param flinkDistJar           flink-yarn jar包地址
     */
    public void yarnSubmit(String yarnApplicationName, String[] args, String[] hadoopConfs, String configurationDirectory, String flinkLibs, String userJarPath, String flinkDistJar, String jobManagerMemory, String taskManagerMemory) {

        YarnClient yarnClient = YarnClient.createYarnClient();
        org.apache.hadoop.conf.Configuration entries = new org.apache.hadoop.conf.Configuration();
        for (int i = 0; i < hadoopConfs.length; i++) {
            entries.addResource(new Path(hadoopConfs[i]));
        }
        YarnConfiguration yarnConfiguration = new YarnConfiguration(entries);
        yarnClient.init(yarnConfiguration);
        yarnClient.start();

        YarnClusterInformationRetriever clusterInformationRetriever = YarnClientYarnClusterInformationRetriever
                .create(yarnClient);

        //获取flink的配置
        Configuration flinkConfiguration = GlobalConfiguration.loadConfiguration(
                configurationDirectory);
        flinkConfiguration.set(CheckpointingOptions.INCREMENTAL_CHECKPOINTS, true);
        flinkConfiguration.set(
                PipelineOptions.JARS,
                Collections.singletonList(
                        userJarPath));

        Path remoteLib = new Path(flinkLibs);
        flinkConfiguration.set(
                YarnConfigOptions.PROVIDED_LIB_DIRS,
                Collections.singletonList(remoteLib.toString()));

        flinkConfiguration.set(
                YarnConfigOptions.FLINK_DIST_JAR,
                flinkDistJar);
        //设置为application模式
        flinkConfiguration.set(
                DeploymentOptions.TARGET,
                YarnDeploymentTarget.APPLICATION.getName());
        //yarn application name
        flinkConfiguration.set(YarnConfigOptions.APPLICATION_NAME, yarnApplicationName);

        flinkConfiguration.set(JobManagerOptions.TOTAL_PROCESS_MEMORY, MemorySize.parse(jobManagerMemory, MEGA_BYTES));
        flinkConfiguration.set(TaskManagerOptions.TOTAL_PROCESS_MEMORY, MemorySize.parse(taskManagerMemory, MEGA_BYTES));

        ClusterSpecification clusterSpecification = new ClusterSpecification.ClusterSpecificationBuilder()
                .createClusterSpecification();

//    设置用户jar的参数和主类
        ApplicationConfiguration appConfig = new ApplicationConfiguration(args, null);

        YarnClusterDescriptor yarnClusterDescriptor = new YarnClusterDescriptor(
                flinkConfiguration,
                yarnConfiguration,
                yarnClient,
                clusterInformationRetriever,
                true);
        ClusterClientProvider<ApplicationId> clusterClientProvider = null;
        try {
            clusterClientProvider = yarnClusterDescriptor.deployApplicationCluster(
                    clusterSpecification,
                    appConfig);
            assert clusterClientProvider != null;
        } catch (ClusterDeploymentException e) {
            XxlJobHelper.log(e);
        }
        ClusterClient<ApplicationId> clusterClient = clusterClientProvider.getClusterClient();
        ApplicationId applicationId = clusterClient.getClusterId();
        XxlJobHelper.log(applicationId.toString());
    }


}
