/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/24
 */
package com.xmcares.platform.seatunnel.connectors.artemis.source;

import com.google.auto.service.AutoService;
import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableSourceFactory;

import java.util.Collections;

import static org.apache.seatunnel.api.configuration.util.OptionRule.builder;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
@AutoService(Factory.class)
public class ArtemisSourceFactory implements TableSourceFactory {

    public static final String IDENTIFIER = "Artemis";

    /**
     * Seatunnel job configuration will use this identifier, e.g., "source { Artemis { ... } }"
     *
     * @return The identifier of the factory
     */
    @Override
    public String factoryIdentifier() {
        return IDENTIFIER;
    }

    // --- Options Definition ---
    public static final Option<String> BROKER_URL =
            Options.key("broker_url")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("Artemis broker URL (e.g., tcp://localhost:61616).");

    public static final Option<String> CLIENT_ID =
            Options.key("client_id")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "Client ID for durable topic subscriptions. ");

    public static final Option<String> TOPIC =
            Options.key("topic")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "topic name for durable topic subscriptions. ");
    public static final Option<String> SUBSCRIPTION =
            Options.key("subscription")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "subscription name for durable topic subscriptions. ");

    public static final Option<String> USERNAME =
            Options.key("username")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("Username for Artemis authentication (optional).");

    public static final Option<String> PASSWORD =
            Options.key("password")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("Password for Artemis authentication (optional).");

    public static final Option<String> FORMAT =
            Options.key("format")
                    .stringType()
                    .defaultValue("json") // Default to json
                    .withDescription("Data format of messages: json, xml, or text.");

    public static final Option<Long> POLL_TIMEOUT_MS =
            Options.key("poll_timeout_ms")
                    .longType()
                    .defaultValue(500L)
                    .withDescription("Timeout for polling messages in milliseconds.");

    public static final Option<Integer> BATCH_SIZE =
            Options.key("batch_size")
                    .intType()
                    .defaultValue(100)
                    .withDescription("Max number of messages to poll in a single batch.");

    public static final Option<Long> POLL_INTERVAL_MS =
            Options.key("poll_interval_ms")
                    .longType()
                    .defaultValue(50L)
                    .withDescription(
                            "Interval in milliseconds to wait if no messages are available, after a poll.");

    public static final Option<CatalogTable> SCHEMA =
            Options.key("schema")
                    .objectType(CatalogTable.class)
                    .noDefaultValue()
                    .withDescription(
                            "The schema of the data received from Artemis. Required for 'json' and 'xml' formats.");

    // --- Option Rule Definition ---
    @Override
    public OptionRule optionRule() {
        return builder()
                .required(BROKER_URL)
                .required(TOPIC)
                .required(CLIENT_ID)
                .optional(USERNAME, PASSWORD)
                .optional(FORMAT, POLL_TIMEOUT_MS, BATCH_SIZE, POLL_INTERVAL_MS)
                .conditional(FORMAT, Collections.singletonList("json"), SCHEMA)
                .conditional(FORMAT, Collections.singletonList("xml"), SCHEMA)
                .build();
    }

    @Override
    public Class<? extends ArtemisSource> getSourceClass() {
        return ArtemisSource.class;
    }
}
