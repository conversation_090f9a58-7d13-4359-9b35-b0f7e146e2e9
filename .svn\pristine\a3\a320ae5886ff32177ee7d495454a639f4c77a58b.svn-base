import request from '@/utils/request';
import { integratorUrl } from '@/utils/baseUrl';
import * as utils from '@/utils/utils';

/**
 * 根据数据同步定义ID获取数据同步实例列表
 * @param pid 查询条件
 * @returns {Promise<unknown>}
 */
export function query(pid) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-job/instance/pid-list`,{pid: pid});
  return request(path, {
    method: 'GET',
  });
}

/**
 * 获取数据同步实例明细信息
 * @param id id
 * @returns {Promise<unknown>}
 */
export function get(id) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-job/instance/get`,{id: id});
  return request(path, {
    method: 'GET',
  });
}

/**
 * 开始运行
 * @param id
 * @returns {Promise<unknown>}
 */
export function beginRun(id) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-job/instance/begin`,{id: id});
  return request(path, {
    method: 'GET',
  });
}

/**
 * 停止运行
 * @param id
 * @returns {Promise<unknown>}
 */
export function stopRun(id) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-job/instance/stop`,{id: id});
  return request(path, {
    method: 'GET',
  });
}

/**
 * 仅运行一次
 * @param id
 * @returns {Promise<unknown>}
 */
export function runOnce(id) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-job/instance/trigger`,{id: id});
  return request(path, {
    method: 'GET',
  });
}

/**
 * 删除数据同步实例
 * @param id
 * @returns {Promise<unknown>}
 */
export function remove(id) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-job/instance/delete`,{id: id});
  return request(path, {
    method: 'GET',
  });
}

/**
 * 更新数据同步实例
 * @param data
 * @returns {Promise<unknown>}
 */
export function update(data) {
  return request(`${integratorUrl}/datasync-job/instance/update`, {
    method: 'POST',
    body: data,
  });
}

/**
 * 读取配置文件信息
 * @param id
 * @returns {Promise<unknown>}
 */
export function readerJson(id) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-job/instance/displayJson`,{id: id});
  return request(path, {
    method: 'GET',
  });
}
