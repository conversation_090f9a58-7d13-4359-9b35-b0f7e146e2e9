<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MyBatisCodeHelperDatasource">
    <option name="projectProfile">
      <ProjectProfile>
        <option name="addSchemaName" value="true" />
        <option name="controllerTemplateString" value="&#10;#* @vtlvariable name=&quot;tableName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;entityPackageName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;entityClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;servicePackageName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;serviceInterfacePackage&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;serviceClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;serviceInterfaceClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;mapperPackageName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;mapperClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;controllerPackage&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;tableRemark&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;myDate&quot; type=&quot;java.util.Date&quot; *#&#10;#* @vtlvariable name=&quot;simpleDateFormat&quot; type=&quot;java.text.SimpleDateFormat&quot; *#&#10;package $!{controllerPackage};&#10;import $!{entityPackageName}.$!{entityClassName};&#10;###set($realServiceName = $!{serviceClassName}+'Impl')&#10;import $!{servicePackageName}.$!{serviceClassName};&#10;import org.springframework.web.bind.annotation.*;&#10;&#10;#set($serviceFirstLower = $!{serviceClassName.substring(0,1).toLowerCase()}+$!{serviceClassName.substring(1,$!{serviceClassName.length()})})&#10;import org.springframework.beans.factory.annotation.Autowired;&#10;&#10;/**&#10;* $!{tableRemark}($!{tableName})表控制层&#10;*&#10;* <AUTHOR> class $!{entityClassName}Controller {&#10;/**&#10;* 服务对象&#10;*/&#10;    @Autowired&#10;    private $!{serviceClassName} $!{serviceFirstLower};&#10;&#10;    /**&#10;    * 通过主键查询单条数据&#10;    *&#10;    * @param id 主键&#10;    * @return 单条数据&#10;    */&#10;    @GetMapping(&quot;selectOne&quot;)&#10;    public $!{entityClassName} selectOne(Integer id) {&#10;    return $!{serviceFirstLower}.selectByPrimaryKey(id);&#10;    }&#10;&#10;}" />
        <option name="generateService" value="true" />
        <option name="javaMapperPackage" value="com.xmcares.platform.admin.integrator.datasync.repository.mapper" />
        <option name="javaMapperPath" value="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java" />
        <option name="javaModelPackage" value="com.xmcares.platform.admin.integrator.datasync.model" />
        <option name="javaModelPath" value="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java" />
        <option name="lastDatabaseCrudChooseModuleName" value="xotp-release-admin" />
        <option name="mapperAnnotaion" value="true" />
        <option name="modelAddToString" value="true" />
        <option name="moduleNameToPackageAndPathMap">
          <map>
            <entry key="xotp-release-admin">
              <value>
                <UserPackageAndPathInfoByModule>
                  <option name="javaMapperPackage" value="com.xmcares.platform.admin.integrator.datasync.repository.mapper" />
                  <option name="javaMapperPath" value="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java" />
                  <option name="javaModelPacakge" value="com.xmcares.platform.admin.integrator.datasync.model" />
                  <option name="javaModelPath" value="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java" />
                  <option name="javaServiceInterfacePackage" value="com.xmcares.platform.hookbox.integrator.service" />
                  <option name="javaServiceInterfacePath" value="$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java" />
                  <option name="javaServicePackage" value="com.xmcares.platform.admin.integrator.datasync.service" />
                  <option name="javaServicePath" value="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java" />
                  <option name="xmlPackage" value="mapperxml" />
                  <option name="xmlPath" value="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/resources" />
                </UserPackageAndPathInfoByModule>
              </value>
            </entry>
          </map>
        </option>
        <option name="removeTablePreName" value="bdp_intg_" />
        <option name="tableGenerateConfigs">
          <map>
            <entry key="xotp:bdp_intg_datasync">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="Datasync" />
                  <option name="moduleName" value="xotp-release-admin" />
                  <option name="mybatisplusIdType" value="INPUT" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="xotp:bdp_intg_datasync_instance">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="DatasyncInstance" />
                  <option name="moduleName" value="xotp-release-admin" />
                  <option name="mybatisplusIdType" value="INPUT" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="xotp:bdp_intg_datasync_job">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="DatasyncJob" />
                  <option name="moduleName" value="xotp-release-admin" />
                  <option name="mybatisplusIdType" value="INPUT" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="xotp:bdp_intg_datasync_job_instance">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="DatasyncJobInstance" />
                  <option name="moduleName" value="xotp-release-admin" />
                  <option name="mybatisplusIdType" value="INPUT" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="xotp:bdp_intg_datasync_model">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="DatasyncModel" />
                  <option name="moduleName" value="xotp-release-admin" />
                  <option name="mybatisplusIdType" value="INPUT" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="xotp:xxl_job_log">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="XxlJobLog" />
                  <option name="moduleName" value="xotp-release-admin" />
                  <option name="mybatisplusIdType" value="INPUT" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
          </map>
        </option>
        <option name="useSwagger" value="true" />
        <option name="userMybatisPlus" value="true" />
        <option name="xmlMapperPackage" value="mapperxml" />
        <option name="xmlMapperPath" value="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/resources" />
      </ProjectProfile>
    </option>
  </component>
</project>