import React, {PureComponent, forwardRef} from 'react';
import {connect} from 'dva';
import {Form} from "@ant-design/compatible";
import {
  Button,
  Card,
  Col, Divider,
  Dropdown,
  Input, Menu, Modal,
  Row, Space, Tabs,
  Tooltip,
} from 'antd';
import * as utils from '@/utils/utils';
import PageHeaderWrapper from "@/components/PageHeaderWrapper";
import CommonTable from "@/components/CommonTable";
import styles from './Process.less';
import {
  CopyOutlined,
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  PlusCircleOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import { menu } from '@/utils/ListOptionMenuUtils'
import {messageModal} from "@/utils/messageModal";
import TabPane from "@ant-design/pro-card/es/components/TabPane";
import Instance from "@/pages/modules/Developer/Process/Instance/Instance";
import Resource from "@/pages/modules/Developer/Process/Resource/Resource";
import InitForm from "@/pages/modules/Developer/Process/Init/InitForm";
import PublishForm from "@/pages/modules/Developer/Process/Publish/PublishForm";
import ResourceForm from "@/pages/modules/Developer/Process/Resource/ResourceForm";
import * as dataflow from '@/services/Developer/Process/Dataflow';
import router from "umi/router";
import SharingApiExample from '@/components/SharingApiExample';
const confirm = Modal.confirm;
const FormItem = Form.Item;
const modelsName = 'process';
let markPage = 1, pageSize = 10;


@connect(({process, tenantManager, deptManager, role, userRole, menu, loading}) => ({
  process,
  tenantManager,
  deptManager,
  role,
  userRole,
  menu,
  loading: loading.models.process,
}))
@Form.create()

class Process extends PureComponent {

  state = {
    selectedRows: [],
    selectedTab: 'instance'
  }

  instance = React.createRef();
  resource = React.createRef();

  /**
   * 用户列表(第一次渲染后调用)
   */
  componentDidMount() {
    this.defineList.queryList();
  }

  handleDelete = (row) => {
    const topThis = this;
    confirm({
      title: '确认框?',
      content: '是否确认删除"'+row.name+'"',
      okText: '确定',
      cancelText: '取消',
      async onOk() {
        const res = await dataflow.deleteDefine(row.id);
        if (res === true) {
          messageModal("success", "删除成功");
          topThis.defineList.queryList();
        }
      },
    });
  }

  handleEdit = (row) => {
    confirm({
      title: '确认框?',
      content: '是否确认打开编辑界面',
      okText: '确定',
      cancelText: '取消',
      async onOk() {
        router.push({
          pathname: '/developer/processEditor',
          query: {
            id: row.id,
            mark: 'edit'
          },
        });
      },
    });
  }

  handlePublish = (row) => {
    const that = this;
    that.publishFormHandler.openPublishView(row);
  }

  /** 初始化表单功能 */
  initFormController = {
    openInitView: ()=> {
      this.initForm.OPEN_MODEL();
    },
    submit: (body, callback)=> {
      // todo YH 初始化表单提交的回调
      console.log(body);
    }
  }

  /** 初始化表单功能 */
  publishFormHandler = {
    openPublishView: (dataflow)=> {
      this.publishForm.OPEN_MODEL(dataflow);
    },
    submit: (dataflowId)=> {
      this.instance.current.wrappedInstance.instanceList.queryList(dataflowId);
    }
  }

  /** 初始化表单功能 */
  resourceFormHandler = {
    openResourceView: ()=> {
      const {selectedRows} = this.state;
      if(selectedRows && selectedRows.length > 0) {
        this.resourceForm.OPEN_MODEL(selectedRows[0]);
      } else {
        messageModal("warning", "请先选择定义");
      }
    },
    submit: (dataflowId)=> {
      this.resource.current.wrappedInstance.resourceList.queryList(dataflowId);
    }
  }
  /** 定义列表功能  */
  defineList = {
    columns: [
      {
        title: '数据开发名称',
        dataIndex: 'name',
        width: '15%',
        render: (text) => {
          return (
            <Tooltip title={text}>
              <div className={styles.resultColumnsDiv}>{text}</div>
            </Tooltip>
          );
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: '15%',
        render: (text) => {
          return (
            <Tooltip title={text}>
              <div className={styles.resultColumnsDiv}>{text}</div>
            </Tooltip>
          );
        },
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        width: '15%',
        render: (text) => {
          return (
            <Tooltip title={text}>
              <div className={styles.resultColumnsDiv}>{text}</div>
            </Tooltip>
          );
        },
      },
      {
        title: '维护人',
        dataIndex: 'createUser',
        width: '15%',
        render: (text) => {
          return (
            <Tooltip title={text}>
              <div className={styles.resultColumnsDiv}>{text}</div>
            </Tooltip>
          );
        },
      },
      {
        title: '备注',
        dataIndex: 'remark',
        render: (text) => {
          return (
            <Tooltip title={text}>
              <div className={styles.resultColumnsDiv}>{text}</div>
            </Tooltip>
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'id',
        width: '200px',
        render: (text, record) => {
          const operation = (
            <Space>
              <Button size="small" type="primary" onClick={() => this.handlePublish(record)}>发布</Button>
              <Button size="small" type="primary" onClick={() => {this.handleEdit(record);}}>编辑</Button>
              <Button size="small" type="primary" danger onClick={() => {this.handleDelete(record);}}>删除</Button>
            </Space>
          );
          return (
            <Tooltip>
              <div>{operation}</div>
            </Tooltip>
          );
        },
      },
    ],
    queryList: ()=> {
      const {dispatch} = this.props;
      const params = {
        page: markPage,
        rows: pageSize,
      };
      dispatch({
        type: `${modelsName}/fetch`,
        payload: params,
        callback: () => {
          this.setState({
            selectedRows: []
          });
        },
      });
    },
    reloadData: ()=> {
      const {form, dispatch} = this.props;
      form.validateFields((err, values) => {
        for (let key in values) {
          if (values[key] === '') {
            delete values[key]
          }
        }
        if (!err) {
          const params = {
            page: markPage,
            rows: pageSize,
            ...values,
          };
          dispatch({
            type: `${modelsName}/fetch`,
            payload: params,
            callback: () => {
              this.setState({
                selectedRows: [],
              });
            },
          });
        }
      });
    },
    onChange: (pagination)=> {
      const {form} = this.props;
      markPage = pagination.current;
      pageSize = pagination.pageSize;
      console.log("page:" + markPage + ", size:" + pageSize);
      form.validateFields((err, fieldsValue) => {
        let data = {
          page: markPage,
          rows: pageSize,
          ...fieldsValue
        };
        const {dispatch} = this.props;
        dispatch({
          payload: data,
          type: `${modelsName}/fetch`,

        });
      });
    },
    onSelect: (selectRowKey, selectRow)=> {
      this.setState({
        selectedRows: selectRowKey
      })

    },
  }

  /** 查询表单功能 */
  suchForm = {
    /** 查询 */
    search: (e) => {
      if (e) e.preventDefault();
      this.defineList.reloadData();
    },
    /** 重置 */
    reset: () => {
      const data = { topThis: this };
      utils.reset(data);
    }
  }

  /** tab标签控制器 */
  tabController = {
    onChange: (key)=> {

      this.setState({
        selectedTab: key
      })
    },

  }

  /**
   * 查询框
   */
  renderForm() {
    const {form} = this.props;
    const {getFieldDecorator} = this.props.form;
    const {search, reset} = this.suchForm;
    return (
      <Form onSubmit={search}>
        <Row gutter={{md: 4, lg: 12, xl: 24}}>
          <Col md={5} sm={24}>
            <FormItem label="数据开发名称">
              {getFieldDecorator('definitionName', {
                rules: [
                  {max: 20, message: '最多可输入20字'},
                ],
              })(<Input placeholder="请输入数据开发名称" onBlur={utils.valToTrim.bind(this, 'definitionName', form)}
                        allowClear={true}/>)}
            </FormItem>
          </Col>
          <Col md={5} sm={24} style={{paddingTop: '4px'}}>
            <Button type="primary" htmlType="submit">
              查询
            </Button>
            <Button style={{marginLeft: 8}} type="primary" onClick={reset}>重置</Button>
          </Col>
        </Row>
      </Form>
    );
  }



  // 界面渲染
  render() {
    const {process: {data, loading}} = this.props;
    const {selectedRows} = this.state;
    const methods = {
      initSubmit: this.initFormController.submit,
    }
    const methodsPublish = {
      publishSubmit: this.publishFormHandler.submit,
    }
    const methodsResource = {
      resourceSubmit: this.resourceFormHandler.submit,
    }
    return (
      <PageHeaderWrapper>
        <Card bordered={false} bodyStyle={{paddingBottom:'0px'}}>
          <div>
            <div className={styles.tableListForm}>{this.renderForm()}</div>

            <Space style={{marginBottom: 16}}>
              <Button type="primary" onClick={() => this.initFormController.openInitView()}>
                <PlusCircleOutlined style={{fontSize: 16}}/>
                <span>新增数据开发</span>
              </Button>
              <Button type="primary" onClick={() => this.resourceFormHandler.openResourceView()}>
                <PlusCircleOutlined style={{fontSize: 16}}/>
                <span>新增资源</span>
              </Button>
              <Button onClick={() => this.defineList.queryList()} icon={<SyncOutlined style={{fontSize: 16}}/>}/>
            </Space>

            <CommonTable
              selectType={"radio"}
              loading={loading}
              data={data}
              scrollY={"190px"}
              columns={this.defineList.columns}
              current={markPage}
              onChange={this.defineList.onChange}
              onSelect={this.defineList.onSelect}
            />
          </div>
        </Card>
        <Card bordered={false} bodyStyle={{paddingTop:'0px'}}>
          <div>
            <Tabs defaultActiveKey="instance" onChange={this.tabController.onChange}>
              <TabPane tab="数据开发实例列表" key="instance">
                <Instance ref={this.instance} id={selectedRows[0] || ''}/>
              </TabPane>
              <TabPane tab="数据开发资源列表" key="resource" forceRender={true}>
                <Resource ref={this.resource} id={selectedRows[0] || ''}/>
              </TabPane>
            </Tabs>
          </div>
        </Card>
        <InitForm wrappedComponentRef={(inst) => this.initForm = inst} {...methods}/>
        <PublishForm wrappedComponentRef={(inst) => this.publishForm = inst} {...methodsPublish}/>
        <ResourceForm wrappedComponentRef={(inst) => this.resourceForm = inst} {...methodsResource}/>
      </PageHeaderWrapper>

    )
  }
}

export default Process
