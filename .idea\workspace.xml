<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="413e139c-38e8-4ddb-b9b9-47592f574e18" name="更改" comment="开发SeaTunnel事件回调的接收接口，实现任务启动与结束的状态更新。">
      <change afterPath="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/resources/seatunnel_ftl/mqtt_sink.ftl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/resources/seatunnel_ftl/mqtt_source.ftl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/resources/seatunnel_ftl/rocketmq_sink.ftl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/resources/seatunnel_ftl/rockettmq_source.ftl" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xotp-admin/xotp-admin-common/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/xotp-admin/xotp-admin-common/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/model/JobLogVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/model/JobLogVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/resources/seatunnel_ftl/main.ftl" beforeDir="false" afterPath="$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/resources/seatunnel_ftl/main.ftl" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handler/XxlJobDatasyncLogHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handler/XxlJobDatasyncLogHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/model/JobLogVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/model/JobLogVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xotp-release/xotp-release-hookbox-integrator/src/main/resources/application-dev.properties" beforeDir="false" afterPath="$PROJECT_DIR$/xotp-release/xotp-release-hookbox-integrator/src/main/resources/application-dev.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xotp-seatunnel/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/xotp-seatunnel/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/connector-mqtt/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/connector-mqtt/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Enum" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/../../../../chatFile/wxfile/WeChat Files/wxid_3746607465912/FileStorage/File/2025-05/AndroidManifest.xml" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.6.3" />
        <option name="localRepository" value="D:\apache-maven-3.6.3\repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="threads" value="8" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="D:\apache-maven-3.6.3\setting\lcxsettings_xmky.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="explicitlyEnabledProfiles" value="nexus-8181,nexus-8081" />
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="PerforceDirect.Settings">
    <option name="CHARSET" value="none" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2wheqLtO0VMMzLDJPHjMehwa1RL" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder0": "0",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder1": "1",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder2": "2",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder3": "3",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder4": "4",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder5": "5",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth0": "405",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth1": "405",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth2": "405",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth3": "405",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth4": "405",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth5": "404",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder0": "0",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder1": "1",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder2": "2",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder3": "3",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder4": "4",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder5": "5",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth0": "407",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth1": "406",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth2": "407",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth3": "406",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth4": "407",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth5": "406",
    "Maven.xotp [clean,install,-e,-X,-rf...].executor": "Run",
    "Maven.xotp [clean,install].executor": "Run",
    "Maven.xotp [clean].executor": "Run",
    "Maven.xotp [validate].executor": "Run",
    "Maven.xotp-admin [clean,install].executor": "Run",
    "Maven.xotp-admin [clean].executor": "Run",
    "Maven.xotp-datax [clean,install].executor": "Run",
    "Maven.xotp-datax [clean].executor": "Run",
    "Maven.xotp-datax-ximcreader [clean,install].executor": "Run",
    "Maven.xotp-datax-ximcreader [clean].executor": "Run",
    "Maven.xotp-flinkx [clean,install].executor": "Run",
    "Maven.xotp-flinkx [clean].executor": "Run",
    "Maven.xotp-flinkx-quality [clean].executor": "Run",
    "Maven.xotp-flinkx-quality [install].executor": "Run",
    "Maven.xotp-hookbox [clean,install].executor": "Run",
    "Maven.xotp-hookbox [clean,package].executor": "Run",
    "Maven.xotp-hookbox [clean].executor": "Run",
    "Maven.xotp-hookbox [package].executor": "Run",
    "Maven.xotp-hookbox [validate].executor": "Run",
    "Maven.xotp-openapi [clean,install].executor": "Run",
    "Maven.xotp-openapi [clean].executor": "Run",
    "Maven.xotp-release [clean,install].executor": "Run",
    "Maven.xotp-release [clean].executor": "Run",
    "Maven.xotp-release [package].executor": "Run",
    "Maven.xotp-seatunnel [clean,install].executor": "Run",
    "Maven.xotp-seatunnel [clean].executor": "Run",
    "Maven.xotp-seatunnel-connectors [clean,install].executor": "Run",
    "Maven.xotp-seatunnel-connectors [clean].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.AdminApplication.executor": "Debug",
    "Spring Boot.DataserviceAdminApplication.executor": "Debug",
    "Spring Boot.DataserviceOpenapiApplication.executor": "Debug",
    "Spring Boot.HookboxApplication.executor": "Run",
    "Spring Boot.HookboxIntegratorApplication.executor": "Debug",
    "Spring Boot.IntegratorAdminApplication.executor": "Debug",
    "Spring Boot.IntegratorHookbooxApplication.executor": "Run",
    "Spring Boot.IntegratorHookboxApplication.executor": "Debug",
    "Spring Boot.OpenapiApplication.executor": "Run",
    "Spring Boot.SchedulerAdminApplication.executor": "Debug",
    "com.codeium.enabled": "true",
    "database.data.extractors.current.export.id": "SQL 插入",
    "database.data.extractors.current.id": "SQL 插入",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/codespace/ideaworkspace/2025-tech/xotp/xotp-seatunnel/xotp-seatunnel-examples/src/main/resources",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.stylelint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.stylelint": "D:\\codespace\\ideaworkspace\\2025-tech\\xotp\\xotp-webui\\node_modules\\stylelint",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.start.executor": "Run",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "ts.external.directory.path": "D:\\software\\jetbrains\\tools\\IntelliJ IDEA Ultimate\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true",
    "应用程序.SeaTunnelEngineLocalExample.executor": "Debug"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\codespace\ideaworkspace\2025-tech\xotp\xotp-seatunnel\xotp-seatunnel-examples\src\main\resources" />
      <recent name="D:\codespace\ideaworkspace\2025-tech\xotp\xotp-seatunnel\xotp-seatunnel-examples\src\main\java\com\xmcares\platform\seatunnel\examples" />
      <recent name="D:\codespace\ideaworkspace\2025-tech\xotp\xotp-admin\xotp-admin-integrator\src\main\resources\seatunnel_ftl" />
      <recent name="D:\codespace\ideaworkspace\2025-tech\xotp\xotp-seatunnel\xotp-seatunnel-connectors\connector-mqtt" />
      <recent name="D:\codespace\ideaworkspace\2025-tech\xotp\xotp-seatunnel\xotp-seatunnel-connectors\connector-fako\src\main\java\com\xmcares\platform\seatunnel\connectors\fako" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\codespace\ideaworkspace\2025-tech\xotp\xotp-admin\xotp-admin-integrator\src\main\resources\mybatis-mapper" />
      <recent name="D:\codespace\ideaworkspace\2025-tech\xotp\xotp-release\xotp-release-hookbox-integrator\src\main\resources" />
      <recent name="D:\codespace\ideaworkspace\2025-tech\xotp\xotp-release\xotp-release-hookbox-integrator\src\main\resources\datax\script" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.xmcares.platform.seatunnel.connectors.mqtt.source" />
      <recent name="com.xmcares.platform.admin.integrator.datasync.util" />
      <recent name="com.xmcares.platform.admin.scheduler.xxljob.model.dto" />
      <recent name="com.xmcares.platform.admin.integrator.datasync.model" />
      <recent name="com.xmcares.platform.hookbox.integrator.service" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvn clean install -e -X -rf :xotp-release-hookbox-integrator" />
      <command value="mvn clean install" />
    </option>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
        <option value="js.build_tools.npm" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="应用程序.SeaTunnelEngineLocalExample">
    <configuration name="SeaTunnelEngineLocalExample" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xmcares.platform.seatunnel.starter.SeaTunnelEngineLocalExample" />
      <module name="connector-starter" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xmcares.platform.seatunnel.starter.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="xotp-release-admin" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xmcares.platform.admin.AdminApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xmcares.platform.admin.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HookboxApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="xotp-release-hookbox" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xmcares.platform.hookbox.HookboxApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xmcares.platform.hookbox.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HookboxIntegratorApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="xotp-release-hookbox-integrator" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xmcares.platform.hookbox.integrator.HookboxIntegratorApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xmcares.platform.hookbox.integrator.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OpenapiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="xotp-release-openapi" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xmcares.platform.openapi.OpenapiApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xmcares.platform.openapi.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="start" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/xotp-webui/package.json" />
      <command value="run" />
      <scripts>
        <script value="start" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="npm.start" />
      <item itemvalue="Spring Boot.AdminApplication" />
      <item itemvalue="Spring Boot.HookboxIntegratorApplication" />
      <item itemvalue="Spring Boot.HookboxApplication" />
      <item itemvalue="Spring Boot.OpenapiApplication" />
      <item itemvalue="应用程序.SeaTunnelEngineLocalExample" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.AdminApplication" />
        <item itemvalue="Spring Boot.HookboxIntegratorApplication" />
        <item itemvalue="npm.start" />
        <item itemvalue="应用程序.SeaTunnelEngineLocalExample" />
        <item itemvalue="Spring Boot.HookboxApplication" />
        <item itemvalue="Spring Boot.OpenapiApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\codespace\ideaworkspace\2025-tech\xotp" />
          <option name="myCopyRoot" value="D:\codespace\ideaworkspace\2025-tech\xotp" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\codespace\ideaworkspace\2025-tech\xotp" />
          <option name="myCopyRoot" value="D:\codespace\ideaworkspace\2025-tech\xotp" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="413e139c-38e8-4ddb-b9b9-47592f574e18" name="更改" comment="" />
      <created>1746498422752</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746498422752</updated>
      <workItem from="1746498426216" duration="34082000" />
      <workItem from="1746671826379" duration="262000" />
      <workItem from="1746672119140" duration="27339000" />
      <workItem from="1746773534865" duration="7588000" />
      <workItem from="1747012265350" duration="16617000" />
      <workItem from="1747097929113" duration="26462000" />
      <workItem from="1747192583874" duration="11832000" />
      <workItem from="1747271951148" duration="31693000" />
      <workItem from="1747639091963" duration="35690000" />
      <workItem from="1747789226287" duration="26608000" />
      <workItem from="1747883369261" duration="25003000" />
      <workItem from="1747965701775" duration="1937000" />
      <workItem from="1747967827017" duration="15183000" />
      <workItem from="1748223744589" duration="18232000" />
      <workItem from="1748496997285" duration="27692000" />
      <workItem from="1748912880623" duration="37331000" />
      <workItem from="1749450448663" duration="19000" />
      <workItem from="1749450478480" duration="238000" />
      <workItem from="1749450735504" duration="11000" />
      <workItem from="1749450754110" duration="127000" />
      <workItem from="1749450891109" duration="332386000" />
      <workItem from="1750129662793" duration="40074000" />
      <workItem from="1750928107321" duration="56158000" />
      <workItem from="1752133015607" duration="472000" />
      <workItem from="1752134033810" duration="108458000" />
      <workItem from="1753060701570" duration="21781000" />
      <workItem from="1753106179087" duration="22115000" />
      <workItem from="1753173484471" duration="495000" />
      <workItem from="1753173992789" duration="19558000" />
      <workItem from="1753253260780" duration="27339000" />
      <workItem from="1753340721585" duration="7106000" />
      <workItem from="1753370368219" duration="992000" />
      <workItem from="1753373022799" duration="2535000" />
      <workItem from="1753406739770" duration="11550000" />
      <workItem from="1753665537869" duration="16538000" />
      <workItem from="1753692181636" duration="2572000" />
      <workItem from="1753708790585" duration="1049000" />
      <workItem from="1753750756964" duration="8415000" />
      <workItem from="1753773858761" duration="58563000" />
    </task>
    <task id="LOCAL-00020" summary="1.修改展示 应用SECRET 样式">
      <option name="closed" value="true" />
      <created>1749094241861</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1749094241861</updated>
    </task>
    <task id="LOCAL-00021" summary="1.修改json解析日期字符串格式&#10;2.修改跳过xxl-job鉴权方式&#10;3.修改admin-scheduler注册端口">
      <option name="closed" value="true" />
      <created>1749695550409</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1749695550409</updated>
    </task>
    <task id="LOCAL-00022" summary="1.解决共享网关无法加载mybatis xml文件问题配置">
      <option name="closed" value="true" />
      <created>1749780677364</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1749780677364</updated>
    </task>
    <task id="LOCAL-00023" summary="提交部署用文件">
      <option name="closed" value="true" />
      <created>1750059251337</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1750059251337</updated>
    </task>
    <task id="LOCAL-00024" summary="提交部署用文件">
      <option name="closed" value="true" />
      <created>1750060323312</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1750060323312</updated>
    </task>
    <task id="LOCAL-00025" summary="1.补充提交admin端注册xxl-job执行器配置文件">
      <option name="closed" value="true" />
      <created>1750061853669</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1750061853669</updated>
    </task>
    <task id="LOCAL-00026" summary="提交baseURL /api 路径">
      <option name="closed" value="true" />
      <created>1750062758629</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1750062758630</updated>
    </task>
    <task id="LOCAL-00027" summary="提交baseURL /api 路径">
      <option name="closed" value="true" />
      <created>1750063076363</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1750063076363</updated>
    </task>
    <task id="LOCAL-00028" summary="修改nginx.conf">
      <option name="closed" value="true" />
      <created>1750063707411</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1750063707411</updated>
    </task>
    <task id="LOCAL-00029" summary="解决日志依赖冲突">
      <option name="closed" value="true" />
      <created>1750072959946</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1750072959946</updated>
    </task>
    <task id="LOCAL-00030" summary="修改构建文件逻辑，合并datax+原项目">
      <option name="closed" value="true" />
      <created>1750075621302</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1750075621302</updated>
    </task>
    <task id="LOCAL-00031" summary="修改配置文件">
      <option name="closed" value="true" />
      <created>1750076529095</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1750076529095</updated>
    </task>
    <task id="LOCAL-00032" summary="注释 数据治理 模块">
      <option name="closed" value="true" />
      <created>1750077527743</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1750077527743</updated>
    </task>
    <task id="LOCAL-00033" summary="修改加载 datax 配置的方式">
      <option name="closed" value="true" />
      <created>1750078740578</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1750078740578</updated>
    </task>
    <task id="LOCAL-00034" summary="提交baseURL /api 路径">
      <option name="closed" value="true" />
      <created>1750079811708</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1750079811708</updated>
    </task>
    <task id="LOCAL-00035" summary="修改deploy.yml">
      <option name="closed" value="true" />
      <created>1750079995839</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1750079995839</updated>
    </task>
    <task id="LOCAL-00036" summary="修改启动主类">
      <option name="closed" value="true" />
      <created>1750082625009</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1750082625009</updated>
    </task>
    <task id="LOCAL-00037" summary="配置生成地址配置">
      <option name="closed" value="true" />
      <created>1750083458632</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1750083458632</updated>
    </task>
    <task id="LOCAL-00038" summary="指定配置文件使用生产环境">
      <option name="closed" value="true" />
      <created>1750083830741</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1750083830741</updated>
    </task>
    <task id="LOCAL-00039" summary="修改job.admin地址">
      <option name="closed" value="true" />
      <created>1750084098073</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1750084098073</updated>
    </task>
    <task id="LOCAL-00040" summary="修改job.admin地址">
      <option name="closed" value="true" />
      <created>1750084825848</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1750084825848</updated>
    </task>
    <task id="LOCAL-00041" summary="修改deploy.yml">
      <option name="closed" value="true" />
      <created>1750085079754</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1750085079754</updated>
    </task>
    <task id="LOCAL-00042" summary="修改deploy.yml">
      <option name="closed" value="true" />
      <created>1750085156249</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1750085156249</updated>
    </task>
    <task id="LOCAL-00043" summary="修改保存job逻辑">
      <option name="closed" value="true" />
      <created>1750085884108</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1750085884108</updated>
    </task>
    <task id="LOCAL-00044" summary="修改deploy.yml">
      <option name="closed" value="true" />
      <created>1750086750588</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1750086750588</updated>
    </task>
    <task id="LOCAL-00045" summary="修改job.admin地址">
      <option name="closed" value="true" />
      <created>1750087066584</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1750087066584</updated>
    </task>
    <task id="LOCAL-00046" summary="修改deploy.yml">
      <option name="closed" value="true" />
      <created>1750087799885</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1750087799885</updated>
    </task>
    <task id="LOCAL-00047" summary="修改deploy.yml">
      <option name="closed" value="true" />
      <created>1750088155710</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1750088155710</updated>
    </task>
    <task id="LOCAL-00048" summary="修改job.admin地址">
      <option name="closed" value="true" />
      <created>1750088553673</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1750088553673</updated>
    </task>
    <task id="LOCAL-00049" summary="修改查找datax.py方法">
      <option name="closed" value="true" />
      <created>1750089109804</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1750089109804</updated>
    </task>
    <task id="LOCAL-00050" summary="排查bug注释文件">
      <option name="closed" value="true" />
      <created>1750089526709</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1750089526709</updated>
    </task>
    <task id="LOCAL-00051" summary="去除修改">
      <option name="closed" value="true" />
      <created>1750091147440</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1750091147440</updated>
    </task>
    <task id="LOCAL-00052" summary="修改openapi打包方式，添加生产打包配置">
      <option name="closed" value="true" />
      <created>1750122048976</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1750122048976</updated>
    </task>
    <task id="LOCAL-00053" summary="修改deploy.yml">
      <option name="closed" value="true" />
      <created>1750122322004</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1750122322004</updated>
    </task>
    <task id="LOCAL-00054" summary="暂时注释有问题的模块">
      <option name="closed" value="true" />
      <created>1750124898344</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1750124898344</updated>
    </task>
    <task id="LOCAL-00055" summary="修改模块名称">
      <option name="closed" value="true" />
      <created>1750125706520</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1750125706520</updated>
    </task>
    <task id="LOCAL-00056" summary="新增openapi deploy.yml">
      <option name="closed" value="true" />
      <created>1750126499865</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1750126499865</updated>
    </task>
    <task id="LOCAL-00057" summary="数据集成 + seatunnle 模板配置">
      <option name="closed" value="true" />
      <created>1752828016745</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1752828016745</updated>
    </task>
    <task id="LOCAL-00058" summary="提交mybatis-plus配置">
      <option name="closed" value="true" />
      <created>1752828589281</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1752828589281</updated>
    </task>
    <task id="LOCAL-00059" summary="移除无用配置">
      <option name="closed" value="true" />
      <created>1752828651342</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1752828651342</updated>
    </task>
    <task id="LOCAL-00060" summary="回退修改">
      <option name="closed" value="true" />
      <created>1752828695527</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1752828695527</updated>
    </task>
    <task id="LOCAL-00061" summary="回退修改">
      <option name="closed" value="true" />
      <created>1752828763004</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1752828763004</updated>
    </task>
    <task id="LOCAL-00062" summary="暂时注释无用controller">
      <option name="closed" value="true" />
      <created>1753061483530</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1753061483531</updated>
    </task>
    <task id="LOCAL-00063" summary="暂时注释无用controller">
      <option name="closed" value="true" />
      <created>1753061499722</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1753061499722</updated>
    </task>
    <task id="LOCAL-00064" summary="开始、停止任务计算中心及管理端对接">
      <option name="closed" value="true" />
      <created>1753173350386</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1753173350386</updated>
    </task>
    <task id="LOCAL-00065" summary="添加根据数据集成任务日志查询功能">
      <option name="closed" value="true" />
      <created>1753339435520</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1753339435521</updated>
    </task>
    <task id="LOCAL-00066" summary="修改日志查询功能问题">
      <option name="closed" value="true" />
      <created>1753408604322</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1753408604322</updated>
    </task>
    <task id="LOCAL-00067" summary="查询日志详情方法">
      <option name="closed" value="true" />
      <created>1753409560860</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1753409560860</updated>
    </task>
    <task id="LOCAL-00068" summary="开发SeaTunnel事件回调的接收接口，实现任务启动与结束的状态更新。">
      <option name="closed" value="true" />
      <created>1753690196771</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1753690196771</updated>
    </task>
    <option name="localTasksCounter" value="69" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="修改启动主类" />
    <MESSAGE value="配置生成地址配置" />
    <MESSAGE value="指定配置文件使用生产环境" />
    <MESSAGE value="修改job.admin地址" />
    <MESSAGE value="修改查找datax.py方法" />
    <MESSAGE value="提交baseURL /api 路径" />
    <MESSAGE value="排查bug注释文件" />
    <MESSAGE value="去除修改" />
    <MESSAGE value="修改openapi打包方式，添加生产打包配置" />
    <MESSAGE value="修改deploy.yml" />
    <MESSAGE value="暂时注释有问题的模块" />
    <MESSAGE value="修改模块名称" />
    <MESSAGE value="新增openapi deploy.yml" />
    <MESSAGE value="数据集成 + seatunnle 模板配置" />
    <MESSAGE value="修改保存job逻辑" />
    <MESSAGE value="提交mybatis-plus配置" />
    <MESSAGE value="移除无用配置" />
    <MESSAGE value="回退修改" />
    <MESSAGE value="暂时注释无用controller" />
    <MESSAGE value="开始、停止任务计算中心及管理端对接" />
    <MESSAGE value="修改前端调用log路径" />
    <MESSAGE value="修改日志查询功能问题" />
    <MESSAGE value="添加根据数据集成任务日志查询功能" />
    <MESSAGE value="查询日志详情方法" />
    <MESSAGE value="开发SeaTunnel事件回调的接收接口，实现任务启动与结束的状态更新。" />
    <option name="LAST_COMMIT_MESSAGE" value="开发SeaTunnel事件回调的接收接口，实现任务启动与结束的状态更新。" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>126</line>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-scheduler/src/main/java/com/xmcares/platform/admin/scheduler/controller/ExtendApiController.java</url>
          <line>100</line>
          <option name="timeStamp" value="21" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-scheduler/src/main/java/com/xmcares/platform/admin/scheduler/controller/ExtendApiController.java</url>
          <line>108</line>
          <option name="timeStamp" value="22" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>133</line>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>123</line>
          <option name="timeStamp" value="25" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-scheduler/src/main/java/com/xmcares/platform/admin/scheduler/controller/ExtendApiController.java</url>
          <line>83</line>
          <option name="timeStamp" value="26" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-scheduler/src/main/java/com/xmcares/platform/admin/scheduler/controller/ExtendApiController.java</url>
          <line>117</line>
          <option name="timeStamp" value="28" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>183</line>
          <option name="timeStamp" value="29" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>170</line>
          <option name="timeStamp" value="30" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/web/DatasyncController.java</url>
          <line>62</line>
          <option name="timeStamp" value="31" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handlers/DataSourceJobHandlers.java</url>
          <line>229</line>
          <option name="timeStamp" value="34" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>158</line>
          <option name="timeStamp" value="45" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>137</line>
          <option name="timeStamp" value="46" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncService.java</url>
          <line>176</line>
          <option name="timeStamp" value="47" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>176</line>
          <option name="timeStamp" value="48" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/DatasyncInstanceRepository.java</url>
          <line>238</line>
          <option name="timeStamp" value="51" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>285</line>
          <option name="timeStamp" value="58" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>254</line>
          <option name="timeStamp" value="60" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-scheduler/src/main/java/com/xmcares/platform/admin/scheduler/controller/ExtendApiController.java</url>
          <line>128</line>
          <option name="timeStamp" value="61" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>313</line>
          <option name="timeStamp" value="63" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>309</line>
          <option name="timeStamp" value="64" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-scheduler/src/main/java/com/xmcares/platform/admin/scheduler/controller/ExtendApiController.java</url>
          <line>142</line>
          <option name="timeStamp" value="65" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-scheduler/src/main/java/com/xmcares/platform/admin/scheduler/controller/ExtendApiController.java</url>
          <line>194</line>
          <option name="timeStamp" value="75" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$PROJECT_DIR$/../../../../apache-maven-3.6.3/repo/com/xuxueli/xxl-job-admin/2.5.0/xxl-job-admin-2.5.0-sources.jar!/com/xxl/job/admin/controller/JobLogController.java</url>
          <line>97</line>
          <option name="timeStamp" value="76" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>318</line>
          <option name="timeStamp" value="79" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$PROJECT_DIR$/../../../../apache-maven-3.6.3/repo/com/xuxueli/xxl-job-admin/2.5.0/xxl-job-admin-2.5.0-sources.jar!/com/xxl/job/admin/controller/JobLogController.java</url>
          <line>119</line>
          <option name="timeStamp" value="80" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncInstanceService.java</url>
          <line>289</line>
          <option name="timeStamp" value="81" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>299</line>
          <option name="timeStamp" value="86" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>335</line>
          <option name="timeStamp" value="89" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/web/DatasyncJobController.java</url>
          <line>121</line>
          <option name="timeStamp" value="92" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/DataxFileRepository.java</url>
          <line>130</line>
          <option name="timeStamp" value="95" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/DataxFileRepository.java</url>
          <line>125</line>
          <option name="timeStamp" value="96" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/DataxFileRepository.java</url>
          <line>131</line>
          <option name="timeStamp" value="97" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncService.java</url>
          <line>190</line>
          <option name="timeStamp" value="98" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>146</line>
          <option name="timeStamp" value="99" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handlers/DataSourceJobHandlers.java</url>
          <line>251</line>
          <option name="timeStamp" value="101" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handlers/DataSourceJobHandlers.java</url>
          <line>362</line>
          <option name="timeStamp" value="103" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handlers/DataSourceJobHandlers.java</url>
          <line>396</line>
          <option name="timeStamp" value="104" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/imcc/service/impl/ImccResourceServiceImpl.java</url>
          <line>37</line>
          <option name="timeStamp" value="105" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/imcc/service/impl/ImccResourceServiceImpl.java</url>
          <line>47</line>
          <option name="timeStamp" value="106" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/imcc/service/impl/ImccResourceServiceImpl.java</url>
          <line>77</line>
          <option name="timeStamp" value="108" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handlers/DataSourceJobHandlers.java</url>
          <line>464</line>
          <option name="timeStamp" value="109" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/DatasyncRepository.java</url>
          <line>271</line>
          <option name="timeStamp" value="111" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handlers/DataSourceJobHandlers.java</url>
          <line>458</line>
          <option name="timeStamp" value="113" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handlers/DataSourceJobHandlers.java</url>
          <line>440</line>
          <option name="timeStamp" value="117" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/DataxFileRepository.java</url>
          <line>119</line>
          <option name="timeStamp" value="122" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$PROJECT_DIR$/../../../../apache-maven-3.6.3/repo/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar!/org/mybatis/spring/SqlSessionFactoryBean.class</url>
          <line>206</line>
          <option name="timeStamp" value="125" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handlers/DataSourceJobHandlers.java</url>
          <line>358</line>
          <option name="timeStamp" value="127" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handlers/DataSourceJobHandlers.java</url>
          <line>381</line>
          <option name="timeStamp" value="128" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>119</line>
          <option name="timeStamp" value="131" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handlers/DataSourceJobHandlers.java</url>
          <line>294</line>
          <option name="timeStamp" value="137" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handlers/DataSourceJobHandlers.java</url>
          <line>168</line>
          <option name="timeStamp" value="138" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-openapi/xotp-openapi-dataservice/src/main/java/com/xmcares/platform/openapi/dataservice/dataset/service/ServiceModelService.java</url>
          <line>72</line>
          <option name="timeStamp" value="139" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>320</line>
          <option name="timeStamp" value="143" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$PROJECT_DIR$/../../../../apache-maven-3.6.3/repo/com/xuxueli/xxl-job-admin/2.5.0/xxl-job-admin-2.5.0-sources.jar!/com/xxl/job/admin/controller/interceptor/PermissionInterceptor.java</url>
          <line>83</line>
          <option name="timeStamp" value="144" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncService.java</url>
          <line>124</line>
          <option name="timeStamp" value="154" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/DatasyncRepository.java</url>
          <line>198</line>
          <option name="timeStamp" value="155" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncService.java</url>
          <line>130</line>
          <option name="timeStamp" value="156" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/web/DatasyncController.java</url>
          <line>86</line>
          <option name="timeStamp" value="157" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/DataxFileRepository.java</url>
          <line>98</line>
          <option name="timeStamp" value="158" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/DataxFileRepository.java</url>
          <line>108</line>
          <option name="timeStamp" value="159" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/common/util/DataxFtlUtils.java</url>
          <line>68</line>
          <option name="timeStamp" value="160" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/common/util/DataxFtlUtils.java</url>
          <line>141</line>
          <option name="timeStamp" value="161" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/common/util/DataxFtlUtils.java</url>
          <line>153</line>
          <option name="timeStamp" value="162" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/common/util/DataxFtlUtils.java</url>
          <line>172</line>
          <option name="timeStamp" value="163" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/common/util/DataxFtlUtils.java</url>
          <line>175</line>
          <option name="timeStamp" value="164" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncService.java</url>
          <line>208</line>
          <option name="timeStamp" value="165" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>339</line>
          <option name="timeStamp" value="166" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>329</line>
          <option name="timeStamp" value="167" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>340</line>
          <option name="timeStamp" value="168" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>407</line>
          <option name="timeStamp" value="171" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>377</line>
          <option name="timeStamp" value="172" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>438</line>
          <option name="timeStamp" value="173" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncJobService.java</url>
          <line>262</line>
          <option name="timeStamp" value="174" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncService.java</url>
          <line>115</line>
          <option name="timeStamp" value="176" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncService.java</url>
          <line>109</line>
          <option name="timeStamp" value="177" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/DatasyncRepository.java</url>
          <line>190</line>
          <option name="timeStamp" value="178" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncService.java</url>
          <line>337</line>
          <option name="timeStamp" value="179" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncService.java</url>
          <line>232</line>
          <option name="timeStamp" value="181" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>100</line>
          <option name="timeStamp" value="182" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>177</line>
          <option name="timeStamp" value="192" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handler/XxlJobDatasyncJobHandler.java</url>
          <line>73</line>
          <option name="timeStamp" value="193" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/model/DatasyncJob.java</url>
          <line>238</line>
          <option name="timeStamp" value="194" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncJobService.java</url>
          <line>370</line>
          <option name="timeStamp" value="195" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncJobService.java</url>
          <line>206</line>
          <option name="timeStamp" value="198" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncJobService.java</url>
          <line>257</line>
          <option name="timeStamp" value="199" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handler/XxlJobDatasyncJobHandler.java</url>
          <line>37</line>
          <option name="timeStamp" value="200" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/manager/DatasyncJobManager.java</url>
          <line>124</line>
          <option name="timeStamp" value="201" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/manager/DatasyncJobManager.java</url>
          <line>122</line>
          <option name="timeStamp" value="202" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-common/src/main/java/com/xmcares/platform/hookbox/common/mq/artemis/ArtemisEmbeddedServer.java</url>
          <line>49</line>
          <option name="timeStamp" value="203" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-common/src/main/java/com/xmcares/platform/hookbox/common/mq/artemis/ArtemisEmbeddedServer.java</url>
          <line>32</line>
          <option name="timeStamp" value="204" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncService.java</url>
          <line>174</line>
          <option name="timeStamp" value="205" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/xxljob/model/XxlJobInfo.java</url>
          <line>85</line>
          <option name="timeStamp" value="206" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handler/XxlJobDatasyncJobHandler.java</url>
          <line>101</line>
          <option name="timeStamp" value="207" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncJobService.java</url>
          <line>175</line>
          <option name="timeStamp" value="208" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handler/XxlJobDatasyncJobHandler.java</url>
          <line>54</line>
          <option name="timeStamp" value="209" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/manager/DatasyncJobManager.java</url>
          <line>166</line>
          <option name="timeStamp" value="210" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/manager/DatasyncJobManager.java</url>
          <line>169</line>
          <option name="timeStamp" value="211" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/manager/DatasyncJobManager.java</url>
          <line>246</line>
          <option name="timeStamp" value="212" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/manager/DatasyncJobManager.java</url>
          <line>304</line>
          <option name="timeStamp" value="213" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/manager/DatasyncJobManager.java</url>
          <line>307</line>
          <option name="timeStamp" value="214" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/service/DatasyncJobInstanceService.java</url>
          <line>36</line>
          <option name="timeStamp" value="216" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/manager/DatasyncJobManager.java</url>
          <line>210</line>
          <option name="timeStamp" value="218" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/manager/DatasyncJobManager.java</url>
          <line>174</line>
          <option name="timeStamp" value="219" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>201</line>
          <option name="timeStamp" value="220" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-common/src/main/java/com/xmcares/platform/hookbox/common/job/seatunnel/RemoteSeaTunnelJobContextService.java</url>
          <line>126</line>
          <option name="timeStamp" value="223" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-common/src/main/java/com/xmcares/platform/hookbox/common/job/seatunnel/RemoteSeaTunnelJobContextService.java</url>
          <line>142</line>
          <option name="timeStamp" value="224" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handler/XxlJobDatasyncLogHandler.java</url>
          <line>105</line>
          <option name="timeStamp" value="232" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handler/XxlJobDatasyncLogHandler.java</url>
          <line>100</line>
          <option name="timeStamp" value="234" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/service/IntegratorXxlJobLogService.java</url>
          <line>87</line>
          <option name="timeStamp" value="237" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-scheduler/src/main/java/com/xmcares/platform/admin/scheduler/xxljob/controller/ExtendApiController.java</url>
          <line>91</line>
          <option name="timeStamp" value="241" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-scheduler/src/main/java/com/xmcares/platform/admin/scheduler/xxljob/controller/ExtendApiController.java</url>
          <line>116</line>
          <option name="timeStamp" value="243" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-scheduler/src/main/java/com/xmcares/platform/admin/scheduler/xxljob/controller/ExtendApiController.java</url>
          <line>118</line>
          <option name="timeStamp" value="244" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>434</line>
          <option name="timeStamp" value="248" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>222</line>
          <option name="timeStamp" value="254" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>127</line>
          <option name="timeStamp" value="255" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>266</line>
          <option name="timeStamp" value="256" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>116</line>
          <option name="timeStamp" value="257" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/SeaTunnelConfigRepository.java</url>
          <line>102</line>
          <option name="timeStamp" value="258" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/service/DatasyncJobService.java</url>
          <line>277</line>
          <option name="timeStamp" value="259" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handler/XxlJobDatasyncLogHandler.java</url>
          <line>132</line>
          <option name="timeStamp" value="260" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handler/XxlJobDatasyncLogHandler.java</url>
          <line>139</line>
          <option name="timeStamp" value="261" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/service/IntegratorXxlJobLogService.java</url>
          <line>121</line>
          <option name="timeStamp" value="262" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/repository/XxlJobSchedulerRepository.java</url>
          <line>353</line>
          <option name="timeStamp" value="265" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-integrator/src/main/java/com/xmcares/platform/admin/integrator/datasync/web/DatasyncJobController.java</url>
          <line>150</line>
          <option name="timeStamp" value="267" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-admin/xotp-admin-scheduler/src/main/java/com/xmcares/platform/admin/scheduler/xxljob/controller/ExtendApiController.java</url>
          <line>280</line>
          <option name="timeStamp" value="272" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/manager/DatasyncJobManager.java</url>
          <line>262</line>
          <option name="timeStamp" value="274" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/handler/SeaTunnelJobEventHandler.java</url>
          <line>67</line>
          <option name="timeStamp" value="276" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/service/SeaTunnelJobEventService.java</url>
          <line>159</line>
          <option name="timeStamp" value="277" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/service/SeaTunnelJobEventService.java</url>
          <line>155</line>
          <option name="timeStamp" value="278" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-hookbox/xotp-hookbox-integrator/src/main/java/com/xmcares/platform/hookbox/integrator/service/SeaTunnelJobEventService.java</url>
          <line>167</line>
          <option name="timeStamp" value="279" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/xotp-seatunnel/xotp-seatunnel-connectors/connector-starter/src/main/java/com/xmcares/platform/seatunnel/starter/SeaTunnelEngineLocalExample.java</url>
          <line>55</line>
          <option name="timeStamp" value="281" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>