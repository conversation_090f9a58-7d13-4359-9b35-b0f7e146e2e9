package com.xmcares.platform.admin.integrator.datasync.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.integrator.datasync.task.IRemoveData;
import com.xmcares.platform.admin.integrator.datasync.vo.SaveDatasync;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/2/22 14:14
 */
@ApiModel(value = "Datasync", description = "数据同步定义信息")
public class Datasync implements  IRemoveData, Serializable {

    public static final String JOB_PREFIX = "Datasync_Job_";

    public static final String ENTITY_NAME = "bdp_intg_datasync";
    public static final String FIELD_NAME = "intg_name";

    public static final String JOB_TYPE_BATCH = "batch";
    public static final String JOB_TYPE_STREAMING = "streaming";


    /** ID */
    @ApiModelProperty(value = "主键")
    private String id;
    /** 创建时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /** 更新时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /** 同步任务名称 */
    @ApiModelProperty(value = "同步任务名称")
    private String intgName;
    /** 数据来源集成方式 0:内置 1:插件 */
    @ApiModelProperty(value = "数据来源集成方式", notes = "0:内置 1:插件")
    private String orginType;
    /** 数据来源集成模型ID */
    @ApiModelProperty(value = "数据来源集成模型ID")
    private String orginIntgModelId;
    /** 数据来源数据源名称 */
    @ApiModelProperty(value = "数据来源数据源名称")
    private String orginDatasourceName;
    /** 数据来源数据源ID */
    @ApiModelProperty(value = "数据来源数据源ID")
    private String orginDatasourceId;
    /** 数据来源插件路径 */
    @ApiModelProperty(value = "数据来源插件路径")
    private String orginPluginPath;
    /** 数据来源基础信息实际Json数据 */
    @ApiModelProperty(value = "数据来源基础信息实际Json数据")
    private String orginBaseJson;
    /** 数据来源进阶信息实际Json数据 */
    @ApiModelProperty(value = "数据来源进阶信息实际Json数据")
    private String orginAdvJson;
    /** 数据来源高级信息实际Json数据 */
    @ApiModelProperty(value = "数据来源高级信息实际Json数据")
    private String orginHighJson;
    /** 数据来源列信息实际Json数据 */
    @ApiModelProperty(value = "数据来源列信息实际Json数据")
    private String orginColumnJson;
    /** 数据来源运行模式 */
    @ApiModelProperty(value = "数据来源运行模式")
    private String orginRunSchema;
    /** 数据去向集成方式 0:内置 1：插件 */
    @ApiModelProperty(value = "数据去向集成方式", notes = "0:内置 1:插件")
    private String destType;
    /** 数据去向集成模型ID */
    @ApiModelProperty(value = "数据去向集成模型ID")
    private String destIntgModelId;
    /** 数据去向数据源名称 */
    @ApiModelProperty(value = "数据去向数据源名称")
    private String destDatasourceName;
    /** 数据去向数据源ID */
    @ApiModelProperty(value = "数据去向数据源ID")
    private String destDatasourceId;
    /** 数据去向插件路径 */
    @ApiModelProperty(value = "数据去向插件路径")
    private String destPluginPath;
    /** 数据去向基础信息实际Json数据 */
    @ApiModelProperty(value = "数据去向基础信息实际Json数据")
    private String destBaseJson;
    /** 数据去向进阶信息实际Json数据 */
    @ApiModelProperty(value = "数据去向进阶信息实际Json数据")
    private String destAdvJson;
    /** 数据去向高级信息实际Json数据 */
    @ApiModelProperty(value = "数据去向高级信息实际Json数据")
    private String destHighJson;
    /** 数据去向列信息实际Json数据 */
    @ApiModelProperty(value = "数据去向列信息实际Json数据")
    private String destColumnJson;
    /** 调度参数 */
    @ApiModelProperty(value = "调度参数")
    private String schedulerExpr;
    /** 路由策略 */
    @ApiModelProperty(value = "路由策略")
    private String routeStrategy;
    /** 阻塞策略 */
    @ApiModelProperty(value = "阻塞策略")
    private String blockStrategy;
    /** 子任务ID组 */
    @ApiModelProperty(value = "子任务ID组")
    private String childJobid;
    /** 执行超时时间 */
    @ApiModelProperty(value = "执行超时时间")
    private int executorTimeout;
    /** 执行失败重试次数 */
    @ApiModelProperty(value = "执行失败重试次数")
    private int executorFailRetryCount;

    /** 是否删除 */
    @ApiModelProperty(value = "是否删除")
    private String hasDelete;

    /** 是否删除 */
    @ApiModelProperty(value = "实例名称")
    private String instanceName;


    @ApiModelProperty(value = "作业运行模式: BAT")
    private String jobMode;

    @ApiModelProperty(value = "字段映射JSON字符串")
    private String fieldMappingJson;

    public String getJobMode() {
        return jobMode;
    }

    public void setJobMode(String jobMode) {
        this.jobMode = jobMode;
    }

    public String getFieldMappingJson() {
        return fieldMappingJson;
    }

    public void setFieldMappingJson(String fieldMappingJson) {
        this.fieldMappingJson = fieldMappingJson;
    }

    @Override
    public String findId() {
        return getId();
    }

    @Override
    public String findName() {
        return getIntgName();
    }

    public void resetPageListInfo() {
        orginBaseJson = null;
        orginAdvJson = null;
        orginHighJson = null;
        orginColumnJson = null;
        destBaseJson = null;
        destAdvJson = null;
        destHighJson = null;
        destColumnJson = null;
    }

    public static Datasync createNewFrom(SaveDatasync addInfo) {
        Datasync result = new Datasync();
        result.setJobMode(addInfo.getJobMode());
        result.setFieldMappingJson(addInfo.getFieldMappingJson());
        result.setIntgName(addInfo.getIntgName());
        result.setOrginType(addInfo.getOrginType());
        result.setOrginIntgModelId(addInfo.getOrginIntgModelId());
        result.setOrginDatasourceName(addInfo.getOrginDatasourceName());
        result.setOrginDatasourceId(addInfo.getOrginDatasourceId());
        result.setOrginPluginPath(addInfo.getOrginPluginPath());
        result.setOrginBaseJson(addInfo.getOrginBaseJson());
        result.setOrginAdvJson(addInfo.getOrginAdvJson());
        result.setOrginHighJson(addInfo.getOrginHighJson());
        result.setOrginColumnJson(addInfo.getOrginColumnJson());
        result.setOrginRunSchema(addInfo.getOrginRunSchema());
        result.setDestType(addInfo.getDestType());
        result.setDestIntgModelId(addInfo.getDestIntgModelId());
        result.setDestDatasourceName(addInfo.getDestDatasourceName());
        result.setDestDatasourceId(addInfo.getDestDatasourceId());
        result.setDestPluginPath(addInfo.getDestPluginPath());
        result.setDestBaseJson(addInfo.getDestBaseJson());
        result.setDestAdvJson(addInfo.getDestAdvJson());
        result.setDestHighJson(addInfo.getDestHighJson());
        result.setDestColumnJson(addInfo.getDestColumnJson());
        result.setSchedulerExpr(addInfo.getSchedulerExpr());
        result.setRouteStrategy(addInfo.getRouteStrategy());
        result.setBlockStrategy(addInfo.getBlockStrategy());
        result.setExecutorTimeout(addInfo.getExecutorTimeout());
        result.setExecutorFailRetryCount(addInfo.getExecutorFailRetryCount());
        result.setHasDelete(YNEnum.NO.getIntCharCode());
        return result;
    }

    public static Datasync updateFrom(SaveDatasync saveDatasync) {
        Datasync result = new Datasync();
        result.setId(saveDatasync.getId());
        result.setIntgName(saveDatasync.getIntgName());
        result.setOrginType(saveDatasync.getOrginType());
        result.setOrginIntgModelId(saveDatasync.getOrginIntgModelId());
        result.setOrginDatasourceName(saveDatasync.getOrginDatasourceName());
        result.setOrginDatasourceId(saveDatasync.getOrginDatasourceId());
        result.setOrginPluginPath(saveDatasync.getOrginPluginPath());
        result.setOrginBaseJson(saveDatasync.getOrginBaseJson());
        result.setOrginAdvJson(saveDatasync.getOrginAdvJson());
        result.setOrginHighJson(saveDatasync.getOrginHighJson());
        result.setOrginColumnJson(saveDatasync.getOrginColumnJson());
        result.setOrginRunSchema(saveDatasync.getOrginRunSchema());
        result.setDestType(saveDatasync.getDestType());
        result.setDestIntgModelId(saveDatasync.getDestIntgModelId());
        result.setDestDatasourceName(saveDatasync.getDestDatasourceName());
        result.setDestDatasourceId(saveDatasync.getDestDatasourceId());
        result.setDestPluginPath(saveDatasync.getDestPluginPath());
        result.setDestBaseJson(saveDatasync.getDestBaseJson());
        result.setDestAdvJson(saveDatasync.getDestAdvJson());
        result.setDestHighJson(saveDatasync.getDestHighJson());
        result.setDestColumnJson(saveDatasync.getDestColumnJson());
        result.setSchedulerExpr(saveDatasync.getSchedulerExpr());
        result.setRouteStrategy(saveDatasync.getRouteStrategy());
        result.setBlockStrategy(saveDatasync.getBlockStrategy());
        result.setExecutorTimeout(saveDatasync.getExecutorTimeout());
        result.setExecutorFailRetryCount(saveDatasync.getExecutorFailRetryCount());
        result.setChildJobid(saveDatasync.getChildJobid());
        result.setFieldMappingJson(saveDatasync.getFieldMappingJson());
        return result;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getIntgName() {
        return intgName;
    }

    public void setIntgName(String intgName) {
        this.intgName = intgName;
    }

    public String getOrginType() {
        return orginType;
    }

    public void setOrginType(String orginType) {
        this.orginType = orginType;
    }

    public String getOrginIntgModelId() {
        return orginIntgModelId;
    }

    public void setOrginIntgModelId(String orginIntgModelId) {
        this.orginIntgModelId = orginIntgModelId;
    }

    public String getOrginDatasourceName() {
        return orginDatasourceName;
    }

    public void setOrginDatasourceName(String orginDatasourceName) {
        this.orginDatasourceName = orginDatasourceName;
    }

    public String getOrginDatasourceId() {
        return orginDatasourceId;
    }

    public void setOrginDatasourceId(String orginDatasourceId) {
        this.orginDatasourceId = orginDatasourceId;
    }

    public String getOrginPluginPath() {
        return orginPluginPath;
    }

    public void setOrginPluginPath(String orginPluginPath) {
        this.orginPluginPath = orginPluginPath;
    }

    public String getOrginBaseJson() {
        return orginBaseJson;
    }

    public void setOrginBaseJson(String orginBaseJson) {
        this.orginBaseJson = orginBaseJson;
    }

    public String getOrginAdvJson() {
        return orginAdvJson;
    }

    public void setOrginAdvJson(String orginAdvJson) {
        this.orginAdvJson = orginAdvJson;
    }

    public String getOrginHighJson() {
        return orginHighJson;
    }

    public void setOrginHighJson(String orginHighJson) {
        this.orginHighJson = orginHighJson;
    }

    public String getOrginColumnJson() {
        return orginColumnJson;
    }

    public void setOrginColumnJson(String orginColumnJson) {
        this.orginColumnJson = orginColumnJson;
    }

    public String getDestType() {
        return destType;
    }

    public void setDestType(String destType) {
        this.destType = destType;
    }

    public String getDestIntgModelId() {
        return destIntgModelId;
    }

    public void setDestIntgModelId(String destIntgModelId) {
        this.destIntgModelId = destIntgModelId;
    }

    public String getDestDatasourceName() {
        return destDatasourceName;
    }

    public void setDestDatasourceName(String destDatasourceName) {
        this.destDatasourceName = destDatasourceName;
    }

    public String getDestDatasourceId() {
        return destDatasourceId;
    }

    public void setDestDatasourceId(String destDatasourceId) {
        this.destDatasourceId = destDatasourceId;
    }

    public String getDestPluginPath() {
        return destPluginPath;
    }

    public void setDestPluginPath(String destPluginPath) {
        this.destPluginPath = destPluginPath;
    }

    public String getDestBaseJson() {
        return destBaseJson;
    }

    public void setDestBaseJson(String destBaseJson) {
        this.destBaseJson = destBaseJson;
    }

    public String getDestAdvJson() {
        return destAdvJson;
    }

    public void setDestAdvJson(String destAdvJson) {
        this.destAdvJson = destAdvJson;
    }

    public String getDestHighJson() {
        return destHighJson;
    }

    public void setDestHighJson(String destHighJson) {
        this.destHighJson = destHighJson;
    }

    public String getDestColumnJson() {
        return destColumnJson;
    }

    public void setDestColumnJson(String destColumnJson) {
        this.destColumnJson = destColumnJson;
    }

    public String getSchedulerExpr() {
        return schedulerExpr;
    }

    public void setSchedulerExpr(String schedulerExpr) {
        this.schedulerExpr = schedulerExpr;
    }

    public String getOrginRunSchema() {
        return orginRunSchema;
    }

    public void setOrginRunSchema(String orginRunSchema) {
        this.orginRunSchema = orginRunSchema;
    }

    public String getRouteStrategy() {
        return routeStrategy;
    }

    public void setRouteStrategy(String routeStrategy) {
        this.routeStrategy = routeStrategy;
    }

    public String getBlockStrategy() {
        return blockStrategy;
    }

    public void setBlockStrategy(String blockStrategy) {
        this.blockStrategy = blockStrategy;
    }

    public int getExecutorTimeout() {
        return executorTimeout;
    }

    public void setExecutorTimeout(int executorTimeout) {
        this.executorTimeout = executorTimeout;
    }

    public int getExecutorFailRetryCount() {
        return executorFailRetryCount;
    }

    public void setExecutorFailRetryCount(int executorFailRetryCount) {
        this.executorFailRetryCount = executorFailRetryCount;
    }

    public String getHasDelete() {
        return hasDelete;
    }

    public void setHasDelete(String hasDelete) {
        this.hasDelete = hasDelete;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public String getChildJobid() {
        return childJobid;
    }

    public void setChildJobid(String childJobid) {
        this.childJobid = childJobid;
    }
}
