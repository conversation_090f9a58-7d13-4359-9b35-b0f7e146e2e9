{"groups": [{"name": "xbdp.integrator", "type": "com.xmcares.platform.admin.integrator.IntegratorProperties", "sourceType": "com.xmcares.platform.admin.integrator.IntegratorProperties"}, {"name": "xbdp.integrator.datax", "type": "com.xmcares.platform.admin.integrator.IntegratorProperties$DataxProperties", "sourceType": "com.xmcares.platform.admin.integrator.IntegratorProperties", "sourceMethod": "getDatax()"}, {"name": "xbdp.integrator.xxl-job", "type": "com.xmcares.platform.admin.integrator.IntegratorProperties$XxlJobProperties", "sourceType": "com.xmcares.platform.admin.integrator.IntegratorProperties", "sourceMethod": "getXxlJob()"}], "properties": [{"name": "xbdp.feign.scheduler-service.name", "type": "java.lang.String", "description": "Feign客户端调用的任务调度服务的名称, 默认为 'scheduler-service'.", "defaultValue": "scheduler-service"}, {"name": "xbdp.feign.scheduler-service.url", "type": "java.lang.String", "description": "Feign客户端调用的任务调度服务的URL."}, {"name": "xbdp.feign.scheduler-service.xxl-job.access-token", "type": "java.lang.String", "description": "Feign客户端调用基于XXL-Job的任务调度服务的访问令牌.", "defaultValue": "default_token"}, {"name": "xbdp.integrator.file-server-root", "type": "java.lang.String", "description": "数据集成模块的资源根路径，默认为/xbdp/integrator", "sourceType": "com.xmcares.platform.admin.integrator.IntegratorProperties"}, {"name": "xbdp.integrator.local-tmp-root", "type": "java.lang.String", "description": "本地临时文件的根目录，默认为${user.dir}/tmp/integrator", "sourceType": "com.xmcares.platform.admin.integrator.IntegratorProperties"}, {"name": "xbdp.integrator.xxl-job.alarm-email", "type": "java.lang.String", "description": "调度任务执行时的告警邮箱", "sourceType": "com.xmcares.platform.admin.integrator.IntegratorProperties$XxlJobProperties"}, {"name": "xbdp.integrator.xxl-job.group", "type": "java.lang.String", "description": "勾盒服务应用的名称", "sourceType": "com.xmcares.platform.admin.integrator.IntegratorProperties$XxlJobProperties"}], "hints": []}