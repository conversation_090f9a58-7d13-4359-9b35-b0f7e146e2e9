/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.sink;

import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.connector.TableSink;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableSinkFactory;
import org.apache.seatunnel.api.table.factory.TableSinkFactoryContext;

import com.google.auto.service.AutoService;

import static com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttOptions.*;

@AutoService(Factory.class)
public class MqttSinkFactory implements TableSinkFactory {

    @Override
    public String factoryIdentifier() {
        return "Mqtt";
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder()
                // 必填参数
                .required(BROKER_URLS)
                // Topic相关参数，必须选择其中一个
                .exclusive(TOPIC, TOPIC_PATTERN)
                // 可选参数
                .optional(
                        CLIENT_ID,
                        USERNAME,
                        PASSWORD,
                        CONNECTION_TIMEOUT,
                        KEEP_ALIVE_INTERVAL,
                        CLEAN_SESSION,
                        SSL_ENABLED,
                        QOS,
                        MESSAGE_FORMAT,
                        RETAINED,
                        MAX_INFLIGHT_MESSAGES,
                        INCLUDE_METADATA,
                        AUTO_RECONNECT,
                        MAX_RECONNECT_DELAY)
                .build();
    }

    @Override
    public TableSink createSink(TableSinkFactoryContext context) {
        return () -> new MqttSink(context.getCatalogTable(), context.getOptions());
    }
}
