package com.xmcares.platform.admin.integrator.datasync.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.integrator.common.error.IntegratorException;
import com.xmcares.platform.admin.integrator.datasync.dto.JobLogPageListDto;
import com.xmcares.platform.admin.integrator.datasync.dto.JobLogPageVo;
import com.xmcares.platform.admin.integrator.datasync.model.Datasync;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncInstance;
import com.xmcares.platform.admin.integrator.datasync.model.SchedulerJob;
import com.xmcares.platform.admin.integrator.datasync.repository.DatasyncInstanceRepository;
import com.xmcares.platform.admin.integrator.datasync.repository.DataxFileRepository;
import com.xmcares.platform.admin.integrator.datasync.repository.SchedulerRepository;
import com.xmcares.platform.admin.integrator.datasync.repository.SeatunnelSchedulerRepository;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobGroup;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobInfo;
import com.xmcares.platform.admin.integrator.datasync.task.IRemoveDataService;
import com.xmcares.platform.admin.integrator.datasync.vo.DisplayDataSyncTask;
import com.xmcares.platform.admin.integrator.datasync.vo.UpdateDatasyncTask;
import com.xxl.job.core.biz.model.LogResult;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/17 9:21
 */
@Service
public class DatasyncInstanceService implements IRemoveDataService<DatasyncInstance> {
    private static final Logger LOG = LoggerFactory.getLogger(DatasyncInstanceService.class);

    @Autowired
    private DatasyncInstanceRepository datasyncInstanceRepository;
    @Autowired
    private SchedulerRepository schedulerRepository;
    @Autowired
    private DataxFileRepository dataxFileRepository;

    @Autowired
    private SeatunnelSchedulerRepository seatunnelSchedulerRepository;
    public DatasyncInstance checkExit(String id) {
        DatasyncInstance datasync = datasyncInstanceRepository.get(id);
        if(datasync == null) {
            throw new IntegratorException("数据同步任务不存在");
        }
        return datasync;
    }

    /**
     * 获取详细
     * @param id 任务表信息
     * @return
     */
    public DisplayDataSyncTask get(String id) {
        DatasyncInstance datasync = checkExit(id);
        Map<String, SchedulerJob> schedulerJobMap = schedulerRepository.querySchedulerJobs(Arrays.asList(datasync.getDispatchId()));
        SchedulerJob schedulerJob = schedulerJobMap.get(datasync.getDispatchId());
        DisplayDataSyncTask result = DisplayDataSyncTask.createBaseFrom(datasync);
        result.buildDispatchInfo(schedulerJob);
        return result;
    }

    /**
     * 根据数据同步定义表ID获取同步任务表数据
     * @param parentId 同步定义表ID
     * @return 同步任务表 + 调度表数据
     */
    public List<DisplayDataSyncTask> findByParentId(String parentId) {
        List<DatasyncInstance> findResult = datasyncInstanceRepository.listByParentId(parentId);
        if (findResult.isEmpty()) {
            return new ArrayList<>();
        }
        List<DisplayDataSyncTask> resultData = findResult.stream().map(DisplayDataSyncTask::createBaseFrom).collect(Collectors.toList());
        List<String> jobIds = findResult.stream().map(DatasyncInstance::getDispatchId).collect(Collectors.toList());
        //获取调度任务信息
        if(!jobIds.isEmpty()) {
            Map<String, SchedulerJob> schedulerJobMap = schedulerRepository.querySchedulerJobs(jobIds);
            resultData.forEach((item) -> {
                String key = item.getDispatchId();
                if(schedulerJobMap.containsKey(key)) {
                    item.buildDispatchInfo(schedulerJobMap.get(key));
                }
            });
        }
        return resultData;
    }

    /**
     * 更新调度信息
     * @param datasyncTask 调度信息
     */
    public void update(UpdateDatasyncTask datasyncTask) {
        DatasyncInstance oldTask = checkExit(datasyncTask.getId());
        if (isRun(oldTask.getDispatchId())) {
            throw new IntegratorException("数据同步运行中，不允许修改或删除");
        }
        Datasync datasync = UpdateDatasyncTask.toDatasync(datasyncTask);
        schedulerRepository.updateScheduler(oldTask.getDispatchId(), datasync);
        // TODO: 现在需要更新本地的调度信息，远程回调成功后，再修改本地的
        datasyncInstanceRepository.update(datasyncTask);
    }

    /**
     * 开始运行调度任务
     * @param id 同步任务表ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void begin(String id) {
        //1. 校验状态等信息后才允许开始运行：启动就不要再启动
        DatasyncInstance datasync = this.checkExit(id);
        if (isRun(datasync.getDispatchId())) {
            throw new IntegratorException("任务已运行，请先停止运行");
        }
        //2. 上傳一份文件到服務
        // dataxFileRepository.upload(datasync.getFilePath(), datasync.getTemplateContext());
        //3. 開始運行
        schedulerRepository.beginScheduler(datasync.getDispatchId());
        // 修改triggerStatus和updateTime
        datasyncInstanceRepository.updateTriggerStatusAndTime(datasync.getId(),
                YNEnum.YES.getIntCode(), new Date());
    }

    /**
     * 停止运行
     * @param id 同步任务表ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void end(String id) {
        //校验状态等信息后才允许开始运行：停止就不要再停止
        DatasyncInstance datasync = this.checkExit(id);
        if (!isRun(datasync.getDispatchId())) {
            throw new IntegratorException("任务已停止，请勿重复停止");
        }
        schedulerRepository.endScheduler(datasync.getDispatchId());
        // 修改triggerStatus和updateTime
        datasyncInstanceRepository.updateTriggerStatusAndTime(datasync.getId(),
                YNEnum.NO.getIntCode(), new Date());
    }

    /**
     * 运行一次
     * @param id 同步任务表ID
     */
    public void triggerScheduler(String id) {
        //1. 校验状态等信息后才允许开始运行：启动就不要再启动
        DatasyncInstance datasync = this.checkExit(id);
        if (isRun(datasync.getDispatchId())) {
            throw new IntegratorException("任务已运行，请先停止运行");
        }
        //2. 從新上傳一份文件到服務 不再需要上传的文件
        // dataxFileRepository.upload(datasync.getFilePath(), datasync.getTemplateContext());
        //3. 运行一次
        schedulerRepository.triggerScheduler(datasync.getDispatchId());
    }

    /**
     * 将状态变更成已经删除
     * @param id 同步信息定义ID
     */
    public Boolean transitionToDelete(String id) {
        // 1. 验证运行状态
        DatasyncInstance datasync = this.checkExit(id);
        if (isRun(datasync.getDispatchId())) {
            throw new IntegratorException("任务已运行，请先停止运行");
        }
        // 1.先删除xxl-job处的数据，再删除本地存储的那份
        schedulerRepository.removeScheduler(datasync.getDispatchId());
        // 2. 执行假删除操作
        return datasyncInstanceRepository.changeHasDelete(id, YNEnum.YES.getIntCharCode());
    }

    @Override
    public Page<DatasyncInstance> findNeedDeletePage(Pagination pagination) {
        return datasyncInstanceRepository.queryNeedDeletePage(new Page<>(pagination.getPageNo() - 1, pagination.getPageSize()));
    }

    @Override
    public Boolean removeData(String id) {
        DatasyncInstance datasync = datasyncInstanceRepository.get(id);
        if (datasync == null) { return true; }
        if (StringUtils.isNotEmpty(datasync.getDispatchId())) {
            try {
                schedulerRepository.removeScheduler(datasync.getDispatchId());
            } catch (Exception e) {
                LOG.error("在删除调度记录【" + datasync.getDispatchId() + "::" + datasync.getInstanceName() + "】的过程中遇到异常", e);
            }
        }
        if (StringUtils.isNotEmpty(datasync.getFilePath())) {
            try {
                dataxFileRepository.removeFile(datasync.getFilePath());
            } catch (Exception e) {
                LOG.error("在删除配置文件【" + datasync.getFilePath() + "::" + datasync.getInstanceName() + "】的过程中遇到异常", e);
            }
        }
        return datasyncInstanceRepository.remove(id);
    }

    private boolean isRun(String dispatchId) {
        Map<String, SchedulerJob> schedulerJobMap = schedulerRepository.querySchedulerJobs(Arrays.asList(dispatchId));
        SchedulerJob schedulerJob = schedulerJobMap.get(dispatchId);
        return schedulerJob.getTriggerStatus() == YNEnum.YES.getIntCode();
    }


    public List<DisplayDataSyncTask> list() {
        List<DatasyncInstance> findResult = datasyncInstanceRepository.list();
        if (findResult.isEmpty()) {
            return new ArrayList<>();
        }
        List<DisplayDataSyncTask> resultData = findResult.stream().map(DisplayDataSyncTask::createBaseFrom).collect(Collectors.toList());

        return resultData;

    }

    public List<String> nextTriggerTime(String scheduleType, String scheduleConf) {
        return schedulerRepository.nextTriggerTime(scheduleType, scheduleConf);
    }

    public Page<JobLogPageVo> jobLogPageList(JobLogPageListDto jobLogPageListDto, int page, int rows) {
        Map<String, Object> resultMap = schedulerRepository.jobLogPageList(jobLogPageListDto);

        // package result
        Page<JobLogPageVo> result = new Page<>();
        result.setPageNo(page);
        result.setPageSize(rows);
        result.setTotal(MapUtil.getInt(resultMap, "recordsTotal"));
        List<JobLogPageVo> jobLogPageVos = new ArrayList<>();
        List<Map<String, Object>> mapListDataObject = MapUtil.get(resultMap, "data", List.class, new ArrayList<JobLogPageVo>());
        // TODO 封装
        if (!CollectionUtils.isEmpty(mapListDataObject)) {
            // 组装jobLogPageVos
            mapListDataObject.forEach(item -> {
                JobLogPageVo jobLogPageVo = new JobLogPageVo();
                jobLogPageVo.setId(MapUtil.getLong(item, "id"));
                jobLogPageVo.setJobName(MapUtil.getStr(item, "jobName"));
                jobLogPageVo.setJobGroup(MapUtil.getInt(item, "jobGroup"));
                jobLogPageVo.setJobId(MapUtil.getInt(item, "jobId"));
                jobLogPageVo.setJobTaskType(MapUtil.getStr(item, "jobTaskType"));
                jobLogPageVo.setExecutorAddress(MapUtil.getStr(item, "executorAddress"));
                jobLogPageVo.setExecutorHandler(MapUtil.getStr(item, "executorHandler"));
                jobLogPageVo.setExecutorParam(MapUtil.getStr(item, "executorParam"));
                jobLogPageVo.setExecutorShardingParam(MapUtil.getStr(item, "executorShardingParam"));
                jobLogPageVo.setExecutorFailRetryCount(MapUtil.getInt(item, "executorFailRetryCount"));
                jobLogPageVo.setTriggerTime(MapUtil.getDate(item, "triggerTime"));
                jobLogPageVo.setTriggerCode(MapUtil.getInt(item, "triggerCode"));
                jobLogPageVo.setTriggerMsg(MapUtil.getStr(item, "triggerMsg"));
                jobLogPageVo.setHandleTime(MapUtil.getDate(item, "handleTime"));
                jobLogPageVo.setHandleCode(MapUtil.getInt(item, "handleCode"));
                jobLogPageVo.setHandleMsg(MapUtil.getStr(item, "handleMsg"));
                jobLogPageVo.setAlarmStatus(MapUtil.getInt(item, "alarmStatus"));
                jobLogPageVo.setProcessId(MapUtil.getStr(item, "processId"));
                jobLogPageVo.setMaxId(MapUtil.getStr(item, "maxId"));
                jobLogPageVos.add(jobLogPageVo);
            });
            List<String> collect = jobLogPageVos.stream().map(item -> item.getJobId() + "").collect(Collectors.toList());
            List<DatasyncInstance> datasyncInstances = datasyncInstanceRepository.listByIds(collect);
            if (!CollectionUtils.isEmpty(datasyncInstances)) {
                Map<String, List<DatasyncInstance>> collectByDispatchId = datasyncInstances.stream().collect(Collectors.groupingBy(DatasyncInstance::getDispatchId));
                // 防止本地不存在对应的日志
                if (MapUtil.isNotEmpty(collectByDispatchId)) {
                    jobLogPageVos.forEach(item -> {
                        List<DatasyncInstance> datasyncInstances1 = collectByDispatchId.get(item.getJobId() + "");
                        if (!CollectionUtils.isEmpty(datasyncInstances1)) {
                            item.setJobName(datasyncInstances1.get(0).getInstanceName());
                        } else {
                            item.setJobName("jobId:" + item.getJobId() + "，对应的jobName不存在");
                        }
                    });
                    // result.setData(jobLogPageVos);
                }
            }
        }
        result.setData(jobLogPageVos);
        return result;
    }

    public List<XxlJobGroup> jobGroupList() {
        return schedulerRepository.findAllJobGroup();
    }

    public List<XxlJobInfo> getJobsByGroup(int jobGroup) {
        return schedulerRepository.getJobsByGroup(jobGroup);
    }

    public Boolean clearLog(int jobGroup, int jobId, int type) {
        return schedulerRepository.clearLog(jobGroup, jobId, type);
    }

    public Boolean logKill(long jobId) {
        return schedulerRepository.logKill(jobId);
    }

    public LogResult logDetailCat(long logId, int fromLineNum) {
        return schedulerRepository.logDetailCat(logId, fromLineNum);
    }


    public List<String> retrieveLogList() {
        return seatunnelSchedulerRepository.getLogList();
    }

    public String retrieveLogFileContent(String logFileName) {
        return seatunnelSchedulerRepository.getLogFileContent(logFileName);
    }
}
