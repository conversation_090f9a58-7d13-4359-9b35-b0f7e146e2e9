<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xmcares.platform</groupId>
        <artifactId>xotp-seatunnel</artifactId>
        <version>1.0.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>connector-starter</artifactId>

    <properties>

    </properties>

    <dependencies>
        <!--   seatunnel core dependencies   -->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-starter</artifactId>
            <version>${seatunnel.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-transforms-v2</artifactId>
            <version>${seatunnel.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-hadoop3-3.1.4-uber</artifactId>
            <version>${seatunnel.version}</version>
        </dependency>

        <!--   seatunnel connectors   -->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-fake</artifactId>
            <version>${seatunnel.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-console</artifactId>
            <version>${seatunnel.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-assert</artifactId>
            <version>${seatunnel.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xmcares.platform</groupId>
            <artifactId>connector-mqtt</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>
