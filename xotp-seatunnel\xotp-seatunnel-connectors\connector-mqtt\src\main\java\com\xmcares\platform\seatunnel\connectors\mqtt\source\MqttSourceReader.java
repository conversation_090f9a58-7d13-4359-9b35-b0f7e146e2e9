/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.source;

import com.xmcares.platform.seatunnel.connectors.mqtt.client.MqttClientManager;
import com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttSourceConfig;
import com.xmcares.platform.seatunnel.connectors.mqtt.exception.MqttConnectorErrorCode;
import com.xmcares.platform.seatunnel.connectors.mqtt.exception.MqttConnectorException;
import org.apache.seatunnel.api.serialization.DeserializationSchema;
import org.apache.seatunnel.api.source.Boundedness;
import org.apache.seatunnel.api.source.Collector;
import org.apache.seatunnel.api.source.SourceReader;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;

import org.apache.seatunnel.format.json.JsonDeserializationSchema;
import org.eclipse.paho.client.mqttv3.IMqttMessageListener;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

public class MqttSourceReader implements SourceReader<SeaTunnelRow, MqttSourceSplit> {

    private static final Logger log = LoggerFactory.getLogger(MqttSourceReader.class);


    private final SourceReader.Context context;
    private final MqttSourceConfig config;
    private final Deque<MqttSourceSplit> splits = new ConcurrentLinkedDeque<>();
    private MqttClientManager clientManager;
    private volatile boolean noMoreSplit;
    private final Object lock = new Object();


    // 添加订阅状态管理
    private final Set<String> subscribedTopics = ConcurrentHashMap.newKeySet();
    private volatile boolean hasSubscribed = false;

    // 使用seatunnel提供的反序列化schema
    private final DeserializationSchema<SeaTunnelRow> deserializationSchema;

    // 添加消息队列用于同步处理
    private final BlockingQueue<SeaTunnelRow> messageQueue;

    private final AtomicLong totalMessagesReceived = new AtomicLong(0);
    private final AtomicLong totalMessagesProcessed = new AtomicLong(0);

    private long lastMessageTimestamp = -1;
    private final boolean isBatchMode;

    public MqttSourceReader(SourceReader.Context context, MqttSourceConfig config, DeserializationSchema<SeaTunnelRow> deserializationSchema) {
        this.context = context;
        this.config = config;
        this.deserializationSchema = deserializationSchema;
        this.isBatchMode = Boundedness.BOUNDED.equals(context.getBoundedness());
        // 构造有界队列，防止队列过大 OOM
        final int queueCapacity = 10000;
        this.messageQueue = new LinkedBlockingQueue<>(queueCapacity);
    }

    @Override
    public void open() throws Exception {
        log.info("Opening MQTT source reader");
        this.clientManager = new MqttClientManager(config);
        this.clientManager.connect();
    }

    @Override
    public void close() throws IOException {
        log.info("Closing MQTT source reader, total messages received: {}, processed: {}",
                totalMessagesReceived.get(), totalMessagesProcessed.get());
        if (clientManager != null) {
            clientManager.close();
        }
    }


    @Override
    public void pollNext(Collector<SeaTunnelRow> output) throws Exception {
        synchronized (lock) {
            // 首次处理split时执行订阅
            if (!hasSubscribed && !splits.isEmpty()) {
                // 使用poll()而不是peek()来处理并移除split
                MqttSourceSplit split = splits.poll();
                if (split != null) {
                    subscribeToTopics(split);
                    hasSubscribed = true;
                    log.info("MQTT topics subscription completed for split: {}", split.getSplitId());
                    // 在批处理模式下，订阅成功后就初始化时间戳
                    if (isBatchMode) {
                        lastMessageTimestamp = System.currentTimeMillis();
                    }
                }
            }
        }
        // 每次最多处理256条，可配置
        final int maxRecordsPerPoll = 256;
        int recordsPolled = 0;

        while (recordsPolled < maxRecordsPerPoll) {
            // 使用带超时的 poll，避免CPU空转，同时给予一定的等待时间
            SeaTunnelRow message = messageQueue.poll(50, TimeUnit.MILLISECONDS);

            if (message != null) {
                output.collect(message);
                totalMessagesProcessed.incrementAndGet();
                recordsPolled++;
                // 批处理模式下的时间戳更新逻辑
                if (isBatchMode) {
                    lastMessageTimestamp = System.currentTimeMillis();
                }
            } else {
                // 如果在50ms内没有取到消息，说明队列暂时为空，
                // 直接跳出循环，将控制权交还给引擎。
                break;
            }
        }

        // 如果本次没有拉取到任何数据，并且是批处理模式，则检查不活跃超时
        if (recordsPolled == 0 && isBatchMode) {
            if (noMoreSplit && config.getBatchInactivityTimeout() > 0) {
                long idleTime = System.currentTimeMillis() - lastMessageTimestamp;
                if (idleTime > config.getBatchInactivityTimeout()) {
                    log.info(
                            "No messages received for {} ms. Signaling no more elements for BATCH mode.",
                            idleTime);
                    context.signalNoMoreElement();
                }
            }
        }
    }

    @Override
    public List<MqttSourceSplit> snapshotState(long checkpointId) throws Exception {
        return new ArrayList<>(splits);
    }

    @Override
    public void addSplits(List<MqttSourceSplit> splits) {
        log.info("Adding {} splits to MQTT source reader", splits.size());
        synchronized (lock) {
            this.splits.addAll(splits);
            // 重置订阅状态，当有新split时需要重新订阅
            if (!splits.isEmpty()) {
                hasSubscribed = false;
            }
        }
    }

    @Override
    public void handleNoMoreSplits() {
        log.info("No more splits for MQTT source reader");
        this.noMoreSplit = true;
    }

    @Override
    public void notifyCheckpointComplete(long checkpointId) throws Exception {
        // MQTT source不需要特殊的checkpoint完成处理
    }

    @Override
    public void notifyCheckpointAborted(long checkpointId) throws Exception {
        // MQTT source不需要特殊的checkpoint中止处理
    }

    private void subscribeToTopics(MqttSourceSplit split) {
        try {
            MqttClient client = clientManager.getClient();
            List<String> topics = split.getTopics();
            int qos = split.getQos();

            for (String topic : topics) {
                // 检查是否已经订阅过该topic
                if (!subscribedTopics.contains(topic)) {
                    client.subscribe(topic, qos, new MqttMessageHandler(topic));
                    subscribedTopics.add(topic);
                    log.info("Subscribed to MQTT topic: {} with QoS: {}", topic, qos);
                } else {
                    log.debug("Topic {} already subscribed, skipping", topic);
                }
            }
        } catch (Exception e) {
            throw new MqttConnectorException(
                    MqttConnectorErrorCode.SUBSCRIPTION_FAILED,
                    "Failed to subscribe to MQTT topics",
                    e);
        }
    }

    private class MqttMessageHandler implements IMqttMessageListener {
        private final String topic;
        private final AtomicLong messageCounter = new AtomicLong(0);
        private final AtomicLong errorCounter = new AtomicLong(0);

        public MqttMessageHandler(String topic) {
            this.topic = topic;
        }

        @Override
        public void messageArrived(String topic, MqttMessage message) throws Exception {
            // 增加消息计数，无论处理成功与否
            long totalMessages = messageCounter.incrementAndGet();
            totalMessagesReceived.incrementAndGet();

            try {
                byte[] payload = message.getPayload();
                String currentClientId = clientManager.getClientId();

                log.debug("Received MQTT message from topic: {}, payload size: {}, total messages: {}",
                        topic, payload.length, totalMessages);

                // 使用seatunnel提供的反序列化schema处理消息
                SeaTunnelRow row = deserializationSchema.deserialize(payload);
                //  if (deserializationSchema instanceof JsonDeserializationSchema) {
                //      // 对于JsonDeserializationSchema，使用deserialize方法
                //      row = deserializationSchema.deserialize(payload);
                //  } else {
                //      // 对于其他DeserializationSchema，使用deserialize方法
                //      row = deserializationSchema.deserialize(payload);
                //  }

                if (row != null) {
                    boolean success = messageQueue.offer(row);

                    if (success) {
                        log.info("Client-Id:{} Successfully processed MQTT message from topic: {}, row: {}", currentClientId, topic, row);
                    } else {
                        // 如果等待5秒后 offer() 仍然返回 false，说明队列持续为满
                        // 记录致命错误，并向上抛出运行时异常来使任务失败
                        errorCounter.incrementAndGet();
                        log.error(
                                "CRITICAL: Internal message queue is full (capacity: {}). Message from topic '{}' is being dropped. " +
                                        "This indicates a persistent downstream bottleneck. " +
                                        "Failing the task to prevent data loss and silent failures.",
                                messageQueue.size(),
                                topic
                        );
                        throw new RuntimeException("MQTT Source queue remained full after 5-second retry timeout, task is failing.");
                    }
                } else {
                    log.warn("Deserialized row is null for message from topic: {}", topic);
                    errorCounter.incrementAndGet();
                }

                // 每100条消息输出一次统计信息
                if (totalMessages % 100 == 0) {
                    log.info("Topic {} - Total messages: {}, Errors: {}, Success rate: {}%, Queue size: {}",
                            topic, totalMessages, errorCounter.get(),
                            ((totalMessages - errorCounter.get()) * 100 / totalMessages),
                            messageQueue.size());
                }

            } catch (Exception e) {
                // 如果是上面我们主动抛出的RuntimeException，则直接再次抛出
                if (e instanceof RuntimeException) {
                    throw e;
                }

                long errors = errorCounter.incrementAndGet();
                log.error("Failed to process MQTT message from topic: {}, message #{} (error #{})",
                        topic, totalMessages, errors, e);

                // 不要重新抛出异常，而是记录错误并继续处理
                log.error("Message payload: {}", new String(message.getPayload()));

                // 每10个错误输出一次统计
                if (errors % 10 == 0) {
                    log.warn("Topic {} - Error rate: {}% ({} errors out of {} messages)",
                            topic, (errors * 100 / totalMessages), errors, totalMessages);
                }
            }
        }

        public long getTotalMessages() {
            return messageCounter.get();
        }

        public long getErrorCount() {
            return errorCounter.get();
        }
    }

    // 添加指标获取方法
    public long getTotalMessagesReceived() {
        return totalMessagesReceived.get();
    }

    public long getTotalMessagesProcessed() {
        return totalMessagesProcessed.get();
    }

    public int getQueueSize() {
        return messageQueue.size();
    }
}

