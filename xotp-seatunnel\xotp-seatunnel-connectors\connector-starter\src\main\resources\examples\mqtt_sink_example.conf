env {
  job.mode = "STREAMING"
}

source {
  Mqtt {
    plugin_output = "fake"
    broker.urls = ["tcp://10.83.3.242:11883"]
    topics = ["iot/test/topic"]
    qos = 1
    message.format = "json"
    schema = {
      fields {
        id = "string"
        epoch = "int"
        model_name = "string"
      }
    }
  }
}

transform {
}

sink {
  Mqtt {
    plugin_input = "fake"
    broker.urls = ["tcp://10.83.3.242:11883"]
    topic = "iot/test/topic_2"
    message.format = "json"
    qos = 1
    retained  =false
  }
}
