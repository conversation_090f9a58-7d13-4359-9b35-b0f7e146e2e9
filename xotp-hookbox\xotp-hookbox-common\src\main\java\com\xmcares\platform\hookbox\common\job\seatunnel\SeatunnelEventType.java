package com.xmcares.platform.hookbox.common.job.seatunnel;

/**
 * SeatunnelEventType
 *
 * <AUTHOR>
 * @Descriptions SeatunnelEventType
 * @Date 2025/7/28 09:50
 */
public enum SeatunnelEventType {
    SCHEMA_CHANGE_ADD_COLUMN,
    SCHEMA_CHANGE_DROP_COLUMN,
    SCHEMA_CHANGE_MODIFY_COLUMN,
    SCHEMA_CHANGE_CHANGE_COLUMN,
    SCHEMA_CH<PERSON><PERSON>_UPDATE_COLUMNS,
    SCHEMA_CHANGE_RENAME_TABLE,
    LIFECYCLE_ENUMERATOR_OPEN,
    LIFECYCLE_ENUMERATOR_CLOSE,
    LIFECYCLE_READER_OPEN,
    LIFECYCLE_READER_CLOSE,
    LIFECYCLE_WRITER_CLOSE,
    REA<PERSON>R_MESSAGE_DELAYED,


}
