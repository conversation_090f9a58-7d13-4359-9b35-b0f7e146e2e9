import {Button, Input, message, Space, Tooltip} from "antd";
import React, {useRef, useState} from "react";
import {ModalForm} from "@ant-design/pro-form";
import {DownloadOutlined} from "@ant-design/icons";
import {findUnSyncTables} from "@/services/Metadata/DataSourceTable/DataSourceTable";
import CommonTable from "@/components/CommonTable";
import {messageModal} from "@/utils/messageModal";
import CustomIcon from "@/components/CustomIcon";

export default ({datasourceId, setTableName}) => {
    const [selectedRows, setSelectRows] = useState([]);
    const [dataSource, setDataSource] = useState({data: [], pageSize: 10000, total: 0});
    const [allTables, setAllTables] = useState([]);  // 新增状态存储所有数据
    const [modalVisible, setModalVisible] = useState(false);
    const formRef = useRef();
    const [searchValue, setSearchValue] = useState("");

    // 加载未同步的表
    const reloadTable = async () => {
        const res = await findUnSyncTables(datasourceId);
        if (res && res.syncTables && res.syncTables.length) {
            const data = res.syncTables.map(item => ({name: item}));
            const updatedDataSource = {data: [...data], pageSize: 10000, total: data.length};
            setDataSource(updatedDataSource);  // 更新表格数据
            setAllTables(updatedDataSource.data); // 存储所有表数据
        }
    };

    // 选择打开弹框
    const handleOpen = async () => {
        setDataSource({data: [], pageSize: 10000, total: 0}); // 清空数据源
        setSelectRows([]);
        setAllTables([])
        if (!datasourceId) {
            messageModal('warning', '请先选择数据源!');
            return;
        }
        setModalVisible(true);
        await reloadTable();  // 加载表数据
    };

    const handleRowSelectChange = (selectedRowKeys, selectedRows) => {
        setSelectRows(selectedRows);
    };

    // 查询函数
    const handleSearch = () => {
        if (searchValue) {
            const filteredData = allTables.filter(item =>
                item.name.toLowerCase().includes(searchValue.toLowerCase())
            );
            setDataSource({data: filteredData, pageSize: 10000, total: filteredData.length});  // 更新为过滤后的数据
        } else {
            // 如果搜索框为空，恢复所有表数据
            setDataSource({data: allTables, pageSize: 10000, total: allTables.length});
        }
    };

    const columns = [{
        title: '名称', dataIndex: 'name', align: 'center', ellipsis: {
            showTitle: false,
        }
    }, {
        title: '描述', dataIndex: 'desc', align: 'center', ellipsis: {
            showTitle: false,
        }
    },];

    return (
        <>
            <Space>
                <Tooltip title="获取数据源表,选择表可以置入表名称">
                    <div onClick={handleOpen} style={{
                        position: "absolute",
                        right: -25,
                        top: 0,
                        cursor:"pointer"
                    }}>
                        <CustomIcon type={"icon-shujukutongbu"} extraCommonProps={{
                            style: {
                                color: '#40a9ff',
                                fontSize: 30,

                            }
                        }}/>
                    </div>

                </Tooltip>
            </Space>
            <ModalForm
                visible={modalVisible}
                formRef={formRef}
                layout="horizontal"
                title="选择表名称"
                width={600}
                autoFocusFirstInput
                onVisibleChange={setModalVisible}
                modalProps={{
                    destroyOnClose: true,
                    onCancel: () => console.log('run'),
                }}
                onFinish={async () => {
                    try {
                        if (selectedRows && selectedRows.length) {
                            setTableName(selectedRows[0]?.name || "");
                            message.success('保存成功');
                            return true;
                        }
                        messageModal('warning', '请先选择一项!');
                        return false;
                    } catch (e) {
                        console.log(e);
                        message.error('保存失败');
                    }
                }}
            >
                <Space direction="vertical" size="middle" style={{display: 'flex'}}>
                    <Input.Group compact>
                        <Input
                            style={{width: '200px'}}
                            placeholder="请输入"
                            value={searchValue}
                            onChange={(e) => {
                                setSearchValue(e.target.value);
                            }}
                        />
                        <Button type="primary" onClick={handleSearch}>查询</Button>
                    </Input.Group>
                    <CommonTable
                        rowKey="name"
                        scrollY={300}
                        columns={columns}
                        data={dataSource}  // 这里使用保留的数据结构
                        selectType="radio"
                        selectedRows={selectedRows}
                        paginationShow={false}
                        onSelect={handleRowSelectChange}
                    />
                </Space>
            </ModalForm>
        </>
    );
};
