/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/29
 */
package com.xmcares.platform.hookbox.integrator.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
@ApiModel(description = "xotp.bdp_intg_datasync_job_instance")
@TableName(value = "xotp.bdp_intg_datasync_job_instance")
public class DatasyncJobInstance {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 同步作业ID
     */
    @TableField(value = "job_id")
    @ApiModelProperty(value = "同步作业ID")
    private String jobId;

    @TableField(value = "job_params")
    @ApiModelProperty(value = "")
    private String jobParams;

    @TableField(value = "schedule_id")
    @ApiModelProperty(value = "")
    private String scheduleId;

    @TableField(value = "schedule_log_id")
    @ApiModelProperty(value = "")
    private String scheduleLogId;

    /**
     * 启动时间
     */
    @TableField(value = "schedule_time")
    @ApiModelProperty(value = "启动时间")
    private Date scheduleTime;

    @TableField(value = "finish_time")
    @ApiModelProperty(value = "")
    private Date finishTime;

    /**
     * 作业实例执行的状态：0-Dead Started; 1-Running; 2-Finished; 3-Failed；4-Canceled/Stopped
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "作业实例执行的状态：0-Dead Started; 1-Running; 2-Finished; 3-Failed；4-Canceled/Stopped")
    private Byte status;

    /**
     * 状态消息
     */
    @TableField(value = "status_message")
    @ApiModelProperty(value = "状态消息")
    private String statusMessage;

    @TableField(value = "received_count")
    @ApiModelProperty(value = "")
    private Integer receivedCount;

    @TableField(value = "recevied_qps")
    @ApiModelProperty(value = "")
    private Double receviedQps;

    @TableField(value = "writed_count")
    @ApiModelProperty(value = "")
    private Integer writedCount;

    @TableField(value = "writed_qps")
    @ApiModelProperty(value = "")
    private Double writedQps;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;


    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 关联的DatasyncJob
     */
    @TableField(exist = false)
    private DatasyncJob datasyncJob;

    public DatasyncJob getDatasyncJob() {
        return datasyncJob;
    }

    public void setDatasyncJob(DatasyncJob datasyncJob) {
        this.datasyncJob = datasyncJob;
    }

    /**
     * 获取ID
     *
     * @return id - ID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置ID
     *
     * @param id ID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取同步作业ID
     *
     * @return job_id - 同步作业ID
     */
    public String getJobId() {
        return jobId;
    }

    /**
     * 设置同步作业ID
     *
     * @param jobId 同步作业ID
     */
    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    /**
     * @return job_params
     */
    public String getJobParams() {
        return jobParams;
    }

    /**
     * @param jobParams
     */
    public void setJobParams(String jobParams) {
        this.jobParams = jobParams;
    }

    /**
     * @return schedule_id
     */
    public String getScheduleId() {
        return scheduleId;
    }

    /**
     * @param scheduleId
     */
    public void setScheduleId(String scheduleId) {
        this.scheduleId = scheduleId;
    }

    /**
     * 获取启动时间
     *
     * @return schedule_time - 启动时间
     */
    public Date getScheduleTime() {
        return scheduleTime;
    }

    /**
     * 设置启动时间
     *
     * @param scheduleTime 启动时间
     */
    public void setScheduleTime(Date scheduleTime) {
        this.scheduleTime = scheduleTime;
    }

    /**
     * @return finish_time
     */
    public Date getFinishTime() {
        return finishTime;
    }

    /**
     * @param finishTime
     */
    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    /**
     * 获取作业实例执行的状态：0-Dead Started; 1-Running; 2-Finished; 3-Failed；4-Canceled/Stopped
     *
     * @return status - 作业实例执行的状态：0-Dead Started; 1-Running; 2-Finished; 3-Failed；4-Canceled/Stopped
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置作业实例执行的状态：0-Dead Started; 1-Running; 2-Finished; 3-Failed；4-Canceled/Stopped
     *
     * @param status 作业实例执行的状态：0-Dead Started; 1-Running; 2-Finished; 3-Failed；4-Canceled/Stopped
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取状态消息
     *
     * @return status_message - 状态消息
     */
    public String getStatusMessage() {
        return statusMessage;
    }

    /**
     * 设置状态消息
     *
     * @param statusMessage 状态消息
     */
    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    /**
     * @return received_count
     */
    public Integer getReceivedCount() {
        return receivedCount;
    }

    /**
     * @param receivedCount
     */
    public void setReceivedCount(Integer receivedCount) {
        this.receivedCount = receivedCount;
    }

    /**
     * @return recevied_qps
     */
    public Double getReceviedQps() {
        return receviedQps;
    }

    /**
     * @param receviedQps
     */
    public void setReceviedQps(Double receviedQps) {
        this.receviedQps = receviedQps;
    }

    /**
     * @return writed_count
     */
    public Integer getWritedCount() {
        return writedCount;
    }

    /**
     * @param writedCount
     */
    public void setWritedCount(Integer writedCount) {
        this.writedCount = writedCount;
    }

    /**
     * @return writed_qps
     */
    public Double getWritedQps() {
        return writedQps;
    }

    /**
     * @param writedQps
     */
    public void setWritedQps(Double writedQps) {
        this.writedQps = writedQps;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }


    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }


    public String getScheduleLogId() {
        return scheduleLogId;
    }

    public void setScheduleLogId(String scheduleLogId) {
        this.scheduleLogId = scheduleLogId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", jobId=").append(jobId);
        sb.append(", jobParams=").append(jobParams);
        sb.append(", scheduleId=").append(scheduleId);
        sb.append(", scheduleLogId=").append(scheduleLogId);
        sb.append(", scheduleTime=").append(scheduleTime);
        sb.append(", finishTime=").append(finishTime);
        sb.append(", status=").append(status);
        sb.append(", statusMessage=").append(statusMessage);
        sb.append(", receivedCount=").append(receivedCount);
        sb.append(", receviedQps=").append(receviedQps);
        sb.append(", writedCount=").append(writedCount);
        sb.append(", writedQps=").append(writedQps);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", datasyncJob=").append(datasyncJob);
        sb.append("]");
        return sb.toString();
    }

}
