package com.xmcares.platform.admin.integrator.datasync.vo;

import com.xmcares.platform.admin.common.validation.Insert;
import com.xmcares.platform.admin.common.validation.Update;
import com.xmcares.platform.admin.integrator.common.util.IntegratorMod;
import com.xmcares.platform.admin.integrator.datasync.model.Datasync;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 数据集成保存模型
 * <AUTHOR> chenYG
 * @date : 2022/3/28 10:54
 */
public class SaveDatasync implements Serializable {

    /** ID */
    @NotNull(message = "ID不允许为空", groups = Update.class)
    @ApiModelProperty(value = "主键", notes = "修改时不允许为空")
    private String id;
    /** 同步任务名称 */
    @NotEmpty(message = "名称不允许为空", groups = Insert.class)
    @NotEmpty(message = "名称不允许为空", groups = Update.class)
    @ApiModelProperty(value = "同步任务名称", required = true)
    private String intgName;
    /** 数据来源集成方式 0:内置 1:插件 */
    @NotEmpty(message = "orginType不允许为空", groups = Insert.class)
    @NotEmpty(message = "orginType不允许为空", groups = Update.class)
    @ApiModelProperty(value = "数据来源集成方式", required = true, notes = "0:内置 1:插件")
    private String orginType;
    /** 数据来源集成模型ID */
    @ApiModelProperty(value = "数据来源集成模型ID")
    private String orginIntgModelId;
    /** 数据来源数据源名称 */
    @ApiModelProperty(value = "数据来源数据源名称")
    private String orginDatasourceName;
    /** 数据来源数据源ID */
    @NotEmpty(message = "orginDatasourceId不允许为空", groups = Insert.class)
    @NotEmpty(message = "orginDatasourceId不允许为空", groups = Update.class)
    private String orginDatasourceId;
    /** 数据来源插件路径 */
    @ApiModelProperty(value = "数据来源插件路径")
    private String orginPluginPath;
    /** 插件名称 */
    @ApiModelProperty(value = "插件名称")
    private String orginPluginName;
    /** 数据来源基础信息实际Json数据 */
    @ApiModelProperty(value = "数据来源基础信息实际Json数据")
    private String orginBaseJson;
    /** 数据来源进阶信息实际Json数据 */
    @ApiModelProperty(value = "数据来源进阶信息实际Json数据")
    private String orginAdvJson;
    /** 数据来源高级信息实际Json数据 */
    @ApiModelProperty(value = "数据来源高级信息实际Json数据")
    private String orginHighJson;
    /** 数据来源列信息实际Json数据 */
    @ApiModelProperty(value = "数据来源列信息实际Json数据")
    private String orginColumnJson;
    /** 数据来源运行模式 */
    @ApiModelProperty(value = "数据来源运行模式")
    private String orginRunSchema;
    /** 数据去向集成方式 0:内置 1：插件 */
    @NotEmpty(message = "orginType不允许为空", groups = Insert.class)
    @NotEmpty(message = "orginType不允许为空", groups = Update.class)
    @ApiModelProperty(value = "数据去向集成方式", required = true, notes = "0:内置 1:插件")
    private String destType;
    /** 数据去向集成模型ID */
    @ApiModelProperty(value = "数据去向集成模型ID")
    private String destIntgModelId;
    /** 数据去向数据源名称 */
    @ApiModelProperty(value = "数据去向数据源名称")
    private String destDatasourceName;
    /** 数据去向数据源ID */
    @NotEmpty(message = "destDatasourceId不允许为空", groups = Insert.class)
    @NotEmpty(message = "destDatasourceId不允许为空", groups = Update.class)
    private String destDatasourceId;
    /** 数据去向插件路径 */
    @ApiModelProperty(value = "数据去向插件路径")
    private String destPluginPath;
    /** 插件名称 */
    @ApiModelProperty(value = "插件名称")
    private String destPluginName;
    /** 数据去向基础信息实际Json数据 */
    @ApiModelProperty(value = "数据去向基础信息实际Json数据")
    private String destBaseJson;
    /** 数据去向进阶信息实际Json数据 */
    @ApiModelProperty(value = "数据去向进阶信息实际Json数据")
    private String destAdvJson;
    /** 数据去向高级信息实际Json数据 */
    @ApiModelProperty(value = "数据去向高级信息实际Json数据")
    private String destHighJson;
    /** 数据去向列信息实际Json数据 */
    @ApiModelProperty(value = "数据去向列信息实际Json数据")
    private String destColumnJson;
    /** 调度任务ID */
    @ApiModelProperty(value = "调度任务ID")
    private String dispatchId;
    /** 调度参数 */
    @ApiModelProperty(value = "调度参数")
    private String schedulerExpr;
    /** 路由策略 */
    @ApiModelProperty(value = "路由策略")
    private String routeStrategy;
    /** 阻塞策略 */
    @ApiModelProperty(value = "阻塞策略")
    private String blockStrategy;
    /** 子任务id */
    @ApiModelProperty(value = "子任务id")
    private String childJobid;

    /** 执行超时时间 */
    @ApiModelProperty(value = "执行超时时间")
    private int executorTimeout;
    /** 执行失败重试次数 */
    @ApiModelProperty(value = "执行失败重试次数")
    private int executorFailRetryCount;

    /** 数据来源模式 */
    @ApiModelProperty(hidden = true)
    IntegratorMod fromMod;
    /** 数据去向模式 */
    @ApiModelProperty(hidden = true)
    private IntegratorMod toMod;

    @ApiModelProperty(value = "任务调度模式：BATCH/STREAMING")
    private String jobMode;

    @ApiModelProperty(value = "字段映射JSON字符串")
    private String fieldMappingJson;

    public IntegratorMod getFromMod() {
        if (fromMod == null) {
            fromMod = IntegratorMod.match(orginType, null);
        }
        return fromMod;
    }

    public IntegratorMod getToMod() {
        if (toMod == null) {
            toMod = IntegratorMod.match(destType, null);
        }
        return toMod;
    }

    public static SaveDatasync createFrom(Datasync datasync) {
        SaveDatasync result = new SaveDatasync();
        result.setId(datasync.getId());
        result.setIntgName(datasync.getIntgName());
        result.setOrginType(datasync.getOrginType());
        result.setOrginIntgModelId(datasync.getOrginIntgModelId());
        result.setOrginDatasourceName(datasync.getOrginDatasourceName());
        result.setOrginDatasourceId(datasync.getOrginDatasourceId());
        result.setOrginPluginPath(datasync.getOrginPluginPath());
        result.setOrginPluginName(datasync.getOrginDatasourceName());
        result.setOrginBaseJson(datasync.getOrginBaseJson());
        result.setOrginAdvJson(datasync.getOrginAdvJson());
        result.setOrginHighJson(datasync.getOrginHighJson());
        result.setOrginColumnJson(datasync.getOrginColumnJson());
        result.setDestType(datasync.getDestType());
        result.setDestIntgModelId(datasync.getDestIntgModelId());
        result.setDestDatasourceName(datasync.getDestDatasourceName());
        result.setDestDatasourceId(datasync.getDestDatasourceId());
        result.setDestPluginPath(datasync.getDestPluginPath());
        result.setDestPluginName(datasync.getDestDatasourceName());
        result.setDestBaseJson(datasync.getDestBaseJson());
        result.setDestAdvJson(datasync.getDestAdvJson());
        result.setDestHighJson(datasync.getDestHighJson());
        result.setDestColumnJson(datasync.getDestColumnJson());
        result.setSchedulerExpr(datasync.getSchedulerExpr());
        result.setChildJobid(datasync.getChildJobid());
        result.setRouteStrategy("");
        result.setBlockStrategy("");
        result.setExecutorTimeout(0);
        result.setExecutorFailRetryCount(0);
        result.setOrginRunSchema("");
        return result;
    }

    public String getChildJobid() {
        return childJobid;
    }

    public void setChildJobid(String childJobid) {
        this.childJobid = childJobid;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIntgName() {
        return intgName;
    }

    public void setIntgName(String intgName) {
        this.intgName = intgName;
    }

    public String getOrginType() {
        return orginType;
    }

    public void setOrginType(String orginType) {
        this.orginType = orginType;
    }

    public String getOrginIntgModelId() {
        return orginIntgModelId;
    }

    public void setOrginIntgModelId(String orginIntgModelId) {
        this.orginIntgModelId = orginIntgModelId;
    }

    public String getOrginDatasourceName() {
        return orginDatasourceName;
    }

    public void setOrginDatasourceName(String orginDatasourceName) {
        this.orginDatasourceName = orginDatasourceName;
    }

    public String getOrginDatasourceId() {
        return orginDatasourceId;
    }

    public void setOrginDatasourceId(String orginDatasourceId) {
        this.orginDatasourceId = orginDatasourceId;
    }

    public String getOrginPluginPath() {
        return orginPluginPath;
    }

    public void setOrginPluginPath(String orginPluginPath) {
        this.orginPluginPath = orginPluginPath;
    }

    public String getOrginPluginName() {
        return orginPluginName;
    }

    public void setOrginPluginName(String orginPluginName) {
        this.orginPluginName = orginPluginName;
    }

    public String getOrginBaseJson() {
        return orginBaseJson;
    }

    public void setOrginBaseJson(String orginBaseJson) {
        this.orginBaseJson = orginBaseJson;
    }

    public String getOrginAdvJson() {
        return orginAdvJson;
    }

    public void setOrginAdvJson(String orginAdvJson) {
        this.orginAdvJson = orginAdvJson;
    }

    public String getOrginHighJson() {
        return orginHighJson;
    }

    public void setOrginHighJson(String orginHighJson) {
        this.orginHighJson = orginHighJson;
    }

    public String getOrginColumnJson() {
        return orginColumnJson;
    }

    public void setOrginColumnJson(String orginColumnJson) {
        this.orginColumnJson = orginColumnJson;
    }


    public String getDestType() {
        return destType;
    }

    public void setDestType(String destType) {
        this.destType = destType;
    }

    public String getDestIntgModelId() {
        return destIntgModelId;
    }

    public void setDestIntgModelId(String destIntgModelId) {
        this.destIntgModelId = destIntgModelId;
    }

    public String getDestDatasourceName() {
        return destDatasourceName;
    }

    public void setDestDatasourceName(String destDatasourceName) {
        this.destDatasourceName = destDatasourceName;
    }

    public String getDestDatasourceId() {
        return destDatasourceId;
    }

    public void setDestDatasourceId(String destDatasourceId) {
        this.destDatasourceId = destDatasourceId;
    }

    public String getDestPluginPath() {
        return destPluginPath;
    }

    public void setDestPluginPath(String destPluginPath) {
        this.destPluginPath = destPluginPath;
    }

    public String getDestPluginName() {
        return destPluginName;
    }

    public void setDestPluginName(String destPluginName) {
        this.destPluginName = destPluginName;
    }

    public String getDestBaseJson() {
        return destBaseJson;
    }

    public void setDestBaseJson(String destBaseJson) {
        this.destBaseJson = destBaseJson;
    }

    public String getDestAdvJson() {
        return destAdvJson;
    }

    public void setDestAdvJson(String destAdvJson) {
        this.destAdvJson = destAdvJson;
    }

    public String getDestHighJson() {
        return destHighJson;
    }

    public void setDestHighJson(String destHighJson) {
        this.destHighJson = destHighJson;
    }

    public String getDestColumnJson() {
        return destColumnJson;
    }

    public void setDestColumnJson(String destColumnJson) {
        this.destColumnJson = destColumnJson;
    }

    public String getDispatchId() {
        return dispatchId;
    }

    public void setDispatchId(String dispatchId) {
        this.dispatchId = dispatchId;
    }

    public String getSchedulerExpr() {
        return schedulerExpr;
    }

    public void setSchedulerExpr(String schedulerExpr) {
        this.schedulerExpr = schedulerExpr;
    }

    public String getRouteStrategy() {
        return routeStrategy;
    }

    public void setRouteStrategy(String routeStrategy) {
        this.routeStrategy = routeStrategy;
    }

    public String getBlockStrategy() {
        return blockStrategy;
    }

    public void setBlockStrategy(String blockStrategy) {
        this.blockStrategy = blockStrategy;
    }

    public int getExecutorTimeout() {
        return executorTimeout;
    }

    public void setExecutorTimeout(int executorTimeout) {
        this.executorTimeout = executorTimeout;
    }

    public int getExecutorFailRetryCount() {
        return executorFailRetryCount;
    }

    public void setExecutorFailRetryCount(int executorFailRetryCount) {
        this.executorFailRetryCount = executorFailRetryCount;
    }

    public String getOrginRunSchema() {
        return orginRunSchema;
    }

    public void setOrginRunSchema(String orginRunSchema) {
        this.orginRunSchema = orginRunSchema;
    }


    public String getJobMode() {
        return jobMode;
    }

    public void setJobMode(String jobMode) {
        this.jobMode = jobMode;
    }

    public String getFieldMappingJson() {
        return fieldMappingJson;
    }

    public void setFieldMappingJson(String fieldMappingJson) {
        this.fieldMappingJson = fieldMappingJson;
    }
}
