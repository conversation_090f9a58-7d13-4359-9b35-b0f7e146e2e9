/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/1
 */
package com.xmcares.platform.hookbox.integrator;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.platform.admin.common.database.DatabaseMetaQueryFactory;
import com.xmcares.platform.admin.common.datasource.jdbc.JdbcDataSourceManager;
import com.xmcares.platform.admin.common.datasource.mq.MqDataSourceManager;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
@Configuration
@ComponentScan(basePackages = {"com.xmcares.platform.hookbox.integrator"})
public class HookboxIntegratorConfiguration {


    @Bean(name = "jdbcDataSourceManager")
    @ConditionalOnMissingBean
    public JdbcDataSourceManager jdbcDataSourceManager(FSTemplate fsTemplate) {
        return new JdbcDataSourceManager(fsTemplate);
    }

    @Bean(name = "mqDataSourceManager")
    @ConditionalOnMissingBean
    public MqDataSourceManager mqDataSourceManager() {
        return new MqDataSourceManager();
    }

    @Bean
    @ConditionalOnMissingBean
    public DatabaseMetaQueryFactory databaseMetaQueryFactory(JdbcDataSourceManager jdbcDataSourceManager) {
        return new DatabaseMetaQueryFactory(jdbcDataSourceManager);
    }


    /**
     * MybatiPlusConfig
     *
     * <AUTHOR>
     * @Descriptions MybatiPlusConfig
     * @Date 2025/5/29 11:22
     */
    @MapperScan("com.xmcares.platform.hookbox.integrator.mapper")
    @Configuration
    //@EnableTransactionManagement
    public static class MybatisPlusConfig {

        @Bean
        public MybatisPlusInterceptor mybatisPlusInterceptor() {
            MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
            // 不同数据库使用的不一样
            interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
            return interceptor;
        }

        @Bean
        public ConfigurationCustomizer configurationCustomizer() {
            return configuration -> configuration.setSafeResultHandlerEnabled(false);
        }

    }

    /**
     * xxl-job config
     *
     * <AUTHOR> 2017-04-28
     */
    @Configuration
    public static class XxlJobConfig {
        private final Logger logger = LoggerFactory.getLogger(XxlJobConfig.class);

        @Value("${xxl.job.admin.addresses}")
        private String adminAddresses;

        @Value("${xxl.job.accessToken}")
        private String accessToken;

        @Value("${xxl.job.executor.appname}")
        private String appname;

        @Value("${xxl.job.executor.address}")
        private String address;

        @Value("${xxl.job.executor.ip}")
        private String ip;

        @Value("${xxl.job.executor.port}")
        private int port;

        @Value("${xxl.job.executor.logpath}")
        private String logPath;

        @Value("${xxl.job.executor.logretentiondays}")
        private int logRetentionDays;

    //    @Value("${xxl.job.executor.registry.nacos}")
    //    private Boolean enableNacos;


        @Bean
        public XxlJobSpringExecutor xxlJobExecutor() {
            logger.info(">>>>>>>>>>> xxl-job config init.");
            XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
            xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
            xxlJobSpringExecutor.setAppname(appname);
            xxlJobSpringExecutor.setAddress(address);
            //xxlJobSpringExecutor.setNacosFlag(enableNacos);
            // 判断是否启用nacos
    //        if (!enableNacos) {
            xxlJobSpringExecutor.setIp(ip);
            xxlJobSpringExecutor.setPort(port);
    //        }
            xxlJobSpringExecutor.setAccessToken(accessToken);
            xxlJobSpringExecutor.setLogPath(logPath);
            xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);

            return xxlJobSpringExecutor;
        }

        /**
         * 针对多网卡、容器内部署等情况，可借助 "spring-cloud-commons" 提供的 "InetUtils" 组件灵活定制注册IP；
         *
         *      1、引入依赖：
         *          <dependency>
         *             <groupId>org.springframework.cloud</groupId>
         *             <artifactId>spring-cloud-commons</artifactId>
         *             <version>${version}</version>
         *         </dependency>
         *
         *      2、配置文件，或者容器启动变量
         *          spring.cloud.inetutils.preferred-networks: 'xxx.xxx.xxx.'
         *
         *      3、获取IP
         *          String ip_ = inetUtils.findFirstNonLoopbackHostInfo().getIpAddress();
         */


    }
}
