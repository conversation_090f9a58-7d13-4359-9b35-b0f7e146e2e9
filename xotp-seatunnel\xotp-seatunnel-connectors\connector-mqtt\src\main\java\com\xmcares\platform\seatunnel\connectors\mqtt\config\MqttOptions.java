/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.config;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;

import java.util.List;

public class MqttOptions {

    // 基础连接配置
    public static final Option<List<String>> BROKER_URLS =
            Options.key("broker.urls")
                    .listType()
                    .noDefaultValue()
                    .withDescription("MQTT broker地址列表，支持failover，格式：tcp://host:port");

    public static final Option<String> CLIENT_ID =
            Options.key("client.id")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("MQTT客户端ID，为空时自动生成唯一ID，并行的情况下，会导致clientID重复，请不要配置");

    public static final Option<String> USERNAME =
            Options.key("username").stringType().noDefaultValue().withDescription("MQTT连接用户名");

    public static final Option<String> PASSWORD =
            Options.key("password").stringType().noDefaultValue().withDescription("MQTT连接密码");

    public static final Option<Integer> CONNECTION_TIMEOUT =
            Options.key("connection.timeout")
                    .intType()
                    .defaultValue(30)
                    .withDescription("连接超时时间（秒）");

    public static final Option<Integer> KEEP_ALIVE_INTERVAL =
            Options.key("keep.alive.interval")
                    .intType()
                    .defaultValue(60)
                    .withDescription("心跳间隔时间（秒）");

    public static final Option<Boolean> CLEAN_SESSION =
            Options.key("clean.session")
                    .booleanType()
                    .defaultValue(true)
                    .withDescription("是否清理会话状态");

    public static final Option<Boolean> SSL_ENABLED =
            Options.key("ssl.enabled")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("是否启用SSL/TLS连接");

    // Source特有配置
    public static final Option<List<String>> TOPICS =
            Options.key("topics")
                    .listType()
                    .noDefaultValue()
                    .withDescription("订阅的topic列表，支持MQTT通配符(+, #)");

    public static final Option<Integer> QOS =
            Options.key("qos")
                    .intType()
                    .defaultValue(1)
                    .withDescription("服务质量等级：0-最多一次，1-至少一次，2-仅一次");

    public static final Option<String> MESSAGE_FORMAT =
            Options.key("message.format")
                    .stringType()
                    .defaultValue("json")
                    .withDescription("消息格式：json（默认）, text/plain（纯文本）, xml（XML格式）");

    public static final Option<Integer> POLL_TIMEOUT =
            Options.key("poll.timeout")
                    .intType()
                    .defaultValue(1000)
                    .withDescription("消息轮询超时时间（毫秒）");

    public static final Option<Long> BATCH_INACTIVITY_TIMEOUT =
            Options.key("batch.inactivity.timeout")
                    .longType()
                    .defaultValue(5000L)
                    .withDescription(
                            "批处理模式下，如果超过这个时间（毫秒）没有收到新消息，则任务结束。设置为-1则永不超时。");

    // Sink特有配置
    public static final Option<String> TOPIC =
            Options.key("topic").stringType().noDefaultValue().withDescription("发布消息的目标topic");

    public static final Option<Boolean> RETAINED =
            Options.key("retained").booleanType().defaultValue(false).withDescription("是否保留消息");

    public static final Option<String> TOPIC_PATTERN =
            Options.key("topic.pattern")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("动态topic模式，支持字段占位符，如：sensor/${device_id}/data");

    public static final Option<Integer> MAX_INFLIGHT_MESSAGES =
            Options.key("max.inflight.messages")
                    .intType()
                    .defaultValue(10)
                    .withDescription("最大未确认消息数量");

    // 重连配置
    public static final Option<Boolean> AUTO_RECONNECT =
            Options.key("auto.reconnect")
                    .booleanType()
                    .defaultValue(true)
                    .withDescription("是否自动重连");

    public static final Option<Integer> MAX_RECONNECT_DELAY =
            Options.key("max.reconnect.delay")
                    .intType()
                    .defaultValue(60000)
                    .withDescription("最大重连延迟时间（毫秒）");

    public static final Option<Boolean> INCLUDE_METADATA =
            Options.key("include.metadata")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("是否在输出消息中包含SeaTunnel元数据字段");

    // 在现有的 MqttOptions 中添加以下配置项
    public static final Option<Integer> BATCH_SIZE = Options.key("batch_size")
            .intType()
            .defaultValue(100)
            .withDescription("Batch size for message publishing");

    public static final Option<Long> BATCH_TIMEOUT_MS = Options.key("batch_timeout_ms")
            .longType()
            .defaultValue(1000L)
            .withDescription("Batch timeout in milliseconds");

    public static final Option<Integer> QUEUE_CAPACITY = Options.key("queue_capacity")
            .intType()
            .defaultValue(10000)
            .withDescription("Message queue capacity");

    public static final Option<Integer> MAX_RETRY_ATTEMPTS = Options.key("max_retry_attempts")
            .intType()
            .defaultValue(3)
            .withDescription("Maximum retry attempts for failed messages");

    public static final Option<Long> RETRY_DELAY_MS = Options.key("retry_delay_ms")
            .longType()
            .defaultValue(1000L)
            .withDescription("Delay between retry attempts in milliseconds");

    public static final Option<Boolean> ENABLE_METRICS = Options.key("enable_metrics")
            .booleanType()
            .defaultValue(false)
            .withDescription("Enable metrics collection");
}

