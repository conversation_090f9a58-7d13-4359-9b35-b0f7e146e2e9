import React, { useState, useEffect, useRef } from 'react';
import { Input, Spin, Card, Button, Space, Typography, Divider, Tooltip, Badge, Select, message } from 'antd';
import { logDetailCat } from "@/services/Scheduler/Log/Log";
import moment from 'moment';
import PageLoading from '@/components/PageLoading';
import { SyncOutlined, DownloadOutlined, CopyOutlined, FullscreenOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;

export default ({ location }) => {
  const [scheduleCode, setScheduleCode] = useState(''); // 调度日志
  const [runCode, setRunCode] = useState(''); // 运行日志
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);
  const [refreshInterval, setRefreshInterval] = useState(10); // 默认10秒，单位秒

  const scheduleScrollRef = useRef(null); // 左侧滚动区域
  const runScrollRef = useRef(null); // 右侧滚动区域
  const timerRef = useRef(null); // 用于保存定时器引用

  let scheduleFromLineNum = 1;

  // 加载调度日志
  const handleScheduleLog = async () => {
    try {
      setRefreshing(true);
      const { query } = location;
      const res = await logDetailCat({
        ...query,
        triggerTime: moment(query.triggerTime).valueOf(),
        fromLineNum: scheduleFromLineNum,
      });

      if (res) {
        const { seaTunnelLog, xxlJobDetailLog } = res;
        setRunCode(seaTunnelLog || '暂无运行日志');
        setScheduleCode(xxlJobDetailLog || '暂无调度日志');
        setLastUpdateTime(moment().format('YYYY-MM-DD HH:mm:ss'));
      } else {
        if (autoRefresh) {
          clearInterval(timerRef.current);
          setAutoRefresh(false);
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      setRefreshing(false);
      setLoading(false);
    }
  };

  // 手动刷新日志
  const handleRefresh = async () => {
    await handleScheduleLog();
  };

  // 复制日志到剪贴板
  const copyToClipboard = (text, type) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success(`${type}日志已复制到剪贴板`);
    }).catch(err => {
      console.error('复制失败:', err);
      message.error('复制失败，请手动选择并复制');
    });
  };

  // 下载日志
  const downloadLog = (content, filename) => {
    const element = document.createElement('a');
    const file = new Blob([content], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = filename;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  // 设置自动刷新定时器
  const setupRefreshTimer = () => {
    clearInterval(timerRef.current);
    if (autoRefresh) {
      timerRef.current = setInterval(async () => {
        await handleScheduleLog();
      }, refreshInterval * 1000);
    }
  };

  // 切换自动刷新
  const toggleAutoRefresh = () => {
    const newState = !autoRefresh;
    setAutoRefresh(newState);
    if (!newState) {
      clearInterval(timerRef.current);
    } else {
      setupRefreshTimer();
    }
  };

  // 更改刷新间隔
  const handleIntervalChange = (value) => {
    setRefreshInterval(value);
    if (autoRefresh) {
      setupRefreshTimer();
    }
  };

  const loadMoreData = async () => {
    if (loading) return;
    await handleScheduleLog();
  };

  useEffect(() => {
    loadMoreData();
    setupRefreshTimer();

    return () => {
      clearInterval(timerRef.current);
    };
  }, []);

  return (
    loading ? (
      <PageLoading />
    ) : (
      <div style={{ padding: '16px', height: '100vh', display: 'flex', flexDirection: 'column' }}>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0 }}>任务日志详情</Title>
          <Space>
            {lastUpdateTime && (
              <Text type="secondary">
                最后更新: {lastUpdateTime}
              </Text>
            )}
            <>
              <Tooltip title="设置刷新间隔">
                <Select
                  value={refreshInterval}
                  onChange={handleIntervalChange}
                  disabled={!autoRefresh}
                  style={{ width: 100 }}
                >
                  <Option value={5}>5 秒</Option>
                  <Option value={10}>10 秒</Option>
                  <Option value={30}>30 秒</Option>
                  <Option value={60}>1 分钟</Option>
                  <Option value={300}>5 分钟</Option>
                </Select>
              </Tooltip>
              <Tooltip title={autoRefresh ? "关闭自动刷新" : "开启自动刷新"}>
                <Button
                  type={autoRefresh ? "primary" : "default"}
                  icon={<SyncOutlined spin={refreshing} />}
                  onClick={toggleAutoRefresh}
                >
                  {autoRefresh ? "自动刷新中" : "自动刷新"}
                </Button>
              </Tooltip>
            </>
            <Button icon={<SyncOutlined />} onClick={handleRefresh} loading={refreshing}>
              刷新
            </Button>
          </Space>
        </div>

        <div style={{ display: 'flex', flex: 1, gap: '16px' }}>
          {/* 左侧调度日志区域 */}
          <Card
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Badge status="processing" text="调度日志" />
                <Space>
                  <Tooltip title="复制日志">
                    <Button
                      type="text"
                      icon={<CopyOutlined />}
                      onClick={() => copyToClipboard(scheduleCode, '调度')}
                    />
                  </Tooltip>
                  <Tooltip title="下载日志">
                    <Button
                      type="text"
                      icon={<DownloadOutlined />}
                      onClick={() => downloadLog(scheduleCode, `调度日志_${moment().format('YYYYMMDD_HHmmss')}.txt`)}
                    />
                  </Tooltip>
                </Space>
              </div>
            }
            style={{ flex: 1, height: '100%', display: 'flex', flexDirection: 'column' }}
            bodyStyle={{ flex: 1, padding: 0, overflow: 'auto' }}
          >
            <Input.TextArea
              style={{
                height: '100%',
                border: 0,
                borderRadius: 0,
                resize: 'none',
                fontFamily: 'monospace',
                padding: '12px',
                color: 'rgba(0, 0, 0, 0.85)',
                backgroundColor: '#fafafa',
              }}
              value={scheduleCode}
              disabled
              ref={scheduleScrollRef}
              id={'scheduleDiv'}
            />
          </Card>

          {/* 右侧运行日志区域 */}
          <Card
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Badge status="success" text="运行日志" />
                <Space>
                  <Tooltip title="复制日志">
                    <Button
                      type="text"
                      icon={<CopyOutlined />}
                      onClick={() => copyToClipboard(runCode, '运行')}
                    />
                  </Tooltip>
                  <Tooltip title="下载日志">
                    <Button
                      type="text"
                      icon={<DownloadOutlined />}
                      onClick={() => downloadLog(runCode, `运行日志_${moment().format('YYYYMMDD_HHmmss')}.txt`)}
                    />
                  </Tooltip>
                </Space>
              </div>
            }
            style={{ flex: 1, height: '100%', display: 'flex', flexDirection: 'column' }}
            bodyStyle={{ flex: 1, padding: 0, overflow: 'auto' }}
          >
            <Input.TextArea
              style={{
                height: '100%',
                border: 0,
                borderRadius: 0,
                resize: 'none',
                fontFamily: 'monospace',
                padding: '12px',
                color: 'rgba(0, 0, 0, 0.85)',
                backgroundColor: '#fafafa',
              }}
              value={runCode}
              disabled
              ref={runScrollRef}
              id={'runDiv'}
            />
          </Card>
        </div>
      </div>
    )
  );
};
