/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：4/24/23
 */
package com.xmcares.platform.admin.common.expression.function;

import com.xmcares.platform.admin.common.expression.PseudoFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统日期时间获取的函数，第三参数默认为天
 * 格式：$FMT_DT{scheduleTime, yyyy-MM-dd hh:mm:ss, 1d}
 * 实例
 * <AUTHOR>
 * @since 1.4.1
 */
public class FmtDateTimeFunction implements PseudoFunction {
    private static final Logger logger = LoggerFactory.getLogger(FmtDateTimeFunction.class);
    public final static String DEFAULT_FUNC_NAME = "FMT_DT";

    public final static String DEFAULT_PARAM_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public final static String DEFAULT_PARAM_AMOUNT = "0d";

    private String name = DEFAULT_FUNC_NAME;



    @Override
    public String execute(Map<String, Object> environment, String ... args) {
        if (args.length == 0) {
            throw new IllegalArgumentException(String.format("函数[ %s ]参数缺少", this.name));
        }
        String varArg = args[0];
        LocalDateTime varTime = getDateTime(environment, varArg);
        String format = args.length > 1 ? args[1] : DEFAULT_PARAM_FORMAT;
        String diff = args.length > 2 ? args[2] : DEFAULT_PARAM_AMOUNT;
        return this.executeInternal(varTime, format, diff);
    }

    /**
     * 获取时间
     * @param dateTime 日期时间
     * @param format 时间格式
     * @param diff 时间间隔
     * @return 时间字符串
     */
    protected String executeInternal(LocalDateTime dateTime, String format, String diff) {
        String regex = "^-?\\d+[yMdhms]?$";
        if (!diff.matches(regex)) {
            throw new IllegalArgumentException(String.format("函数[ %s ]参数[ %s ]不符合标准格式", this.name, diff));
        }

        char field = diff.charAt(diff.length() - 1);
        String ss = diff.replaceFirst("[yMdhms]", "");
        int amount = Integer.parseInt(ss);

        switch (field) {
            case 'y' : dateTime = dateTime.plusYears(amount); break; // 年
            case 'M' : dateTime = dateTime.plusMonths(amount); break;// 月
            case 'h' : dateTime = dateTime.plusHours(amount); break;// 小时
            case 'm' : dateTime = dateTime.plusMinutes(amount); break; // 分钟
            case 's' : dateTime = dateTime.plusSeconds(amount); break; // 秒
            default  : dateTime = dateTime.plusDays(amount); // 天
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return dateTime.format(formatter);
    }


    /**
     * 解析参数
     * @param environment 运行上下文变量
     * @param varArg 参数
     * @return LocalDateTime
     */
    protected LocalDateTime getDateTime(Map<String, Object> environment, String varArg) {
        Object varValue = environment.get(varArg);
        if (varValue == null) {
            varValue = varArg;
            logger.info("函数[ {} ]参数[ {} ]在运行上下文变量中不存在或为空值，使用变量名即值", this.name, varArg);
        }
        LocalDateTime result;
        if (varValue instanceof LocalDateTime) {
            result = (LocalDateTime) varValue;
        } else if (varValue instanceof LocalDate) {
            result = ((LocalDate) varValue).atStartOfDay(); // 设置时间为 00:00:00
        } else if (varValue instanceof Date) {
            result = ((Date) varValue).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        } else if (varValue instanceof Calendar) {
            result = ((Calendar) varValue).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        } else if (varValue instanceof Long) {
            result = Instant.ofEpochMilli((Long) varValue).atZone(ZoneId.systemDefault()).toLocalDateTime();
        } else if (varValue instanceof String) {
            try {
                // 尝试使用默认格式解析
                LocalDate date = LocalDate.parse((String) varValue, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                result = date.atStartOfDay(); // 设置时间为 00:00:00
            } catch (Exception e) {
                try {
                    result = LocalDateTime.parse((String) varValue, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } catch (Exception e1) {
                    throw new IllegalArgumentException("无法将字符串 [" + varValue + "] 解析为 LocalDateTime", e1);
                }
            }
        } else {
            throw new IllegalArgumentException("不支持的日期时间类型: " + varValue.getClass());
        }
        return result;
    }




    public void setName(String name) {
        this.name = name;
    }
    @Override
    public String getName() {
        return this.name;
    }


    public static void main(String[] args) {
        Map<String, Object> environment = new HashMap<>();
        environment.put("dateObj", LocalDateTime.of(2025, 4, 5, 10, 0));
        environment.put("dateStr", "2025-04-05");
        environment.put("timestamp", System.currentTimeMillis());
        FmtDateTimeFunction function = new FmtDateTimeFunction();
        System.out.println(function.execute(environment, "dateObj", "yyyy-MM-dd HH:mm:00", "-1h"));; // 减去一小时
        System.out.println(function.execute(environment, "dateStr", "yyyy-MM-dd", "1d"));;  // 将字符串解析为 LocalDateTime 并加一天

        System.out.println(function.execute(environment, "timestamp", "yyyy-MM-dd HH:00", "2h"));; // 时间戳转为 LocalDateTime 加两小时
    }
}
