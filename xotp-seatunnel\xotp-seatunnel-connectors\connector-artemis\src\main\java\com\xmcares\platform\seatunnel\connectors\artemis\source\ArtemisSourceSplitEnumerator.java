/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/24
 */
package com.xmcares.platform.seatunnel.connectors.artemis.source;

import org.apache.seatunnel.api.source.Boundedness;
import org.apache.seatunnel.api.source.SourceSplitEnumerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * Artemis Source Split Enumerator: 负责发现并分配 splits 给 readers。 对于 Artemis MQ，splits 主要用于并行度管理。 它会生成与
 * Job 并行度数量相等的 splits。
 *
 * <AUTHOR>
 * @since 2025/7/24
 */
public class ArtemisSourceSplitEnumerator
        implements SourceSplitEnumerator<ArtemisSourceSplit, ArtemisSourceState> {

    private static final Logger log = LoggerFactory.getLogger(ArtemisSourceSplitEnumerator.class);

    private final Boundedness boundedness;

    private final Context<ArtemisSourceSplit> context;
    // 维护所有已生成的但尚未分配的 splits 队列
    private final Queue<ArtemisSourceSplit> unassignedSplitsPool;
    // 维护已注册但尚未获得 split 的 reader subtask 索引队列
    private final Queue<Integer> unassignedReaders;
    // 跟踪已分配的 split: subtaskIndex -> split
    private final Map<Integer, ArtemisSourceSplit> assignedSplits;

    /** 恢复构造函数：从检查点状态恢复 Enumerator。 */
    public ArtemisSourceSplitEnumerator(
            Context<ArtemisSourceSplit> context,
            ArtemisSourceState restoredState,
            Boundedness boundedness) {
        // 调用主构造函数进行基本初始化，并传递 jobContext
        this(context, boundedness);
        log.info(
                "ArtemisSourceSplitEnumerator restored. Restored state: {}. Job boundedness: {}",
                restoredState,
                boundedness);
    }

    /** 主构造函数：初始化 Enumerator。 */
    public ArtemisSourceSplitEnumerator(
            Context<ArtemisSourceSplit> context, Boundedness boundedness) {
        this.context = context;
        this.boundedness = boundedness;
        // 根据 jobContext 判断是否是有界源
        this.unassignedSplitsPool = new ConcurrentLinkedQueue<>();
        this.unassignedReaders = new ConcurrentLinkedQueue<>();
        this.assignedSplits = new HashMap<>();
        log.info("ArtemisSourceSplitEnumerator initialized. boundedness: {}", boundedness);
    }

    @Override
    public void open() {
        // 没有特定的资源需要在此处打开。
        log.info("ArtemisSourceSplitEnumerator opened.");
    }

    /** 该方法仅由引擎执行一次。 在这里生成所有初始 splits 并尝试进行初步分配。 */
    @Override
    public void run() throws Exception {
        log.info("ArtemisSourceSplitEnumerator running.");
        // 获取 Job 的总并行度
        int parallelism = context.currentParallelism();
        log.info("Job parallelism is: {}", parallelism);

        // 生成所有逻辑 splits 并放入未分配池
        for (int i = 0; i < parallelism; i++) {
            ArtemisSourceSplit split = new ArtemisSourceSplit("artemis-split-" + i);
            unassignedSplitsPool.offer(split); // 将生成的 split 加入未分配队列
        }
        log.info("ArtemisSourceSplitEnumerator generated {} splits.", generatedSplits().size());

        // 尝试将 splits 分配给所有已注册的 readers
        // 注意：在 run() 调用时，可能还没有 reader 注册，
        // 实际的分配会在 registerReader() 或 handleSplitRequest() 中发生。
        tryAssignSplitsToAvailableReaders();

        log.info("ArtemisSourceSplitEnumerator finished initial run.");
    }

    /** 尝试将 splits 分配给当前可用的 readers。 */
    private void tryAssignSplitsToAvailableReaders() {
        // 迭代已注册但尚未分配 split 的 reader
        while (!unassignedReaders.isEmpty() && !unassignedSplitsPool.isEmpty()) {
            Integer subtaskIndex = unassignedReaders.poll(); // 取出一个未分配的 reader
            ArtemisSourceSplit split = unassignedSplitsPool.poll(); // 取出一个未分配的 split

            if (subtaskIndex != null && split != null) {
                // 将 split 分配给 reader
                context.assignSplit(subtaskIndex, Collections.singletonList(split));
                assignedSplits.put(subtaskIndex, split); // 更新已分配的映射
                log.info("Assigned split {} to reader subtask {}", split.splitId(), subtaskIndex);
            } else {
                // 如果出现意外情况 (例如 unassignedReaders 或 unassignedSplitsPool 在检查后变空)，
                // 将取出的元素重新放回队列，并退出循环。
                if (subtaskIndex != null) unassignedReaders.offer(subtaskIndex);
                if (split != null) unassignedSplitsPool.offer(split);
                break;
            }
        }

        // 检查是否所有 splits 都已分配完毕，并且是 BOUNDED 模式
        if (boundedness.equals(Boundedness.BOUNDED)) {
            if (unassignedSplitsPool.isEmpty()
                    && assignedSplits.size() == context.currentParallelism()) {
                // 如果所有 splits 都已生成并尝试分配，且未分配池为空，则通知所有 readers 不再有更多 splits
                Set<Integer> registeredReaders = context.registeredReaders();
                log.info("Signaling no more splits to readers: {}", registeredReaders);
                for (Integer readerIndex : registeredReaders) {
                    context.signalNoMoreSplits(readerIndex);
                }
            }
        }
    }

    @Override
    public void addSplitsBack(List<ArtemisSourceSplit> splits, int subtaskIndex) {
        log.info(
                "ArtemisSourceSplitEnumerator received splits back from subtask {}: {}",
                subtaskIndex,
                splits);
        // 当 reader 发生故障时，它持有的 splits 会被返回给 Enumerator。
        // 对于 MQ，通常不重新分配 *相同的* split 对象，而是认为 reader 会重新启动并注册。
        // 因此，我们只需标记该 subtask 为未分配，以便在它重新注册时可以分配一个新的逻辑 split。
        assignedSplits.remove(subtaskIndex); // 移除该 subtask 的旧分配记录
        unassignedReaders.offer(subtaskIndex); // 将该 subtask 重新加入未分配队列
        log.info(
                "Subtask {} marked for re-assignment. Current unassigned readers: {}",
                subtaskIndex,
                unassignedReaders.size());
        // 尝试重新分配 splits
        tryAssignSplitsToAvailableReaders();
    }

    @Override
    public int currentUnassignedSplitSize() {
        // 返回当前未分配的 splits 数量
        return unassignedSplitsPool.size();
    }

    @Override
    public void handleSplitRequest(int subtaskIndex) {
        log.info(
                "ArtemisSourceSplitEnumerator received split request from subtask: {}",
                subtaskIndex);
        // 当一个 reader 明确请求更多 splits 时调用此方法。
        // 对于 MQ，通常每个 reader 只需要一个“无限”的逻辑 split，所以这个方法可能不常用。
        // 如果 reader 请求 split 且尚未分配，则尝试分配。
        if (!assignedSplits.containsKey(subtaskIndex)) {
            unassignedReaders.offer(subtaskIndex); // 将请求的 reader 加入未分配队列
            tryAssignSplitsToAvailableReaders(); // 尝试分配
        }
    }

    @Override
    public void registerReader(int subtaskIndex) {
        log.info("ArtemisSourceSplitEnumerator registered reader subtask: {}", subtaskIndex);
        // 当 reader 注册时，如果它还没有被分配 split，则将其添加到未分配队列。
        if (!assignedSplits.containsKey(subtaskIndex)) {
            unassignedReaders.offer(subtaskIndex);
            // 尝试立即分配 splits 给这个新注册的 reader，以及其他待分配的 reader
            tryAssignSplitsToAvailableReaders();
        }
    }

    @Override
    public ArtemisSourceState snapshotState(long checkpointId) throws Exception {
        log.info(
                "ArtemisSourceSplitEnumerator snapshotting state for checkpoint {}. Returning empty state.",
                checkpointId);
        // 对于这个简单的 MQ，Enumerator 通常不需要存储复杂的状态，
        // 因为 readers 自己处理消息确认/偏移量。
        // 如果你有动态主题发现或更复杂的分配逻辑，你可能会在这里保存这些状态。
        return ArtemisSourceState.empty(); // 返回一个空状态
    }

    @Override
    public void notifyCheckpointComplete(long checkpointId) throws IOException {
        log.info("ArtemisSourceSplitEnumerator checkpoint {} complete.", checkpointId);
    }

    @Override
    public void close() throws IOException {
        // 对于这个简单的 Enumerator，没有特定的资源需要在此处关闭。
        log.info("ArtemisSourceSplitEnumerator closed.");
    }

    // 辅助方法，用于调试或内部检查已生成的 splits 数量
    private List<ArtemisSourceSplit> generatedSplits() {
        List<ArtemisSourceSplit> allSplits = new ArrayList<>();
        allSplits.addAll(unassignedSplitsPool);
        allSplits.addAll(assignedSplits.values());
        return allSplits;
    }
}
