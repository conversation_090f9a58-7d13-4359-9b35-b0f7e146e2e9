/**
 * @Copyright 厦门民航凯亚有限公司
 * @File 数据服务模型管理
 * <AUTHOR>
 * @Date 2022-09-06
 */
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import router from 'umi/router';

import {
  DeleteOutlined,
  SyncOutlined,
  PlusCircleOutlined,
  DownOutlined,
  CopyOutlined,
  EditOutlined,
  ConsoleSqlOutlined,
  ControlOutlined,
} from '@ant-design/icons';
import {
  Card,
  Tag,
  Modal,
  Menu,
  Form,
  Input,
  Dropdown,
  Space,
  Button,
  Tooltip,
  notification,
  Table,
} from 'antd';
import PageHeaderWrapper from '@/components/PageHeaderWrapper/index';
import CommonTable from '@/components/CommonTable/index';
import SharingApiExample from '@/components/SharingApiExample/index';

import {
  pageDataset,
  saveDataset,
  deleteDataset,
  copyDataset,
  publishDataset,
  getPublishedDatasets,
  createPublishedDataset,
  updatePublishedDataset,
  deletePublishedDataset,
} from '@/services/DataService/Dataset';

import DatasetPublish from './components/DatasetPublish';

import EditModal from './components/EditModal';
import DataSource from './components/DataSource';
import styles from './DataService.less';

const confirm = Modal.confirm;

@connect(({ dataSourceTable }) => ({
  dataSourceList: dataSourceTable.dataSourceList,
}))
export default class DataService extends PureComponent {
  state = {
    selectedModel: undefined,
    publishDataset: undefined,
    selectedRows: [],
    dataSourceList: [],
    expandedRowKeys: [],
    expandedDatasource: [],
    search: {
      datasetName: '',
      datasourceId: '',
    },
    loading: false,
    pagination: {
      pageSize: 10,
      current: 1,
      total: 0,
      data: [],
    },
  };

  componentDidMount() {
    this.queryDataSourceList();
    this.queryList();
  }

  //查询数据源的下拉列表
  queryDataSourceList() {
    const { dispatch, dataSourceList } = this.props;
    if (dataSourceList && dataSourceList.length > 0) {
      this.setState({
        dataSourceList: dataSourceList.filter(item => item.category === 'JDBC'),
      });
    } else {
      dispatch({
        type: `dataSourceTable/queryDataSourceList`,
        callback: ret => {
          this.setState({
            dataSourceList: ret.filter(item => item.category === 'JDBC'),
          });
        },
      });
    }
  }

  /**
   * 获取数据
   */
  queryList = () => {
    const {
      search,
      pagination: { pageSize, current },
    } = this.state;
    let data = {
      pageNo: current,
      pageSize: pageSize,
    };
    if (!!search.datasetName) {
      data.datasetName = search.datasetName;
    }
    if (!!search.datasourceId) {
      data.datasourceId = search.datasourceId;
    }
    this.setState(
      {
        loading: true,
        selectedRows: [],
        expandedRowKeys: [],
        expandedDatasource: [],
        pagination: {
          ...this.state.pagination,
          data: [],
        },
      },
      () => {
        pageDataset(data)
          .then(ret => {
            if (ret && ret.data) {
              this.setState({
                loading: false,
                pagination: {
                  ...this.state.pagination,
                  pageSize: ret.data.pageSize,
                  current: ret.data.pageNo,
                  total: ret.data.total,
                  data: ret.data.data,
                },
              });
            } else {
              this.setState({
                loading: false,
              });
            }
          })
          .catch(err => {
            this.setState({
              loading: false,
            });
          });
      }
    );
  };

  /**
   * CommonTable 分页 排序 触发事件
   * @param pagination 分页
   * @param filtersArg
   * @param sorter 排序
   */
  handleCommonTableChange = (pagination, filtersArg, sorter) => {
    this.setState(
      {
        pagination: {
          ...this.state.pagination,
          ...pagination,
        },
      },
      () => {
        this.queryList();
      }
    );
  };
  /**
   * 选中一条记录
   * @param rows
   */
  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };
  /**
   * 查询
   */
  handleSearch = e => {
    if (e) e.preventDefault();

    this.setState(
      {
        selectedRows: [],
        pagination: {
          ...this.state.pagination,
          pageSize: 10,
          current: 1,
        },
      },
      () => {
        this.queryList();
      }
    );
  };
  handleDataSource = value => {
    const { search } = this.state;
    this.setState({
      search: {
        ...search,
        datasourceId: value,
      },
    });
  };
  handleModelsName = event => {
    const { search } = this.state;
    this.setState({
      search: {
        ...search,
        datasetName: event.target.value,
      },
    });
  };

  handleAdd = () => {
    this.setState({
      selectedModel: {
        datasetCode: '',
        datasetName: '',
        datasourceId: '',
      },
    });
  };
  handleDeletes = () => {
    const { selectedRows, loading } = this.state;
    if (selectedRows && selectedRows.length > 0) {
      confirm({
        title: '操作提示',
        content: '您确认删除选择的服务?',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          if (loading == true) {
            notification.error({
              message: '删除中，请稍等！',
            });
            return;
          }
          this.setState({
            loading: true,
          });
          const datasetIds = [];
          selectedRows.forEach(row => {
            if (row.datasetPublished !== '1') {
              datasetIds.push(row.datasetId);
            }
          });
          if (datasetIds.length > 0) {
            deleteDataset({ datasetIds })
              .then(ret => {
                if (ret && ret.success == true) {
                  notification.success({
                    message: '删除成功！',
                  });
                  this.queryList();
                } else {
                  this.setState({
                    loading: false,
                  });
                  notification.error({
                    message: ret.message || '删除失败！',
                  });
                }
              })
              .catch(err => {
                this.setState({
                  loading: false,
                });
                notification.error({
                  message: err.message || '删除失败！',
                });
              });
          }
        },
      });
    } else {
      //提示 请选择要删除的行
      notification.error({
        message: '请选择要删除的服务！',
      });
    }
  };
  handleDelete = row => {
    const datasetIds = [row.datasetId];
    if (row.datasetPublished == '1') {
      notification.error({
        message: '已发布的服务不能直接删除！',
      });
      return;
    }

    confirm({
      title: '操作提示',
      content: '您确认删除"' + row.datasetName + '"的服务?',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteDataset({ datasetIds })
          .then(ret => {
            if (ret && ret.success == true) {
              notification.success({
                message: '删除成功！',
              });
              this.queryList();
            } else {
              notification.error({
                message: ret.message || '删除失败！',
              });
            }
          })
          .catch(err => {
            notification.error({
              message: err.message || '删除失败！',
            });
          });
      },
    });
  };
  handleEdit = row => {
    this.setState({
      selectedModel: {
        ...row,
      },
    });
  };
  handleCopy = row => {
    this.setState({
      selectedModel: {
        ...row,
        datasetCode: '',
      },
    });
  };
  handlePublish = row => {
    // const mes = row.datasetPublished == '1' ? '停止服务' : '发布服务';
    // if (row.datasetPublished == '0') {
    //   //判断是否已经设计sql
    //   if (!row.datasetSql) {
    //     notification.error({
    //       message: '未定义的模型无法发布！',
    //     });
    //     return;
    //   }
    // }

    // confirm({
    //   title: '操作提示',
    //   content: '您确认' + mes + '?',
    //   okText: '确认',
    //   cancelText: '取消',
    //   onOk: async () => {
    //     const isPublish = row.datasetPublished == '1' ? 0 : 1;
    //     publishDataset({
    //       datasetId: row.datasetId,
    //       isPublish,
    //     })
    //       .then(ret => {
    //         if (ret.success != true) {
    //           notification.error({
    //             message: ret.message || mes + '失败！',
    //           });
    //         } else {
    //           notification.success({
    //             message: mes + '成功！',
    //           });
    //           this.queryList();
    //         }
    //       })
    //       .catch(err => {
    //         notification.error({
    //           message: err.message || mes + '失败！',
    //         });
    //       });
    //   },
    // });

    this.setState({
      publishDataset: row,
    });
  };
  handlePublishClose = () => {
    this.setState({
      publishDataset: undefined,
    });
  };
  handlePublishSubmit = model => {
    const { publishDataset } = this.state;
    createPublishedDataset({ ...publishDataset, ...model })
      .then(ret => {
        if (ret && ret.success == true) {
          notification.success({
            message: '发布成功！',
          });
          this.openExpanded(true, publishDataset);
          this.setState({
            publishDataset: undefined,
          });
        } else {
          notification.error({
            message: ret?.message || '发布失败！',
          });
        }
      })
      .catch(err => {
        notification.error({
          message: err.message || '发布失败！',
        });
      });
  };

  handleUpDownDataset = dataset => {
    const { expandedRowKeys } = this.state;
    const mes = dataset.datasetPublished == '1' ? '下线服务' : '上线服务';
    confirm({
      title: '操作提示',
      content: '您确认' + mes + '?',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const isPublish = dataset.datasetPublished == '1' ? 0 : 1;
        updatePublishedDataset(dataset.datasetCode, isPublish)
          .then(ret => {
            if (ret.success != true) {
              notification.error({
                message: ret.message || mes + '失败！',
              });
            } else {
              notification.success({
                message: mes + '成功！',
              });
              this.openExpanded(true, '');
            }
          })
          .catch(err => {
            notification.error({
              message: err.message || mes + '失败！',
            });
          });
      },
    });
  };

  handleDeleteDataset = dataset => {
    confirm({
      title: '操作提示',
      content: '您确认删除"' + dataset.datasetName + '"的服务?',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deletePublishedDataset(dataset.id)
          .then(ret => {
            if (ret && ret.success == true) {
              notification.success({
                message: '删除成功！',
              });
              this.openExpanded(false, '');
            } else {
              notification.error({
                message: ret.message || '删除失败！',
              });
            }
          })
          .catch(err => {
            notification.error({
              message: err.message || '删除失败！',
            });
          });
      },
    });
  };

  handleClose = () => {
    this.setState({
      selectedModel: undefined,
    });
  };
  handleSubmit = model => {
    if (model.isCopy) {
      delete model.isCopy;
      copyDataset(model)
        .then(ret => {
          if (ret && ret.success == true && ret.data && ret.data.datasetId) {
            notification.success({
              message: '保存成功！',
            });
            this.setState({
              selectedModel: undefined,
            });
            this.queryList();
            this.openDesign(ret.data?.datasetId);
          } else {
            notification.error({
              message: ret.message || '保存失败！',
            });
          }
        })
        .catch(err => {
          notification.error({
            message: err.message || '保存失败！',
          });
        });
    } else {
      delete model.isCopy;
      if (!model.datasetId) {
        //有属性没值 新增
        delete model.datasetId;
      }
      saveDataset(model)
        .then(ret => {
          if (ret && ret.success == true && ret.data && ret.data.datasetId) {
            notification.success({
              message: '保存成功！',
            });
            this.setState({
              selectedModel: undefined,
            });
            this.queryList();

            if (!model.datasetId) {
              this.openDesign(ret.data?.datasetId);
            }
          } else {
            notification.error({
              message: ret.message || '保存失败！',
            });
          }
        })
        .catch(err => {
          notification.error({
            message: err.message || '保存失败！',
          });
        });
    }
  };
  openDesign = datasetId => {
    if (datasetId) {
      confirm({
        title: '操作提示',
        content: '您确认进入设计界面?',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          router.push({
            pathname: '/dataservice/modeldesign',
            query: {
              id: datasetId,
            },
          });
        },
      });
    }
  };

  openExpanded = (expanded, record) => {
    const { expandedRowKeys, expandedDatasource } = this.state;
    let datasetId = record?.datasetId;
    if (!record) {
      datasetId = expandedRowKeys[0];
    }
    if (expanded && datasetId) {
      this.setState({
        expandedRowKeys: [datasetId],
        expandedDatasource: [],
      });
      getPublishedDatasets(datasetId)
        .then(ret => {
          console.log(ret);
          this.setState({
            expandedDatasource: ret?.data || [],
          });
        })
        .catch(err => {
          notification.error({
            message: err.message || '获取失败！',
          });
        });
    } else {
      this.setState({
        expandedRowKeys: [],
      });
    }
  };

  renderForm() {
    const {
      search: { datasetName, datasourceId },
      dataSourceList,
    } = this.state;

    return (
      <Form layout="inline" style={{ marginBottom: 16 }}>
        <Form.Item label="数据集名称" name="datasetName">
          <Input value={datasetName} onChange={this.handleModelsName} />
        </Form.Item>
        <Form.Item label="数据源" name="dataSource">
          <DataSource value={datasourceId} opts={dataSourceList} onChange={this.handleDataSource} />
        </Form.Item>
        <Form.Item>
          <Button type="primary" onClick={this.handleSearch}>
            查询
          </Button>
        </Form.Item>
      </Form>
    );
  }

  renderExpandedRow = record => {
    const { expandedDatasource } = this.state;
    const columns = [
      {
        title: '服务名称',
        dataIndex: 'datasetName',
        width: '280px',
      },
      {
        title: '服务API',
        dataIndex: 'serviceName',
        width: '280px',
      },
      {
        title: '服务地址',
        dataIndex: 'serviceApi',
        render: (text, row) => {
          let url = text;
          if (!!row.datasetParameter) {
            try {
              const params = JSON.parse(row.datasetParameter);
              params &&
                params.forEach(param => {
                  if (url.indexOf('?') > -1) {
                    url += '&';
                  } else {
                    url += '?';
                  }
                  url += param.label + '=' + param.value;
                });
            } catch (e) {}
          }

          return (
            <Tooltip title={url}>
              <div>{url}</div>
            </Tooltip>
          );
        },
      },
      {
        title: '耗时',
        dataIndex: 'timeConsuming',
        width: '80px',
        render: text => {
          if (text) {
            return <div>{text}ms</div>;
          } else {
            return '-';
          }
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: '150px',
        render: text => {
          return <div>{text}</div>;
        },
      },
      {
        title: '操作',
        dataIndex: 'datasetId',
        width: '200px',
        render: (text, row) => {
          const operation = (
            <Space>
              {row.datasetPublished == '1' ? (
                <Button
                  size="small"
                  type="primary"
                  danger
                  onClick={() => this.handleUpDownDataset(row)}
                >
                  下线服务
                </Button>
              ) : (
                <Button size="small" type="primary" onClick={() => this.handleUpDownDataset(row)}>
                  上线服务
                </Button>
              )}
              <Button size="small" type="warning" onClick={() => this.handleDeleteDataset(row)}>
                删除服务
              </Button>
            </Space>
          );

          return (
            <Tooltip>
              <div>{operation}</div>
            </Tooltip>
          );
        },
      },
    ];

    return (
      <Table
        loading={false}
        pagination={false}
        key={record.id}
        columns={columns}
        dataSource={expandedDatasource}
      />
    );
  };

  render() {
    const columns = [
      {
        title: '数据集名称',
        dataIndex: 'datasetName',
        width: '20%',
      },
      {
        title: '数据源',
        dataIndex: 'datasourceName',
        width: '20%',
        render: (text, row) => {
          return (
            <Tooltip title={text}>
              <div>
                <Tag>{row.datasourceType}</Tag> {text}
              </div>
            </Tooltip>
          );
        },
      },
      {
        title: '平均耗时',
        dataIndex: 'timeConsuming',
        width: '10%',
        render: text => {
          if (text) {
            return <div>{text}ms</div>;
          } else {
            return '-';
          }
        },
      },
      {
        title: '模型',
        dataIndex: 'datasetSql',
        width: '80px',
        render: (text, row) => {
          if (text) {
            if (row.datasetMode == '0') {
              return (
                <Tooltip title="设计器模型">
                  <div>
                    <ControlOutlined />
                  </div>
                </Tooltip>
              );
            } else {
              return (
                <Tooltip title="SQL模型">
                  <div>
                    <ConsoleSqlOutlined />
                  </div>
                </Tooltip>
              );
            }
          } else {
            return '-';
          }
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: '150px',
        render: text => {
          return <div>{text}</div>;
        },
      },
      {
        title: '操作',
        dataIndex: 'datasetId',
        width: '10%',
        render: (text, row) => {
          const isPublished = row.datasetPublished == '1';
          const menu = (
            <Menu>
              <Space direction="vertical">
                <Button disabled={isPublished} type="text" onClick={() => this.handleEdit(row)}>
                  <EditOutlined />
                  <span>编辑</span>
                </Button>
                <Button type="text" onClick={() => this.handleCopy(row)}>
                  <CopyOutlined />
                  <span>复制</span>
                </Button>
                <Button disabled={isPublished} type="text" onClick={() => this.handleDelete(row)}>
                  <DeleteOutlined />
                  <span>删除</span>
                </Button>
              </Space>
            </Menu>
          );

          const operation = (
            <Space>
              {row.datasetPublished == '1' ? (
                <Button size="small" type="primary" danger onClick={() => this.handlePublish(row)}>
                  停止服务
                </Button>
              ) : (
                <Button size="small" type="primary" onClick={() => this.handlePublish(row)}>
                  发布服务
                </Button>
              )}
              <Button
                disabled={isPublished}
                size="small"
                onClick={() => {
                  this.openDesign(text);
                }}
              >
                设计服务
              </Button>
              <Dropdown overlay={menu} trigger={['click']}>
                <Button size="small">
                  <Space>
                    更多
                    <DownOutlined />
                  </Space>
                </Button>
              </Dropdown>
            </Space>
          );

          return (
            <Tooltip>
              <div>{operation}</div>
            </Tooltip>
          );
        },
      },
    ];
    const {
      selectedRows,
      selectedModel,
      publishDataset,
      dataSourceList,
      pagination,
      loading,
      expandedRowKeys,
    } = this.state;

    return (
      <PageHeaderWrapper>
        <Card bordered={false}>
          <div className={styles.dataService}>
            {this.renderForm()}
            <Space style={{ marginBottom: 16 }}>
              <Button type="primary" onClick={this.handleAdd}>
                <PlusCircleOutlined style={{ fontSize: 16 }} />
                <span>新增</span>
              </Button>

              <Button onClick={this.queryList} icon={<SyncOutlined style={{ fontSize: 16 }} />} />

              <SharingApiExample />
            </Space>
            <CommonTable
              expandable={true}
              expandedRowKeys={expandedRowKeys}
              onExpand={this.openExpanded}
              expandedRowRender={this.renderExpandedRow}
              rowKey={'datasetId'}
              loading={loading}
              data={pagination}
              columns={columns}
              current={pagination.current}
              rowSelectionShow={false}
              selectedRows={selectedRows}
              onSelect={(selectedRowKeys, selectedRows) => {
                this.setState({
                  selectedRowKeys,
                  selectedRows,
                });
              }}
              onChange={this.handleCommonTableChange}
            />
          </div>
        </Card>
        {selectedModel ? (
          <EditModal
            model={selectedModel}
            dataSourceList={dataSourceList}
            onCancel={this.handleClose}
            onOk={this.handleSubmit}
          />
        ) : null}
        {publishDataset ? (
          <DatasetPublish
            model={publishDataset}
            onCancel={this.handlePublishClose}
            onOk={this.handlePublishSubmit}
          />
        ) : null}
      </PageHeaderWrapper>
    );
  }
}
