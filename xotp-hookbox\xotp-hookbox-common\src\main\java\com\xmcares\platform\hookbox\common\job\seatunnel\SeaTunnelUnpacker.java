/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/17
 */
package com.xmcares.platform.hookbox.common.job.seatunnel;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 *
 * TODO
 * <AUTHOR>
 * @since 2.1.0
 */
public class SeaTunnelUnpacker {

    private static final Logger logger = LoggerFactory.getLogger(SeaTunnelUnpacker.class);

    public static void unpackIfNeeded(String zipPath, String targetDir) throws IOException {
        Path targetDirPath = Paths.get(targetDir).toAbsolutePath().normalize();
        if (!targetDirPath.toFile().exists()) {
            logger.info("[SeaTunnel] 已存在目录，跳过解压: {}", targetDir);
            return;
        }
        logger.info("[SeaTunnel] 解压 seatunnel.zip 到 {}", targetDir);
        Path zipFilePath = Paths.get(zipPath);

        try (ZipInputStream zis = new ZipInputStream(Files.newInputStream(zipFilePath))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                Path newPath = targetDirPath.resolve(entry.getName()).normalize();
                // 防止 Zip Slip 攻击
                if (!newPath.startsWith(targetDirPath)) {
                    throw new IOException("Entry is outside of the target dir: " + entry.getName());
                }
                if (entry.isDirectory()) {
                    Files.createDirectories(newPath);
                } else {
                    // 创建父目录
                    Files.createDirectories(newPath.getParent());
                    // 写入文件
                    try (OutputStream fos = Files.newOutputStream(newPath)) {
                        byte[] buffer = new byte[4096];
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
            }
        }
    }
}
