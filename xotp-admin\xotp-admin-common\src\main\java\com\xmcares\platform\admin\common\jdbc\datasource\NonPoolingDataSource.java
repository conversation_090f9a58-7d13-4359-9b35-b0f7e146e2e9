/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：5/16/23
 */
package com.xmcares.platform.admin.common.jdbc.datasource;

import com.xmcares.platform.admin.common.jdbc.AbstractDriverDataSource;

import java.sql.Connection;
import java.sql.Driver;
import java.sql.SQLException;
import java.util.Properties;
import java.util.logging.Level;

/**
 * 没有连接池的数据源
 * <AUTHOR>
 * @since 1.4.1
 */
public class NonPoolingDataSource extends AbstractDriverDataSource {

    private Driver driver;

    public NonPoolingDataSource() {
    }

    /**
     * Create a new DriverManagerDataSource with the given standard Driver parameters.
     * @param driver the JDBC Driver object
     * @param url the JDBC URL to use for accessing the DriverManager
     * @see Driver#connect(String, Properties)
     */
    public NonPoolingDataSource(Driver driver, String url) {
        setDriver(driver);
        setUrl(url);
    }

    /**
     * Create a new DriverManagerDataSource with the given standard Driver parameters.
     * @param driver the JDBC Driver object
     * @param url the JDBC URL to use for accessing the DriverManager
     * @param username the JDBC username to use for accessing the DriverManager
     * @param password the JDBC password to use for accessing the DriverManager
     * @see Driver#connect(String, Properties)
     */
    public NonPoolingDataSource(Driver driver, String url, String username, String password) {
        this(driver, url);
        setUsername(username);
        setPassword(password);
    }

    /**
     * Create a new DriverManagerDataSource with the given standard Driver parameters.
     * @param driver the JDBC Driver object
     * @param url the JDBC URL to use for accessing the DriverManager
     * @param conProps the JDBC connection properties
     * @see Driver#connect(String, Properties)
     */
    public NonPoolingDataSource(Driver driver, String url, Properties conProps) {
        this(driver, url);
        setConnectionProperties(conProps);
    }


    /**
     * Specify the JDBC Driver implementation class to use.
     * <p>An instance of this Driver class will be created and held
     * within the SimpleDriverDataSource.
     * @see #setDriver
     */
    public void setDriverClass(Class<? extends Driver> driverClass) {
        try {
            this.driver = driverClass.newInstance();
        } catch (InstantiationException |IllegalAccessException e) {
            throw new IllegalArgumentException("创建驱动失败", e);
        }
    }

    /**
     * Specify the JDBC Driver instance to use.
     * <p>This allows for passing in a shared, possibly pre-configured
     * Driver instance.
     * @see #setDriverClass
     */
    public void setDriver(Driver driver) {
        this.driver = driver;
    }

    /**
     * Return the JDBC Driver instance to use.
     */
    public Driver getDriver() {
        return this.driver;
    }


    @Override
    protected Connection getConnectionFromDriver(Properties props) throws SQLException {
        Driver driver = getDriver();
        String url = getUrl();
        if (driver == null) {
            throw new IllegalArgumentException("Driver must not be null");
        }
        if (logger.isLoggable(Level.FINE)) {
            logger.fine("Creating new JDBC Driver Connection to [" + url + "]");
        }
        return driver.connect(url, props);
    }
}

