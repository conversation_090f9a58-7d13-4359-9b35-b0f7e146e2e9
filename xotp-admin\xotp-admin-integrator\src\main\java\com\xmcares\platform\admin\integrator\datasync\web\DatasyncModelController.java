package com.xmcares.platform.admin.integrator.datasync.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.validation.Insert;
import com.xmcares.platform.admin.common.validation.Update;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncModel;
import com.xmcares.platform.admin.metadata.database.model.DatasourceModel;
import com.xmcares.platform.admin.metadata.database.service.DatasourceModelService;
import com.xmcares.platform.admin.integrator.datasync.service.DatasyncModelService;
import com.xmcares.platform.admin.metadata.database.vo.SupportDatasourceModelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @Date 2022/03/29 09:48:18
 */
@Api(value = "集成任务模型访问控制器")
@Validated
@RestController
@RequestMapping("/datasync-model")
public class DatasyncModelController {

    @Autowired
    private DatasyncModelService datasyncModelService;
    @Autowired
    private DatasourceModelService datasourceModelService;

    @GetMapping("/supportDatasourceModel")
    @ApiOperation("获取新增时至此的数据源模型")
    public List<SupportDatasourceModelVo> findSupportDatasourceModel() {
        List<DatasourceModel> datasyncModels = datasourceModelService.listBySimple();
        if (CollectionUtils.isEmpty(datasyncModels)) {
            throw new BusinessException("请先配置数据源模型！");
        }
        return datasyncModelService.findSupportDatasourceModel(
                datasyncModels.stream().map(model -> new SupportDatasourceModelVo(model.getId(), model.getType(), "")).collect(Collectors.toList()));
    }

    @GetMapping("/page-query")
    @ApiOperation("集成任务模型分页列表")
    @ResponseBody
    public Page page(DatasyncModel datasyncModel, Integer page, Integer rows) {
        Pagination pagination = new Pagination();
        pagination.setPageNo(page != null ? page : 1);
        pagination.setPageSize(rows != null ? rows : 10);
        return datasyncModelService.page(datasyncModel, pagination);
    }

    @RequestMapping("/list-query")
    @ApiOperation("集成任务模型列表")
    @ResponseBody
    public List list(@RequestBody DatasyncModel datasyncModel) {
        return datasyncModelService.list(datasyncModel);
    }

    @GetMapping("/get")
    @ApiOperation("集成任务模型详情")
    @ResponseBody
    public DatasyncModel get(@NotBlank(message = "id不能为空") String id) {
        return datasyncModelService.get(id);
    }

    /**
     * 根据数据源，获取集成模型信息
     *
     * @param datasourceId 数据源id
     * @param type         数据源类型:0.读类型;1.写类型
     * @return
     */
    @GetMapping("/get/by/datasource")
    @ApiOperation("根据数据源，获取集成模型信息")
    @ResponseBody
    public DatasyncModel get(@NotBlank(message = "datasourceId不能为空") String datasourceId, String type) {
        return datasyncModelService.get(datasourceId, type);
    }

    @PostMapping("/add")
    @ApiOperation("新增集成模型信息")
    public Boolean add(@RequestBody @Validated({Insert.class}) DatasyncModel datasyncModel) {
        if ("0".equals(datasyncModel.getIntegrationWay())) {
            if (StringUtils.isEmpty(datasyncModel.getDatasourceModelId())) {
                throw new BusinessException("数据源模型ID不能为空");
            }
        }
        datasyncModelService.add(datasyncModel);
        return true;
    }

    @PostMapping("/update")
    @ApiOperation("修改集成模型信息")
    public Boolean update(@RequestBody @Validated({Update.class}) DatasyncModel datasyncModel) {
//        if (datasyncModelService.assertDatasyncModelUsing(datasyncModel.getId())) {
//            throw new BusinessException("已经被使用的模型不允许修改");
//        }
        datasyncModelService.update(datasyncModel);
        return true;
    }

    @GetMapping("/delete")
    @ApiOperation("删除集成模型信息")
    public Boolean delete(@NotBlank(message = "id不能为空") String id) {
        if (!datasyncModelService.assertDatasyncModelUsing(id)) {
            datasyncModelService.delete(id);
            return true;
        } else {
            return false;
        }

    }

    @PostMapping("/batch-delete")
    public Boolean deleteBatch(@RequestBody @NotEmpty(message = "id不能为空") List<String> ids) {
        datasyncModelService.deleteBatch(ids);
        return true;
    }

    @ApiOperation("上传插件")
    @ResponseBody
    @PostMapping(value = "/plugin-upload", produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean pluginUpload(@NotBlank(message = "id不能为空") @RequestParam(name = "id") String id,
                                @NotNull(message = "file不能为空") @RequestParam("file") MultipartFile file) throws Exception {
        datasyncModelService.uploadPlugin(id, file);
        return true;
    }

}
