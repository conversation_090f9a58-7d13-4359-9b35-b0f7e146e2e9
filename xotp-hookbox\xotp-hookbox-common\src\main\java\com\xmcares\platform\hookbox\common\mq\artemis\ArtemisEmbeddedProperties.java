/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/10
 */
package com.xmcares.platform.hookbox.common.mq.artemis;

import org.apache.activemq.artemis.core.config.impl.ConfigurationImpl;
import org.springframework.boot.context.properties.ConfigurationProperties;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2.1.0
 */
@ConfigurationProperties(prefix = "xbdp.hookbox.mq.server.artemis")
public class ArtemisEmbeddedProperties {

    /**
     * 是否启用持久化（默认 false）。
     * 若为 true，会将消息存储在磁盘上。
     */
    private boolean persistenceEnabled = true;

    /**
     * 是否启用安全认证机制（默认 false）。
     */
    private boolean securityEnabled = false;

    /**
     * Broker 监听端口，默认为 61616。
     */
    private int port = 61616;

    /**
     * 日志存储目录（journal）。
     */
    private String journalDir = "data/mq/journal";

    /**
     * 绑定信息目录（bindings）。
     */
    private String bindingsDir = "data/mq/bindings";

    /**
     * 分页存储目录（paging）。
     */
    private String pagingDir = "data/mq/paging";

    /**
     * 大消息存储目录（large-messages）。
     */
    private String largeMessagesDir = "data/mq/largemessages";

    /**
     * Acceptor 的额外参数，如 protocol、host、port 等。
     * 例如：artemis.broker.acceptor-params.protocols=CORE
     */
    private Map<String, String> acceptorParams = new HashMap<>();


    @PostConstruct
    public void resolvePaths() {
        String appHome = System.getProperty("app.home");
        if (appHome != null && !appHome.isEmpty()) {
            if (!journalDir.startsWith("/")) {
                journalDir = appHome + "/" + journalDir;
            }
            if (!bindingsDir.startsWith("/")) {
                bindingsDir = appHome + "/" + bindingsDir;
            }
            if (!pagingDir.startsWith("/")) {
                pagingDir = appHome + "/" + pagingDir;
            }
            if (!largeMessagesDir.startsWith("/")) {
                largeMessagesDir = appHome + "/" + largeMessagesDir;
            }
        }
    }

    // Getters & Setters
    public boolean isPersistenceEnabled() {
        return persistenceEnabled;
    }

    public void setPersistenceEnabled(boolean persistenceEnabled) {
        this.persistenceEnabled = persistenceEnabled;
    }

    public boolean isSecurityEnabled() {
        return securityEnabled;
    }

    public void setSecurityEnabled(boolean securityEnabled) {
        this.securityEnabled = securityEnabled;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getJournalDir() {
        return journalDir;
    }

    public void setJournalDir(String journalDir) {
        this.journalDir = journalDir;
    }

    public String getBindingsDir() {
        return bindingsDir;
    }

    public void setBindingsDir(String bindingsDir) {
        this.bindingsDir = bindingsDir;
    }

    public String getPagingDir() {
        return pagingDir;
    }

    public void setPagingDir(String pagingDir) {
        this.pagingDir = pagingDir;
    }

    public String getLargeMessagesDir() {
        return largeMessagesDir;
    }

    public void setLargeMessagesDir(String largeMessagesDir) {
        this.largeMessagesDir = largeMessagesDir;
    }

    public Map<String, String> getAcceptorParams() {
        return acceptorParams;
    }

    public void setAcceptorParams(Map<String, String> acceptorParams) {
        this.acceptorParams = acceptorParams;
    }
}
