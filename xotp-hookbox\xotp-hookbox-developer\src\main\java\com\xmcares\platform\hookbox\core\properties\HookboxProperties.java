package com.xmcares.platform.hookbox.core.properties;

import com.xmcares.framework.commons.error.BusinessException;
import com.xmcares.platform.hookbox.core.error.HookboxErrorCode;
import com.xmcares.platform.hookbox.core.error.SystemException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2022/7/12 15:21
 **/
@Component
public class HookboxProperties  implements InitializingBean {
    Logger logger = LoggerFactory.getLogger(this.getClass());
    /**  */
    @Value("${xbdp.fileLocal.root}")
    private String localRoot;
    /** datax程序的可执行文件位置 */
    @Value("${xbdp.hooks.datax.base-path}")
    private String dataxBinRoot;
    /** datax python解释器路径 */
    @Value("${xbdp.hooks.datax.python.path:}")
    private String dataxPythonPath;
    /** datax插件的初始化路径 */
    @Value("${xbdp.hooks.datax.plugin.run.path}")
    private String dataxPluginInstallPath;
    /** 文件服务器的根路径 */
    @Value("${xbdp.fileServer.root}")
    private String fileServerRoot;
    /** 文件服务器的目录分隔符 */
    @Value("${xbdp.fileServer.separator}")
    private String fileServerSeparator;
    /** flink程序的可执行文件位置 */
    @Value("${xbdp.hooks.flink.base-path}")
    private String flinkBinRoot;
    /** flink的URL地址 */
    @Value("${xbdp.hooks.flink.master.url}")
    private String flinkUrl;

    @Value("${xbdp.hooks.flink.ssl.enable}")
    private Boolean enableSSL;

    @Value("${xbdp.hooks.flink.ssl.keystore.path}")
    private String keystorePath;

    @Value("${xbdp.hooks.flink.ssl.keystore.password}")
    private String keystorePassword;

    @Value("${xbdp.hooks.flink.ssl.keystore.type}")
    private String keyStoreType;

    @Value("${xbdp.hooks.flink.ssl.encryption}")
    private String sslEncryption;

    @Override
    public void afterPropertiesSet() throws Exception {
        if (StringUtils.isEmpty(localRoot)) {
            throw new BusinessException(HookboxErrorCode.HOOK_LOCAL_FILE_UNDEFINE);
        }
        logger.info("begin create InstallPath");
        checkAndAutoMkdir(new File(localRoot));
        checkAndAutoMkdir(new File(getDevResourceInstallPath()));
        checkAndAutoMkdir(new File(getDatasourceResourceInstallPath()));
    }

    private void checkAndAutoMkdir(File dir) {
        if (!dir.exists()) {
            if (!dir.mkdirs()) {
                throw new SystemException(HookboxErrorCode.HOOK_LOCAL_FILE_CRETE_ERROR);
            }
            if (!dir.exists()) {
                throw new SystemException(HookboxErrorCode.HOOK_LOCAL_FILE_CRETE_ERROR);
            }
            logger.info("create dir ok -{}-", dir.getAbsolutePath());
        } else {
            if (!dir.isDirectory()) {
                throw new BusinessException(HookboxErrorCode.HOOK_LOCAL_FILE_NOT_DIR);
            }
            logger.info("checkAndAutoMkdir -{}- exists", dir.getName());
        }
    }


    public String getLocalRoot() {
        return localRoot;
    }

    public String getDataxBinRoot() {
        return dataxBinRoot;
    }

    public String getDataxPythonPath() {
        return dataxPythonPath;
    }

    public String getDataxPluginInstallPath() {
        return dataxPluginInstallPath;
    }

    public String getDevResourceInstallPath() {
        return localRoot + File.separator + "DEV_RESOURCE";
    }

    public String getDatasourceResourceInstallPath() {
        return localRoot + "/" + "DATABASE_RESOURCE";
    }

    public String getFileServerRoot() {
        return fileServerRoot;
    }

    public String getFileServerSeparator() {
        return fileServerSeparator;
    }

    public String getFlinkBinRoot() {
        return flinkBinRoot;
    }

    public String getFlinkUrl() {
        return flinkUrl;
    }

    public Boolean getEnableSSL() {
        return enableSSL;
    }

    public void setEnableSSL(Boolean enableSSL) {
        this.enableSSL = enableSSL;
    }

    public String getKeystorePath() {
        return keystorePath;
    }

    public void setKeystorePath(String keystorePath) {
        this.keystorePath = keystorePath;
    }

    public String getKeystorePassword() {
        return keystorePassword;
    }

    public void setKeystorePassword(String keystorePassword) {
        this.keystorePassword = keystorePassword;
    }

    public String getKeyStoreType() {
        return keyStoreType;
    }

    public void setKeyStoreType(String keyStoreType) {
        this.keyStoreType = keyStoreType;
    }

    public String getSslEncryption() {
        return sslEncryption;
    }

    public void setSslEncryption(String sslEncryption) {
        this.sslEncryption = sslEncryption;
    }
}
