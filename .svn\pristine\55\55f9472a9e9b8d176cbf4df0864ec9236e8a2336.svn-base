/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/3/2
 */
package com.xmcares.platform.admin.integrator.datasync.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmcares.framework.commons.context.UserContext;
import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.platform.admin.common.datasource.mq.MqType;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.common.vo.ReturnT;
import com.xmcares.platform.admin.integrator.IntegratorProperties;
import com.xmcares.platform.admin.integrator.common.error.UnknownException;
import com.xmcares.platform.admin.integrator.common.util.ConstantUtils;
import com.xmcares.platform.admin.integrator.datasync.dto.DatasyncDto;
import com.xmcares.platform.admin.integrator.datasync.dto.JobLogPageListDto;
import com.xmcares.platform.admin.integrator.datasync.dto.XxlJobLogWithInstanceVO;
import com.xmcares.platform.admin.integrator.datasync.model.*;
import com.xmcares.platform.admin.integrator.datasync.repository.mapper.DatasyncJobMapper;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.XxljobClient;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobGroup;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobInfo;
import com.xmcares.platform.admin.integrator.datasync.vo.DataxTempVo;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceRepository;
import com.xxl.job.core.biz.model.LogResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;
import com.xmcares.platform.admin.integrator.datasync.util.JsonJobParamBuilder;

import javax.annotation.Resource;
import java.util.*;


/**
 * 基于xxl-job调度datax完成数据同步
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public class XxlJobSchedulerRepository implements SchedulerRepository {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private XxljobClient xxlJobClient;

    @Autowired
    private IntegratorProperties properties;

//    @Resource
//    private DatasyncInstanceRepository datasyncInstanceRepository;

    @Resource
    private DatasyncJobMapper datasyncJobMapper;

    @Autowired
    private DatasourceRepository datasourceRepository;

    @Override
    public Map<String, SchedulerJob> querySchedulerJobs(List<String> jobIds) {
        if (CollectionUtils.isEmpty(jobIds)) {
            return new HashMap<>();
        }
        // TODO: 这里对应的其实是bdp_intg_datasync_task下的dispatch_id，需要改造成查自己的
//        ReturnT<List<XxlJobInfo>> findResult = xxlJobClient.queryList(jobIds);
        List<DatasyncJob> datasyncInstances = datasyncJobMapper.selectList(Wrappers.<DatasyncJob>lambdaQuery()
                .in(DatasyncJob::getScheduleId, jobIds)
                .eq(DatasyncJob::getDeleted, YNEnum.NO.getIntCharCode()));

//        if (findResult.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
//            throw new BusinessException(findResult.getMsg());
//        }
//        List<XxlJobInfo> jobs = findResult.getContent();

        if(CollectionUtils.isEmpty(datasyncInstances)) {
            return new HashMap<>();
        }
        Map<String, SchedulerJob> schedulerJobMap = new HashMap<>();
        datasyncInstances.forEach(job -> {
            SchedulerJob schedulerJob = new SchedulerJob();
            schedulerJob.setId(job.getScheduleId());
            schedulerJob.setJobGroup(job.getScheduleGroup() + "");
            schedulerJob.setExpression(job.getScheduleConf());
            schedulerJob.setTriggerStatus(job.getStatus());
            schedulerJob.setExecutorTimeout(job.getExecutorTimeout());
            schedulerJob.setExecutorFailRetryCount(job.getExecutorFailRetryCount());
            schedulerJob.setSchedulerType(job.getScheduleType());
            schedulerJob.setExecutorRouteStrategy(job.getExecutorRouteStrategy());
            schedulerJob.setExecutorBlockStrategy(job.getExecutorBlockStrategy());
            schedulerJobMap.put(job.getScheduleId(), schedulerJob);
        });
        return schedulerJobMap;
    }


    @Override
    public XxlJobInfo addScheduler(DatasyncDto saveDatasync, String dataSyncTaskId, String filePath, DataxTempVo context) {
        try {
            // 1. 从XXL Job上获取任务运行的服务信息
            // 固定死默认的执行器
            ReturnT<String> jobGroupResult = xxlJobClient.loadXxlJobGroupById(2);
            // code 为 200，才是正确的
            if (!(null != jobGroupResult && jobGroupResult.getCode() == ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE)) {
                throw new BusinessException(jobGroupResult.getMsg());
            }

            XxlJobGroup xxlJobGroup = JSON.parseObject(jobGroupResult.getContent(), XxlJobGroup.class);


//            ReturnT<XxlJobGroup> jobGroupResult = xxlJobClient.findJobGroup(properties.getXxlJob().getGroup());
//            if (jobGroupResult.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
//                throw new BusinessException(jobGroupResult.getMsg());
//            }


            // 2. 使用简化的JSON格式构建参数
            JsonJobParamBuilder paramBuilder = new JsonJobParamBuilder();

            // 2.1. 设置任务ID（原DATASYNC_INSTANCE_ID）
            paramBuilder.setJobId(dataSyncTaskId);

            // 2.2. 设置文件路径
            paramBuilder.setFilePath(filePath);

            // 2.3. 设置同步类型
            Datasource orig = datasourceRepository.get(saveDatasync.getOrginDatasourceId());
            if (MqType.XIMC.equalsTypeName(orig.getType())) {
                // 消息中心到数据库
                paramBuilder.setSyncType("msg2db");
            } else {
                // 数据库到数据库
                paramBuilder.setSyncType("db2db");
            }

            // 3. 获取登陆用户信息
            UserContext userContext = UserContextHolder.getUserContext();
            // 4. 构建最终的JSON参数字符串
            String jsonParams = paramBuilder.build();
            logger.info("构建的JSON参数: {}", jsonParams);

            // 5. 构建XXL JOB 任务信息
            XxlJobInfo xxlJobInfo =
                    // 5.1. 初始化XXL JOB 任务信息（使用JSON格式参数）
                    XxlJobInfo.createNewFromByBeginHandler(saveDatasync, userContext.getUsername(),
                                    xxlJobGroup.getId(), properties.getXxlJob().getAlarmEmail(), jsonParams)
                            // 5.2. 构建增量参数信息
                            .buildIncrement(saveDatasync.buildOrginMapInfo(), saveDatasync.getFromMod(), saveDatasync.getOrginDatasourceId())
                            // 5.3. 构建JVM运行参数信息
                            .buildJvm(saveDatasync.buildOrginMapInfo());
            //设置子任务id linbk  20241014
            xxlJobInfo.setChildJobId(saveDatasync.getChildJobid());
            // 6. 将 XXL JOB 任务信息提交到 调度服务
            // 使用xxl-job默认的新增方法
            ReturnT<String> jobResult = xxlJobClient.addJobInfo(xxlJobInfo);
            if (jobResult.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
                throw new BusinessException("创建Job任务失败：" + jobResult.getMsg());
            } else {
                // 需要手动设置jobResult返回新增成功后的jobId，否则默认为空
                xxlJobInfo.setId(Integer.parseInt(jobResult.getContent()));
                return xxlJobInfo;
            }
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            logger.error("添加调度任务异常", e);
            throw new UnknownException("内部服务异常");
        }
    }

    private String parserPath(String key, Object param) {
        if (key.length() <= 4) { return null; }
        if (!"PATH".equalsIgnoreCase(key.substring(key.length() - 4))) {
            return null;
        }
        String v = param instanceof JSON ? param.toString() : String.valueOf(param);
        if (JSONObject.isValid(v)) {
            JSONObject result = JSON.parseObject(v);
            if (result.containsKey("path")) {
                return result.getString("path");
            }
        }
        return null;
    }

    @Override
    public void updateScheduler(String dispatchId, Datasync saveDatasync) {
        try {
            // 1. 获取旧的信息
            ReturnT<String> findResult = xxlJobClient.loadJobInfoById(Integer.parseInt(dispatchId));

            if (findResult.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
                throw new BusinessException(findResult.getMsg());
            }
            XxlJobInfo findResultObject = JSON.parseObject(findResult.getContent(), XxlJobInfo.class);

            // 3. 获取登陆用户信息
            UserContext userContext = UserContextHolder.getUserContext();
            // 4. 构建XXL JOB 任务信息
            XxlJobInfo xxlJobInfo =
                    // 4.1. 初始化XXL JOB 任务信息
                    XxlJobInfo.createUpdateFromByBeginHandler(dispatchId, findResultObject.getJobDesc(), saveDatasync, userContext.getUsername(),
                            findResultObject.getJobGroup(),
                            properties.getXxlJob().getAlarmEmail(), findResultObject.getExecutorParam());
            // 5. 将 XXL JOB 任务信息提交到 调度服务
            // TODO: 待修改
            ReturnT<String> jobResult = xxlJobClient.updateJob(xxlJobInfo);
            if (jobResult.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
                throw new BusinessException("创建Job任务失败：" + jobResult.getMsg());
            }

        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            logger.error("添加调度任务异常", e);
            throw new UnknownException("内部服务异常");
        }
    }

    @Override
    public void removeScheduler(String dispatchId) {
        try {
            ReturnT<String> stringReturnT = xxlJobClient.removeJob(Integer.parseInt(dispatchId));
            if (stringReturnT.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
                // 删除任务失败，抛出异常
                throw new BusinessException(stringReturnT.getMsg());
            }
        } catch (Exception e) {
            logger.error("移除调度服务任务异常，id:{}", dispatchId, e);
            throw new UnknownException("任务删除失败");
        }
    }

    @Override
    public void beginScheduler(String dispatchId) {
        try {
            ReturnT<String> result = xxlJobClient.startJob(Integer.parseInt(dispatchId));
            if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
                throw new BusinessException(result.getMsg());
            }
        } catch (Exception e) {
            logger.error("开始调度服务任务异常", e);
            throw new UnknownException("任务开始失败");
        }
    }

    @Override
    public void endScheduler(String dispatchId) {
        try {
            ReturnT<String> result = xxlJobClient.endJob(Integer.parseInt(dispatchId));
            if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
                throw new BusinessException(result.getMsg());
            }
        } catch (Exception e) {
            logger.error("停止调度服务任务异常", e);
            throw new UnknownException("任务停止失败");
        }
    }

    @Override
    public void triggerScheduler(String dispatchId) {
        // 查询本地的job状态
        // 判断状态是否为停止，挺值得才允许运行一次
        // 调用触发方法
        DatasyncJob byDispatchId = datasyncJobMapper.selectOne(new QueryWrapper<DatasyncJob>()
                .eq("schedule_id", dispatchId)
                .eq("deleted", YNEnum.NO.getIntCharCode()));
        if (ObjectUtils.isEmpty(byDispatchId)) {
            throw new BusinessException("该任务不存在");
        }
//        // 1. 获取旧的信息
//        ReturnT<XxlJobInfo> findResult = xxlJobClient.findJob(dispatchId);
//        if (findResult.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
//            throw new BusinessException(findResult.getMsg());
//        }

        if (byDispatchId.getStatus() != 0) {
            // todo: 使用seatunnel判断任务是否 running
            throw new BusinessException("任务还在运行中，只有停止的任务才允许运行一次");
        }

        xxlJobClient.triggerJob(XxlJobInfo.buildTriggerJob(byDispatchId));
    }

    @Override
    public List<String> nextTriggerTime(String scheduleType, String scheduleConf) {
        // 参数校验
        if (StringUtils.isEmpty(scheduleType) || StringUtils.isEmpty(scheduleConf)) {
            throw new BusinessException("传入参数为空！");
        }
        ReturnT<List<String>> stringReturnT = xxlJobClient.nextTriggerTime(scheduleType, scheduleConf);
        if (stringReturnT.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
            // 获取调度信息失败，抛出异常
            throw new BusinessException(stringReturnT.getMsg());
        }
        return stringReturnT.getContent();
    }

    @Override
    public Map<String, Object> jobLogPageList(JobLogPageListDto jobLogPageListDto) {
        Map<String, Object> findResult =  xxlJobClient.jobLogPageList(jobLogPageListDto.getStart(), jobLogPageListDto.getLength(), jobLogPageListDto.getJobGroup(), jobLogPageListDto.getJobId(), jobLogPageListDto.getLogStatus(), jobLogPageListDto.getFilterTime());

        return findResult;
    }

    @Override
    public List<XxlJobGroup> findAllJobGroup() {
        ReturnT<String> result = xxlJobClient.findAllJobGroup();
        if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
            throw new BusinessException(result.getMsg());
        }
        return JSON.parseArray(result.getContent(), XxlJobGroup.class);
    }

    @Override
    public List<XxlJobInfo> getJobsByGroup(int jobGroup) {
        ReturnT<List<XxlJobInfo>> result = xxlJobClient.getJobsByGroup(jobGroup);
        if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
            throw new BusinessException(result.getMsg());
        }
        return result.getContent();
    }

    @Override
    public Boolean clearLog(int jobGroup, int jobId, int type) {
        ReturnT<String> result = xxlJobClient.clearLog(jobGroup, jobId, type);
        if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
            throw new BusinessException(result.getMsg());
        }
        return true;
    }

    @Override
    public JobLogVO datasyncLog(long instanceId,int fromLine) {
        try {
            ReturnT<String> result = xxlJobClient.datasyncLog(instanceId);

            if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
                throw new BusinessException(result.getMsg());
            }
            String content = result.getContent();
            if(StringUtils.isEmpty(content)){
                throw new BusinessException("获取日志失败，内容为空");
            }
            return JSON.parseObject(content, JobLogVO.class);
        } catch (Exception e) {
            throw new BusinessException("获取作业实例日志失败", e);
        }
    }

    @Override
    public Page<XxlJobLogWithInstanceVO> pageDatasyncLog(JobLogPageListDto jobLogPageListDto, int page, int rows) {
        Map<String, Object> pageRequest = new HashMap<>();

        pageRequest.put("current", page);
        pageRequest.put("size", rows);
        Map<String, Object> queryDtoMap = new HashMap<>();
        queryDtoMap.put("jobId", jobLogPageListDto.getJobId());
        queryDtoMap.put("jobGroup", jobLogPageListDto.getJobGroup());

        queryDtoMap.put("status", jobLogPageListDto.getLogStatus());
        queryDtoMap.put("filterTime", jobLogPageListDto.getFilterTime());

        queryDtoMap.put("triggerCode", jobLogPageListDto.getTriggerCode());

        pageRequest.put("queryDTO", queryDtoMap);

        try {
            ReturnT<String> stringReturnT = xxlJobClient.pageDatasyncLog(pageRequest);
            if(stringReturnT.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE){
                throw new BusinessException("获取作业实例日志失败");
            }
            String content = stringReturnT.getContent();
            JSONObject jsonObject = JSON.parseObject(content);

            // 空指针校验
            List<XxlJobLogWithInstanceVO> resultList = Optional.ofNullable(jsonObject)
                    .map(obj -> obj.getJSONObject("records"))
                    .map(records -> {
                        Object recordsObj = records.get("records");
                        JSONArray recordsArray;

                        if (recordsObj instanceof JSONArray) {
                            // 多条记录的情况
                            recordsArray = (JSONArray) recordsObj;
                        } else if (recordsObj instanceof JSONObject) {
                            // 单条记录的情况，需要包装成数组
                            recordsArray = new JSONArray();
                            recordsArray.add(recordsObj);
                        } else {
                            // 其他情况返回空数组
                            recordsArray = new JSONArray();
                        }

                        return recordsArray;
                    })
                    .filter(array -> !array.isEmpty())
                    .map(array -> array.toJavaList(XxlJobLogWithInstanceVO.class))
                    .orElse(new ArrayList<>());
            Page<XxlJobLogWithInstanceVO> tPage = new Page<>();
            tPage.setRecords(resultList);
            tPage.setTotal(jsonObject.getLong("total"));
            tPage.setCurrent(jsonObject.getLong("current"));
            tPage.setPages(jsonObject.getLong("pages"));
            tPage.setSize(jsonObject.getLong("size"));
            tPage.setOptimizeCountSql(jsonObject.getBoolean("optimizeCountSql"));
            tPage.setSearchCount(jsonObject.getBoolean("optimizeCountSql"));
            return tPage;
        } catch (Exception e) {
            throw new BusinessException("查询日志失败", e);
        }
    }

    @Override
    public LogResult logDetailCat(long logId, int fromLineNum) {
        ReturnT<LogResult> result = xxlJobClient.logDetailCat(logId, fromLineNum);
        if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
            throw new BusinessException(result.getMsg());
        }
        return result.getContent();
    }

    @Override
    public Boolean logKill(long jobId) {
        ReturnT<String> result = xxlJobClient.logKill(jobId);
        if (result.getCode() != ConstantUtils.SCHEDULER_REQUEST_SUCCESS_CODE) {
            throw new BusinessException(result.getMsg());
        }
        return true;
    }
}

