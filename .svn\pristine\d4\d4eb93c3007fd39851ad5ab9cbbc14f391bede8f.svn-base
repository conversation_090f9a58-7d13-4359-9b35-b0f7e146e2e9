import React, {PureComponent} from 'react';
import {connect} from 'dva';
import {DeleteOutlined, EditOutlined, PlusCircleOutlined} from '@ant-design/icons';
import {Form} from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Divider,
    Input,
    Modal,
    Row,
    Select,
    Tooltip,
    Tree,
} from 'antd';
import CommonTable from '@/components/CommonTable/index';
import PageHeaderWrapper from '@/components/PageHeaderWrapper/index';
import styles from './DataSource.less';
import * as utils from '@/utils/utils';
import {messageModal} from '@/utils/messageModal';
import ModalForm from "@/pages/modules/Metadata/DataSource/components/ModalForm";
import {getTemplate} from '@/services/Metadata/DataSource/DataSource';
import {getSupportTypes, getTypes} from '@/services/Metadata/DataSourceModel/DataSourceModel'
import router from "umi/router";


let markPage = 1, pageSize = 10;
const TreeNode = Tree.TreeNode;
const confirm = Modal.confirm;
const modelsName = 'dataSource';
const FormItem = Form.Item;

const type = [
    {code: "mysql", name: "mysql"},
    {code: "oracle", name: "oracle"},
    {code: "rocketmq", name: "rocketmq"},
    {code: "kafka", name: "kafka"},
    {code: "redis", name: "redis"},
    {code: "es", name: "es"},
    {code: "hive", name: "hive"},
];

const category = [
    {code: "JDBC", name: "jdbc数据库"},
    // {code: "BIGDATA", name: "大数据存储"},
    {code: "MQ", name: "消息组件"},
    // {code: "NOSQL", name: "NOSQL"},  // todo 去除数据源类别
];

@connect(({dataSource, tenantManager, deptManager, role, userRole, menu, loading}) => ({
    dataSource,
    tenantManager,
    deptManager,
    role,
    userRole,
    menu,
    loading: loading.models.dataSource,
}))
@Form.create()

export default class DataSource extends PureComponent {
    state = {
        selectedRows: undefined,
        defaultValue: [],
        modalVisible: false,
        modalTitle: '',
        selectedRowKeys: undefined,
        allTypes: [],
    };

    queryList = () => {
        const {dispatch} = this.props;
        dispatch({
            type: `${modelsName}/fetch`,
            callback: () => {
                this.setState({
                    selectedRows: undefined,
                });
            },
        });
    };


    // 获取数据源列表
    queryDataSourceList = () => {
        const {dispatch} = this.props;
        dispatch({
            type: `${modelsName}/fetchSourceList`
        });
    }

    // 获取数据源模型类型
    queryAllTypes = () => {
        getTypes().then((res) => {
            this.setState({
                allTypes: res
            })
        });
    }

    /**
     * 用户列表(第一次渲染后调用)
     */
    componentDidMount() {
        markPage = 1;
        this.queryAllTypes();
        this.queryList();
        this.queryDataSourceList();
    }


    deleteDataSource = () => {
        const {dispatch} = this.props;
        const {selectedRows} = this.state;
        const ids = [];
        const topThis = this;
        if (!!selectedRows && selectedRows.length > 0) {
            confirm({
                title: '确定删除?',
                content: '删除该条记录',
                okText: '确定',
                cancelText: '取消',
                onOk() {
                    const data = {
                        id: selectedRows[0].id,
                    };
                    dispatch({
                        type: `${modelsName}/deleteDataSource`,
                        payload: data,
                        callback: () => {
                            topThis.setState({
                                selectedRows: [],
                            });
                            const data = {
                                topThis,
                                path: `${modelsName}/fetch`,
                            };
                            utils.search(data);
                        },
                    });
                },
            });
        } else {
            messageModal('warning', '请选择一条记录!');
        }
    };


    onChange = checkedValues => {
        this.setState({
            defaultValue: checkedValues,
        });
    };


    /**
     * 打开新增或者修改或者详情的框
     */
    openAddOrUpdateOrDetailModal = async (type) => {
        const {selectedRows, selectedRowKeys,} = this.state;
        if (type === 'add') {
            this.formRef.handleMinModalVisible(true, '新增', 0);
        } else if (type === 'update') {
            if (selectedRows && selectedRows.length > 0) {
                const res = await getTemplate({id: selectedRowKeys[0]});
                this?.formRef?.handleMinModalVisible(true, '修改', 1, res, selectedRowKeys[0]);
            } else {
                messageModal('warning', '请先选择一行');
            }
        }
    };

    /**
     * 打开表管理界面
     */
    openTableManager = () => {
        const {selectedRows, selectedRowKeys} = this.state;
        if (selectedRows && selectedRows.length > 0) {
            const selectedRow = selectedRows[0];
            if (selectedRow.category !== 'JDBC') {
                messageModal('warning', '仅对关系型数据库有效');
                return;
            }
            router.push({
                pathname: '/metadata/dataSourceTable',
                query: {
                    dataSourceId: selectedRow.id,
                },
            });
        } else {
            messageModal('warning', '请先选择一行');
        }
    }

    submitForm = (data, mark) => {
        const {dispatch} = this.props;
        const {selectedRowKeys} = this.state;
        if (mark == 0) {
            dispatch({
                type: `${modelsName}/create`,
                payload: {
                    ...data,
                },
                callback: (res) => {
                    if (res === true) {
                        this.formRef.handleMinModalVisible(false);
                        messageModal('success', res.message || '新增成功');
                        this.reloadData();
                    } else {
                        messageModal('error', res.message || '新增失败');
                    }
                }
            });
        }

        if (mark == 1) {
            dispatch({
                type: `${modelsName}/update`,
                payload: {
                    id: selectedRowKeys[0],
                    ...data,
                },
                callback: (res) => {
                    if (res === true) {
                        this.formRef.handleMinModalVisible(false);
                        messageModal('success', '编辑成功');
                        this.reloadData();
                        this.setState({
                            selectedRows: [],
                            selectedRowKeys: undefined,
                        });
                    } else {
                        messageModal('error', res.message || '编辑失败');
                    }
                }
            });
        }
    }


    renderTreeNodes = data => {
        if (!!data && data.length > 0) {
            return data.map(item => {
                if (utils.isNull(item.children)) {
                    item.children = [];
                }
                if (item.children) {
                    return (
                        <TreeNode title={item.text} key={item.id} dataRef={item}>
                            {this.renderTreeNodes(item.children)}
                        </TreeNode>
                    );
                }
                return <TreeNode {...item} />;
            });
        }
    };

    handleModalVisible = flag => {
        this.setState({
            modalVisible: flag,
        });
    };

    /**
     * CommonTable 分页 排序 触发事件
     * @param pagination 分页
     * @param filtersArg
     * @param sorter 排序
     */
    handleCommonTableChange = (pagination, filtersArg, sorter) => {
        const {form} = this.props;
        markPage = pagination.current;
        pageSize = pagination.pageSize;
        form.validateFields((err, fieldsValue) => {
            let data = {
                page: pagination.current,
                rows: pagination.pageSize,
                ...fieldsValue
            };
            const {dispatch} = this.props;
            dispatch({
                payload: data,
                type: `${modelsName}/fetch`,
                callback: () => {
                    this.setState({
                        selectedRows: undefined,
                    });
                },
            });
        });
    };

    reloadData = () => {
        const {form, dispatch} = this.props;
        form.validateFields((err, values) => {
            for (let key in values) {
                if (values[key] === '') {
                    delete values[key]
                }
            }
            if (!err) {
                markPage = 1;
                const params = {
                    page: markPage,
                    rows: pageSize,
                    ...values,
                };
                dispatch({
                    type: `${modelsName}/fetch`,
                    payload: params,
                });
            }
        });
    }
    /**
     * 查询
     */
    handleSearch = (e) => {
        if (e) e.preventDefault();
        this.reloadData();
        this.setState({
            selectedRows: undefined,
            selectedRowKeys: undefined,
        });
    };

    /**
     * 重置
     */
    handleFormReset = () => {
        const data = {
            topThis: this,
        };
        utils.reset(data);
    };


    /**
     * 查询框
     */
    renderForm() {
        const {form} = this.props;
        const {getFieldDecorator} = this.props.form;
        const {allTypes} = this.state;
        return (
            <Form onSubmit={this.handleSearch}>
                <Row gutter={{md: 4, lg: 12, xl: 24}}>
                    <Col md={5} sm={24}>
                        <FormItem label="数据源名称">
                            {getFieldDecorator('name', {
                                rules: [
                                    {max: 20, message: '最多可输入20字'},
                                ],
                            })(<Input placeholder="请输入数据源名称" onBlur={utils.valToTrim.bind(this, 'name', form)}
                                      allowClear={true}/>)}
                        </FormItem>
                    </Col>

                    <Col xxl={5} xl={8} sm={9}>
                        <FormItem label="模型名称：">
                            {getFieldDecorator('type', {
                                initialValue: ''
                            })(<Select style={{width: '100%'}} allowClear={true}>
                                {allTypes && allTypes.map(item => (
                                    <Select.Option placeholder="请选择" key={item.type}>{item.type}</Select.Option>
                                ))}
                            </Select>)}
                        </FormItem>
                    </Col>

                    <Col md={5} sm={24} style={{paddingTop: '4px'}}>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        <Button style={{marginLeft: 8}} type="primary" onClick={this.handleFormReset}>重置</Button>
                    </Col>
                </Row>
            </Form>
        );
    }

    render() {
        const columns = [
            {
                title: '数据源名称',
                dataIndex: 'name',
                width: '15%',
                render: (text) => {
                    return (
                        <Tooltip title={text}>
                            <div className={styles.resultColumnsDiv}>{text}</div>
                        </Tooltip>
                    );
                },
            },
            {
                title: '数据源模型',
                dataIndex: 'type',
                width: '15%',
                render: (text) => {
                    return (
                        <Tooltip title={text}>
                            <div className={styles.resultColumnsDiv}>{text}</div>
                        </Tooltip>
                    );
                },
            },
            {
                title: '数据源类型',
                dataIndex: 'category',
                width: '15%',
                render: (text) => {
                    let name = null;
                    for (let item of category) {
                        if (text == item.code) {
                            name = item.name;
                        }
                    }
                    return (
                        <Tooltip title={name === null ? text : name}>
                            <div className={styles.resultColumnsDiv}>{name === null ? text : name}</div>
                        </Tooltip>
                    );
                },
            },
            {
                title: '创建人',
                dataIndex: 'createUser',
                width: '15%',
                render: (text) => {
                    return (
                        <Tooltip title={text}>
                            <div className={styles.resultColumnsDiv}>{text}</div>
                        </Tooltip>
                    );
                },
            },
            {
                title: '创建时间',
                dataIndex: 'createTime',
                width: '15%',
                render: (text) => {
                    return (
                        <Tooltip title={text}>
                            <div className={styles.resultColumnsDiv}>{text}</div>
                        </Tooltip>
                    );
                },
            },
            {
                title: '更新时间',
                dataIndex: 'updateTime',
                width: '15%',
                render: (text) => {
                    return (
                        <Tooltip title={text}>
                            <div className={styles.resultColumnsDiv}>{text}</div>
                        </Tooltip>
                    );
                },
            },
            {
                title: '描述',
                dataIndex: 'remark',
                width: '15%',
                render: (text) => {
                    return (
                        <Tooltip title={text}>
                            <div className={styles.resultColumnsDiv}>{text}</div>
                        </Tooltip>
                    );
                },
            },
        ];
        const {
            selectedRows,
        } = this.state;
        const {dataSource: {data, loading, dataSourceList}, menu: {currentBtnArray}} = this.props;

        const methods = {
            submitForm: this.submitForm,
            dataSourceList,
            category,
            selectedRows,
        }

        return (
            <PageHeaderWrapper>
                <Card bordered={false}>
                    <div>
                        <div className={styles.tableListForm}>{this.renderForm()}</div>
                        <div className={styles.btnStyle}>
              <span>
                   {/*<Editor/>*/}
                  <PlusCircleOutlined style={{fontSize: 16, color: '#40a9ff'}}/>&nbsp;
                  <a onClick={() => this.openAddOrUpdateOrDetailModal('add')}>新增</a>
                <Divider type="vertical"/>
              </span>
                            <span>
                <EditOutlined style={{fontSize: 16, color: '#40a9ff'}}/>&nbsp;
                                <a onClick={() => this.openAddOrUpdateOrDetailModal('update')}>修改</a>
                <Divider type="vertical"/>
              </span>
                            <span>
                <EditOutlined style={{fontSize: 16, color: '#40a9ff'}}/>&nbsp;
                                <a onClick={() => this.openTableManager()}>管理表</a>
                <Divider type="vertical"/>
              </span>
                            <span>
                <DeleteOutlined style={{fontSize: 16, color: 'red'}}/>&nbsp;
                                <a style={{color: 'red'}} onClick={this.deleteDataSource}>删除</a>
              </span>
                        </div>
                        <CommonTable
                            selectedRows={selectedRows}
                            selectType="radio"
                            loading={loading}
                            data={data}
                            columns={columns}
                            current={markPage}
                            onSelect={(selectedRowKeys, selectedRows) => {
                                this.setState({
                                    selectedRowKeys,
                                    selectedRows,
                                });
                            }}
                            onChange={this.handleCommonTableChange}
                        />
                        <ModalForm ref={(inst) => this.formRef = inst}  {...methods} />
                    </div>
                </Card>
            </PageHeaderWrapper>
        );
    }
}
