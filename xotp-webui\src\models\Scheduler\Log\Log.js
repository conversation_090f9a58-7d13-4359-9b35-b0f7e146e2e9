import * as log from '@/services/Scheduler/Log/Log';
import * as utils from '@/utils/utils';
import {messageModal} from "@/utils/messageModal";
import * as userManage from "@/services/Authority/UserManager/userManager";

export default {
  namespace: 'log',
  state: {
    loading: false,
    clearLogLoading: false,
    data: {
      data:[],
    },
    logList:{}
  },
  effects: {
    /**
     * 待办事项查询列表
     */
    *fetch({ payload, callback }, { call, put }) {
      yield put({
        type: 'changeLoading',
        payload: true,
      });
      const value = utils.pagingInit2(payload, 1, 10, '', '');
      const response = yield call(log.query, value);
      if(response) {
        response.pageNo = value.page;
        yield put({
          type: 'tableList',
          payload: response,
        });
      }
      yield put({
        type: 'changeLoading',
        payload: false,
      });
      if (callback) callback();
    },
    *killJob({ payload, callback }, { call }) {
      const response = yield call(log.killJob, payload);
      if (response) {
        messageModal('success','终止任务成功');
      } else {
          messageModal('error','终止任务','终止任务失败');
      }
      if (callback) callback();
    },
    *clearLog({ payload, callback }, { call, put }) {
      yield put({
        type: 'changeClearLogLoading',
        payload: true,
      });
      const response = yield call(log.clearLog, payload);
      if (response) {
        messageModal('success', '日志清理完成');
      } else{
        messageModal('error', '异常信息', '日志清理异常');
      }
      yield put({
        type: 'changeClearLogLoading',
        payload: false,
      });
      if (callback) callback();
    },
    *jobGroups({ callback }, { call, put }) {
      const response = yield call(log.jobGroups);
      if (callback) callback(response);
    },
    *getJobsByGroup({ payload, callback }, { call }) {
      const response = yield call(log.getJobsByGroup, payload);
      if (response && Array.isArray(response)) {
        if (callback) callback(response);
      } else {
        messageModal('error','异常信息','获取任务信息异常');
      }
    },
  },
  reducers: {
    tableList(state, action) {
      return {
        ...state,
        data: {...action.payload,data:action?.payload?.records||[]},
      };
    },
    changeLoading(state, action) {
      return {
        ...state,
        loading: action.payload,
      };
    },
    changeClearLogLoading(state, action) {
      return {
        ...state,
        clearLogLoading: action.payload,
      };
    }
  },
};
