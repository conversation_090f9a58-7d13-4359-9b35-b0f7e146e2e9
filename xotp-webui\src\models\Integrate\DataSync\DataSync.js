import * as dataSync from '@/services/Integrate/DataSync/DataSync';
import * as utils from '@/utils/utils';
import {messageModal} from "@/utils/messageModal";

export default {
  namespace: 'dataSync',
  state: {
    loading: false,
    data: {
      data:[],
    },
    childrenDatasource: {},
    pwdUpdateSuccess:false,
  },
  effects: {
    /**
     * 待办事项查询列表
     */
    *fetch({ payload, callback }, { call, put }) {
      yield put({
        type: 'changeLoading',
        payload: true,
      });
      const value = utils.pagingInit2(payload, 1, 10, '', '');
      const response = yield call(dataSync.query, value);
      if(response) {
        response.pageNo = value.page;
        if (callback) callback(response);
        yield put({
          type: 'tableList',
          payload: response,
        });
      }
      yield put({
        type: 'changeLoading',
        payload: false,
      });
    },
    *deleteDataSource({ payload, callback }, { call }) {
      const response = yield call(dataSync.deleteDataSource, payload);
      if (response===true) {
        messageModal('success',response.message||'删除成功');
      } else if(response) {
          messageModal('error','删除失败',response.message||'删除失败');
        }
      if (callback) callback();
    },
    *create({ payload, callback }, { call, put }) {
      const response = yield call(dataSync.create, payload);
      if (callback) callback(response);
    },
    *update({ payload, callback }, { call }) {
      const response = yield call(dataSync.update, payload);
      if (callback) callback(response);
    },
    *publish({ payload, callback }, { call }) {
      dataSync.publish(payload).then((result)=> {
        if (result === true) {
          messageModal('success','发布成功');
          if (callback.success) { callback.success(); }
        } else {
          messageModal('error','发布失败');
          if (callback.fault) { callback.fault(); }
        }
      }).finally(()=> {
        if (callback.end) { callback.end(); }
      });
    },
    // 获取数据源列表
    *fetchSourceList({ payload }, { call, put  }) {
      const response = yield call(dataSync.querySourceList, payload);
      if(response){
        yield put({
          type: 'saveSourceList',
          payload: response.data,
        });
      }
    },
  },
  reducers: {
    tableList(state, action) {
      return {
        ...state,
        data: action.payload,
      };
    },
    saveSourceList(state,action){
      return {
        ...state,
        dataSourceList: action?.payload||{},
      };
    },
    changeLoading(state, action) {
      return {
        ...state,
        loading: action.payload,
      };
    },
    updatePwdSuccessState(state, action) {
      return {
        ...state,
        pwdUpdateSuccess: action.payload,
      };
    }
  },
};
