/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xmcares.platform.seatunnel.connectors.mqtt.source;

import com.xmcares.platform.seatunnel.connectors.mqtt.config.MqttSourceConfig;
import org.apache.seatunnel.api.common.JobContext;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.serialization.DeserializationSchema;
import org.apache.seatunnel.api.source.Boundedness;
import org.apache.seatunnel.api.source.SeaTunnelSource;
import org.apache.seatunnel.api.source.SourceReader;
import org.apache.seatunnel.api.source.SourceSplitEnumerator;
import org.apache.seatunnel.api.source.SupportParallelism;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.common.constants.JobMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.seatunnel.format.json.JsonDeserializationSchema;
import org.apache.seatunnel.format.text.TextDeserializationSchema;

import java.util.Collections;
import java.util.List;

public class MqttSource
        implements SeaTunnelSource<SeaTunnelRow, MqttSourceSplit, MqttSourceState>,
        SupportParallelism {

    private static final Logger log = LoggerFactory.getLogger(MqttSource.class);

    private final MqttSourceConfig config;
    private JobContext jobContext;
    private DeserializationSchema<SeaTunnelRow> deserializationSchema;


    public MqttSource(ReadonlyConfig readonlyConfig) {
        this.config = MqttSourceConfig.buildConfig(readonlyConfig);
        this.config.validate();
        this.deserializationSchema = createDeserializationSchema();
    }

    private DeserializationSchema<SeaTunnelRow> createDeserializationSchema() {
        CatalogTable catalogTable = config.getCatalogTable();
        String messageFormat = config.getMessageFormat();

        log.info("Creating deserialization schema for message format: {}", messageFormat);

        if ("json".equalsIgnoreCase(messageFormat)) {
            log.info("Using JSON deserialization schema");
            return new JsonDeserializationSchema(catalogTable, false, false);
        } else if ("text".equalsIgnoreCase(messageFormat) || "plain".equalsIgnoreCase(messageFormat)) {
            log.info("Using Text deserialization schema");
            return TextDeserializationSchema.builder()
                    .seaTunnelRowType(catalogTable.getSeaTunnelRowType())
                    // 默认使用逗号分隔符，可以通过配置自定义
                    .delimiter(",")
                    .build();
        } else if ("xml".equalsIgnoreCase(messageFormat)) {
            log.info("Using XML deserialization schema");
            return new MqttXmlDeserializationSchema(catalogTable);
        } else {
            log.warn("Unsupported message format: {}, using JSON format as default", messageFormat);
            return new JsonDeserializationSchema(catalogTable, false, false);
        }
    }

    @Override
    public Boundedness getBoundedness() {
        // 根据作业模式决定是否有界
        return JobMode.BATCH.equals(jobContext.getJobMode())
                ? Boundedness.BOUNDED
                : Boundedness.UNBOUNDED;
    }

    @Override
    public List<CatalogTable> getProducedCatalogTables() {
        return Collections.singletonList(config.getCatalogTable());
    }

    @Override
    public SourceSplitEnumerator<MqttSourceSplit, MqttSourceState> createEnumerator(
            SourceSplitEnumerator.Context<MqttSourceSplit> enumeratorContext) {
        return new MqttSourceSplitEnumerator(enumeratorContext, config, Collections.emptySet());
    }

    @Override
    public SourceSplitEnumerator<MqttSourceSplit, MqttSourceState> restoreEnumerator(
            SourceSplitEnumerator.Context<MqttSourceSplit> enumeratorContext,
            MqttSourceState checkpointState) {
        return new MqttSourceSplitEnumerator(
                enumeratorContext, config, checkpointState.getAssignedSplits());
    }

    @Override
    public SourceReader<SeaTunnelRow, MqttSourceSplit> createReader(
            SourceReader.Context readerContext) {
        return new MqttSourceReader(readerContext, config, deserializationSchema);
    }

    @Override
    public String getPluginName() {
        return "Mqtt";
    }

    @Override
    public void setJobContext(JobContext jobContext) {
        this.jobContext = jobContext;
    }
}

