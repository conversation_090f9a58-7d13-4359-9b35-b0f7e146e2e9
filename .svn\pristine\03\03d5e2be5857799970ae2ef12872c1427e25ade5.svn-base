/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2025
 * Author： lius
 * Date：2025/7/16
 */
package com.xmcares.platform.admin.integrator.datasync.repository.seatunnel;

import com.xmcares.platform.admin.integrator.common.config.XxljobClientFeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@FeignClient(name = "${xbdp.feign.scheduler-service.name:scheduler-service}",
        url = "${xbdp.feign.scheduler-service.url:}",
        configuration = XxljobClientFeignConfiguration.class
)
public interface seatunnelClient {


    @GetMapping(value = "/log")
    List<String> getLogList();

    @GetMapping(value = "/log/{logFileName}")
    String getLogFileContent(@PathVariable("logFileName") String logFileName);
}
