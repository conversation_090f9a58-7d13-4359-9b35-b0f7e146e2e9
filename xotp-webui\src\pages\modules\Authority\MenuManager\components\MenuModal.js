import React, { PureComponent } from 'react';
import { Modal, Row, Col, Input, Select } from 'antd/lib/index';
import styles from '@/pages/modules/Authority/DeptManager/DeptManager.less';
import { Form } from '@ant-design/compatible';

@Form.create()
class MenuModal extends PureComponent {

  constructor(props) {
    super(props);
    this.state = {pathRequire:false, onceEdit:true} //根据菜单类型判断菜单路径是否必填
  }

  handleOk = (e) => {
    const modalForm = this.props.form;
    if (this.props.handleOk) {
      this.props.handleOk(e, modalForm,this);
    }
  };

  handleCancel = (e) => {
    const modalForm = this.props.form;
    if (this.props.handleCancel) {
      this.props.handleCancel(e, modalForm);
    }
    this.setState({pathRequire:false,onceEdit: true})
  };

  selectMenuType = (e) => {
    if(e == 1) { //菜单
      this.setState({pathRequire: true})
    }else{ //按钮或其它
      this.setState({pathRequire: false})
    }
  }

  validatorName = (rule, value, callback) => {
    if (value && value.length > 50) {
      callback('菜单名称长度不能大于50！');
    }
    callback();
  };

  validatorCode = (rule, value, callback) => {
    if (value && value.length > 50) {
      callback('菜单编码长度不能大于50！');
    }
    callback();
  };

  validatorSortNo = (rule, value, callback) => {
    if (value && value.length > 150) {
      callback('排序编码长度不能大于150！');
    }
    callback();
  };

  validatorUrl = (rule, value, callback) => {
    if (value && value.length > 50) {
      callback('资源地址长度不能大于50！');
    }
    callback();
  };

  render = () => {
    const formItemLayout = {
      labelCol: {
        md: { span: 8 },
      },
      wrapperCol: {
        md: { span: 16 },
      },
    };
    const formItemLayout1 = {
      labelCol: {
        md: { span: 4 },
      },
      wrapperCol: {
        md: { span: 20 },
      },
    };
    const { field, method, modalVisible, modalTitle, resources,rowMenuData } = this.props;
    const { getFieldDecorator } = this.props.form;
    const destroyOnClose = true;
    const mode = 'multiple';
    let treeNode = field;
    if (treeNode === undefined || treeNode === null) {
      treeNode = [];
    }
    let resourceIds = [];
    if (method !== 'add') {
      if (treeNode && treeNode.resourceIds) {
        resourceIds = treeNode.resourceIds.split(',');
      }
    }
    console.log(rowMenuData)
    if(method == "edit" && this.state.onceEdit) {
      this.setState({pathRequire: rowMenuData.type==1})
      this.setState({onceEdit: false})
    }

    return (
      <Modal
        title={modalTitle}
        visible={modalVisible}
        wrapClassName="vertical-center-modal"
        destroyOnClose={destroyOnClose}
        onCancel={this.handleCancel}
        onOk={this.handleOk}
        cancelText="取消"
        okText="保存"
        width={600}
        maskClosable={false}
      >
        <Form>
          <Form.Item  style={{display:'none'}}>
            {getFieldDecorator('id', {
              initialValue: rowMenuData&&rowMenuData.id,
            })(<Input type={'hidden'} />)}
          </Form.Item>
          <Form.Item  style={{display:'none'}}>
            {getFieldDecorator('parentId', {
              initialValue: method === 'add' ?(rowMenuData&&rowMenuData.level!==0?rowMenuData&&rowMenuData.id:null) : rowMenuData&&rowMenuData.parentId,
            })(<Input type={'hidden'} />)}
          </Form.Item>
          <Row >
            <Col span={12}>
              <Form.Item {...formItemLayout} label="菜单名称：" style={{ display: 'flex', flex: 1 }} >
                {getFieldDecorator('name', {
                  rules: [
                    { required: true, message: '菜单名称不能为空' },
                    { max: 20, message: '最多可输入20字' },
                  ],
                  initialValue: method === 'add' ? '' : rowMenuData&&rowMenuData.text,
                })(
                  <Input placeholder="请输入" />
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item {...formItemLayout} label="菜单编码：" style={{ display: 'flex', flex: 1 }} >
                {getFieldDecorator('code', {
                  rules: [
                    { required: true, message: '菜单编码不能为空' },
                    { max: 20, message: '最多可输入20字' },
                    { pattern: new RegExp(/^[A-Za-z0-9_]*$/, "g"),message: '请输入英文，数字，下划线'},
                  ],
                  initialValue: method === 'add' ? '' : rowMenuData&&rowMenuData.code,
                })(
                  <Input placeholder="请输入" />
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row >
            <Col span={12}>
              <Form.Item {...formItemLayout} label="菜单类型" style={{ display: 'flex', flex: 1 }} >
                {getFieldDecorator('type', {
                  rules: [{ required: true, message: '菜单类型不能为空' }],
                  initialValue: method === 'add' ? '' : rowMenuData&&rowMenuData.type,
                })(
                  <Select
                    style={{ width: '100%' }}
                    placeholder="请选择"
                    onSelect = {this.selectMenuType}
                  >
                    <Select.Option key="1">菜单</Select.Option>
                    <Select.Option key="2">按钮</Select.Option>
                    <Select.Option key="3">其他</Select.Option>
                  </Select>
                )}
              </Form.Item>
            </Col>
            {/*<Col span={12}>*/}
            {/*  <Form.Item {...formItemLayout} label="加载类型" style={{ display: 'flex', flex: 1 }} >*/}
            {/*    {getFieldDecorator('loadType', {*/}
            {/*      rules: [{ required: true, message: '加载类型不能为空' }],*/}
            {/*      initialValue: method === 'add' ? '' : rowMenuData&&rowMenuData.loadType,*/}
            {/*    })(*/}
            {/*      <Select*/}
            {/*        style={{ width: '100%' }}*/}
            {/*        placeholder="请选择"*/}
            {/*      >*/}
            {/*        <Select.Option key="1">web</Select.Option>*/}
            {/*        <Select.Option key="2">pda</Select.Option>*/}
            {/*        <Select.Option key="3">其他</Select.Option>*/}
            {/*      </Select>*/}
            {/*    )}*/}
            {/*  </Form.Item>*/}
            {/*</Col>*/}
          </Row>
          <Row >
            <Col span={12}>
              <Form.Item {...formItemLayout} label="是否启用" style={{ display: 'flex', flex: 1 }} >
                {getFieldDecorator('status', {
                  rules: [{ required: true, message: '是否启用不能为空' }],
                  initialValue: method === 'add' ? '1' : rowMenuData&&rowMenuData.status,
                })(
                  <Select
                    style={{ width: '100%' }}
                    placeholder="请选择"
                  >
                    <Select.Option key="1">启用</Select.Option>
                    <Select.Option key="0">禁用</Select.Option>
                  </Select>
                )}
              </Form.Item>
            </Col>
            {/*{window.location.pathname=='/menu/menuManager'?null:<Col span={12}>*/}
            {/*  <Form.Item {...formItemLayout} label="系统绑定" style={{ display: 'flex', flex: 1 }} >*/}
            {/*      {getFieldDecorator('systemId', {*/}
            {/*        initialValue: method === 'add' ? ' ' : rowMenuData&&rowMenuData.systemId,*/}
            {/*      })(*/}
            {/*        <Select*/}
            {/*          // mode={mode}*/}
            {/*          style={{ width: '100%' }}*/}
            {/*          placeholder="请选择"*/}
            {/*        >*/}
            {/*          {resources ?*/}
            {/*            resources.map(item =>*/}
            {/*              <Select.Option key={item.id}>{item.name}</Select.Option>) : []}*/}
            {/*        </Select>*/}
            {/*      )}*/}
            {/*  </Form.Item>*/}
            {/*</Col>}*/}
          </Row>
          <Row>
            <Col span={12}>
              {/*<Form.Item {...formItemLayout1} label="资源绑定" style={{ display: 'flex', flex: 1 }} >*/}
              {/*  {getFieldDecorator('resourceIds', {*/}
              {/*    initialValue: resourceIds,*/}
              {/*  })(*/}
              {/*    <Select*/}
              {/*      mode={mode}*/}
              {/*      style={{ width: '100%' }}*/}
              {/*      placeholder="请选择"*/}
              {/*    >*/}
              {/*      {resources ?*/}
              {/*        resources.map(item =>*/}
              {/*          <Select.Option key={item.id}>{item.name}</Select.Option>) : []}*/}
              {/*    </Select>*/}
              {/*  )}*/}
              {/*</Form.Item>*/}
              <Form.Item {...formItemLayout} label="菜单路径：" >
                {getFieldDecorator('path', {
                  rules: [
                    { required: this.state.pathRequire, message: '菜单路径不能为空' },
                    { max: 255, message: '最长不能输入255位' },
                    { pattern: new RegExp(/^(\/([a-zA-Z]|[0-9])*)*$/, "g"),
                      message: '请输入如/test/test地址'
                    },
                  ],
                  initialValue: method === 'add' ? '' : rowMenuData&&rowMenuData.path,
                })(
                  <Input placeholder="请输入" />
                )}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  };
}
export default MenuModal;
