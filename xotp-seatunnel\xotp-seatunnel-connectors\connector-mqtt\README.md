# SeaTunnel MQTT Connector

SeaTunnel MQTT Connector支持从MQTT消息队列读取数据和向MQTT消息队列写入数据。基于Eclipse Paho MQTT v3客户端实现，支持MQTT 3.1.1协议。

## 功能特性

### Source端功能
- ✅ 支持多个MQTT Broker的failover连接
- ✅ 支持订阅多个Topic
- ✅ 支持QoS 0、1、2三种服务质量等级
- ✅ 支持用户名/密码认证
- ✅ 支持SSL/TLS加密连接
- ✅ 支持自动重连机制
- ✅ 支持流批一体处理
- ✅ 支持并行度配置
- ✅ 支持JSON、TEXT、XML三种消息格式解析
- ✅ 防重复订阅机制

### Sink端功能
- ✅ 支持多个MQTT Broker的failover连接
- ✅ 支持固定Topic和动态Topic模式
- ✅ 支持QoS 0、1、2三种服务质量等级
- ✅ 支持Retained消息
- ✅ 支持JSON和TEXT两种消息格式
- ✅ 支持用户名/密码认证
- ✅ 支持SSL/TLS加密连接
- ✅ 支持自动重连机制

## 依赖要求

- SeaTunnel 2.3.11+
- Eclipse Paho MQTT v3 Client 1.2.5+
- Java 8+

## Source配置参数

| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| broker.urls | List<String> | 是 | - | MQTT Broker地址列表，格式：tcp://host:port |
| topics | List<String> | 是 | - | 订阅的Topic列表 |
| qos | Integer | 否 | 1 | 服务质量等级，取值：0、1、2 |
| client.id | String | 否 | auto-generated | MQTT客户端ID |
| username | String | 否 | - | 认证用户名 |
| password | String | 否 | - | 认证密码 |
| connection.timeout | Integer | 否 | 30 | 连接超时时间（秒） |
| keep.alive.interval | Integer | 否 | 60 | 心跳间隔（秒） |
| clean.session | Boolean | 否 | true | 是否清除会话 |
| ssl.enabled | Boolean | 否 | false | 是否启用SSL |
| message.format | String | 否 | json | 消息格式：json（默认）、text/plain（纯文本）、xml（XML格式） |
| poll.timeout | Long | 否 | 1000 | 轮询超时时间（毫秒） |
| auto.reconnect | Boolean | 否 | true | 是否自动重连 |

## Sink配置参数

| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| broker.urls | List<String> | 是 | - | MQTT Broker地址列表 |
| topic | String | 是 | - | 发布消息的目标topic |
| qos | Integer | 否 | 1 | 服务质量等级 |
| retained | Boolean | 否 | false | 是否保留消息 |
| message.format | String | 否 | json | 消息格式：json、text |
| client.id | String | 否 | auto-generated | MQTT客户端ID |
| username | String | 否 | - | 认证用户名 |
| password | String | 否 | - | 认证密码 |
| connection.timeout | Integer | 否 | 30 | 连接超时时间（秒） |
| keep.alive.interval | Integer | 否 | 60 | 心跳间隔（秒） |
| clean.session | Boolean | 否 | true | 是否清除会话 |
| ssl.enabled | Boolean | 否 | false | 是否启用SSL |
| auto.reconnect | Boolean | 否 | true | 是否自动重连 |

## 消息格式说明

### 1. JSON格式（默认）
当`message.format = "json"`时，connector会使用SeaTunnel提供的JsonDeserializationSchema来解析JSON消息。消息必须是有效的JSON格式，字段名需要与schema定义匹配。

**示例JSON消息：**
```json
{
  "id": "sensor001",
  "epoch": 1640995200,
  "model_name": "temperature_sensor"
}
```

**对应的schema配置：**
```hocon
schema = {
  fields {
    id = "string"
    epoch = "int"
    model_name = "string"
  }
}
```

### 2. 纯文本格式（TEXT/PLAIN）
当`message.format = "text"`或`"plain"`时，connector支持两种文本处理模式：

**单字段模式：**如果schema只定义一个字段，整个消息内容作为该字段的值。
```hocon
message.format = "text"
schema = {
  fields {
    log_message = "string"  # 整个消息内容
  }
}
```

**CSV模式：**如果定义多个字段，使用逗号分隔符分割消息内容。
```hocon
message.format = "text"
schema = {
  fields {
    name = "string"
    age = "int"
    city = "string"
  }
}
```
对应消息：`"John,25,Beijing"`

### 3. XML格式
当`message.format = "xml"`时，connector使用Jackson XML解析器解析XML消息，支持嵌套字段访问。

**示例XML消息：**
```xml
<sensor>
  <sensor_id>temp001</sensor_id>
  <temperature>23.5</temperature>
  <location>
    <city>Beijing</city>
    <country>China</country>
  </location>
</sensor>
```

**对应的schema配置：**
```hocon
message.format = "xml"
schema = {
  fields {
    sensor_id = "string"           # 直接字段访问
    temperature = "double"         # 数值类型自动转换
    "location.city" = "string"     # 嵌套字段访问
  }
}
```

## 使用示例

### JSON格式消息处理

```hocon
env {
  job.mode = "STREAMING"
  job.name = "mqtt-json-example"
}

source {
  plugin_name = "Mqtt"
  broker.urls = ["tcp://***********:11883"]
  topics = ["iot/test/topic"]
  qos = 1
  client.id = "seatunnel-source-001"
  message.format = "json"
  
  schema = {
    fields {
      id = "string"
      epoch = "int"
      model_name = "string"
    }
  }
}

sink {
  plugin_name = "Mqtt"
  broker.urls = ["tcp://***********:11883"]
  topic = "iot/test/topic_processed"
  message.format = "json"
  qos = 1
  retained = false
}
```

### XML格式消息处理

```hocon
source {
  plugin_name = "Mqtt"
  broker.urls = ["tcp://localhost:1883"]
  topics = ["sensors/xml"]
  qos = 1
  message.format = "xml"
  
  schema = {
    fields {
      sensor_id = "string"
      temperature = "double"
      "location.city" = "string"  # 嵌套字段
    }
  }
}
```

### 文本格式消息处理

```hocon
source {
  plugin_name = "Mqtt"
  broker.urls = ["tcp://localhost:1883"]
  topics = ["logs/text"]
  qos = 1
  message.format = "plain"
  
  schema = {
    fields {
      log_message = "string"
    }
  }
}
```

## 故障排除

### 常见问题

1. **重复订阅日志**
   - **问题**: 日志中出现重复的"Subscribed to MQTT topic"消息
   - **原因**: 早期版本的订阅逻辑缺陷
   - **解决**: 已在最新版本中修复，添加了订阅状态管理机制

2. **无法读取消息**
   - **问题**: 消息格式无法正确解析
   - **原因**: 消息格式与schema不匹配，或message.format设置错误
   - **解决**: 确保消息结构与schema定义一致，正确设置message.format

3. **XML解析失败**
   - **问题**: XML消息解析报错
   - **原因**: XML格式不正确或字段路径错误
   - **解决**: 检查XML格式，使用"parent.child"语法访问嵌套字段

4. **连接失败**
   - **问题**: 无法连接到MQTT Broker
   - **原因**: 网络问题、认证失败或配置错误
   - **解决**: 检查broker.urls、网络连接、用户名密码等配置

## 版本历史

### v2.3.11+
- 修复重复订阅问题
- 新增XML格式支持，使用Jackson XML解析器
- 增强TEXT格式支持，支持CSV模式
- 添加订阅状态管理机制
- 提升消息处理性能和稳定性

## 技术支持

如果遇到问题，请检查：
1. MQTT Broker连接状态
2. 消息格式是否正确
3. Schema定义是否匹配消息结构
4. 网络连接和防火墙配置
5. 对于XML格式，确认字段路径语法正确 
