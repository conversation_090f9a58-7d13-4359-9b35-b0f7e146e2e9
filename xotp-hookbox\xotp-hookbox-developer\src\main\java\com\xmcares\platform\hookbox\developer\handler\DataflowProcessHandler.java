/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/28
 */
package com.xmcares.platform.hookbox.developer.handler;

import com.xmcares.platform.hookbox.common.job.context.JsonJobParams;
import com.xmcares.platform.hookbox.developer.model.DataflowProcess;
import com.xmcares.platform.hookbox.developer.service.DataflowProcessService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
@Component
public class DataflowProcessHandler {

    @Resource
    private DataflowProcessService processService;



    @XxlJob(value = "DataflowJobHandler")
    public void execute() {
        XxlJobHelper.log("进入数据开发流程启动进程");
        Date startTime = new Date();//实际开始时间

        String xxlJobId = XxlJobHelper.getJobId() + "";
        JsonJobParams jobParams = new JsonJobParams(XxlJobHelper.getJobParam());

        String processId = jobParams.getParam("processId");

        DataflowProcess process = processService.getDataflowProcess(processId);







    }



}
