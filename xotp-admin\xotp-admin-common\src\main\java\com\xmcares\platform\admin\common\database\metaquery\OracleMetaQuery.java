package com.xmcares.platform.admin.common.database.metaquery;

import com.xmcares.platform.admin.common.database.DataSourceBasedMetaQuery;
import com.xmcares.platform.admin.common.database.metainfo.ColumnInfo;
import com.xmcares.platform.admin.common.database.metainfo.TableInfo;
import com.xmcares.platform.admin.common.errors.MetadataException;
import com.xmcares.platform.admin.common.jdbc.JdbcUtils;
import org.apache.commons.dbutils.handlers.BeanHandler;
import org.apache.commons.dbutils.handlers.BeanListHandler;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.List;

/**
 * Oracle元数据表查询
 * Oracle元数据查询
 *
 * <AUTHOR>
 * @since 2.1.0
 */
public class OracleMetaQuery extends DataSourceBasedMetaQuery {

    private static final String SQL_TABLES = "SELECT table_name AS name, comments AS comments FROM user_tab_comments";
    private static final String SQL_TABLE_COLUMNS = "SELECT column_name AS name, data_type AS type, comments AS comments " +
            "FROM user_col_comments JOIN user_tab_columns USING(table_name, column_name) WHERE table_name = ?";

    public OracleMetaQuery(DataSource dataSource) {
        super(dataSource);
    }

    public OracleMetaQuery(DataSource dataSource, String schema) {
        super(dataSource, schema);
    }

    @Override
    public TableInfo getTableInfo(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            throw new IllegalArgumentException("表名不能为空");
        }
        try {
            // 查询特定表的元信息
            return JdbcUtils.executeQuery(this.dataSource, SQL_TABLES + " WHERE table_name = ?", new BeanHandler<>(TableInfo.class), tableName);
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取数据库[%s]表[%s]的 TableInfo 失败", this.schema, tableName), e);
        }
    }

    @Override
    public List<TableInfo> getTableInfos() {
        try {
            // 查询所有表的元信息
            return JdbcUtils.executeQuery(this.dataSource, SQL_TABLES, new BeanListHandler<>(TableInfo.class));
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取数据库[%s]所有表的 TableInfo 失败", this.schema), e);
        }
    }

    @Override
    public List<ColumnInfo> getColumnInfos(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            throw new IllegalArgumentException("表名不能为空");
        }
        try {
            // 查询特定表的列元信息
            return JdbcUtils.executeQuery(this.dataSource, SQL_TABLE_COLUMNS, new BeanListHandler<>(ColumnInfo.class), tableName);
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取数据库[%s]表[%s]的列 ColumnInfo 失败", this.schema, tableName), e);
        }
    }
}
