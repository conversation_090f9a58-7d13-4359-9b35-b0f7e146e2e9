server.port=8085
spring.application.name=xotp-admin

#### 1. Web properties: Http\uFF0CServlet, MVC...
management.endpoints.web.exposure.include=*
spring.main.allow-bean-definition-overriding=true
#spring.main.allow-circular-references=true
spring.servlet.multipart.max-file-size=256MB
spring.servlet.multipart.max-request-size=256MB

#### 2. DataSource & JDBC Properties
spring.datasource.url=jdbc:mysql://*************:3306/xotp?useSSL=false&zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf8&rewriteBatchedStatements=true&autoReconnect=true
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.hikari.minimum-idle=100
spring.datasource.hikari.maximum-pool-size=500
## idle timeout, default value 600000ms(10 minutes)
spring.datasource.hikari.idle-timeout=600000
## max live time, default value 1800000ms(30 minutes)
spring.datasource.hikari.max-lifetime=1800000
## connection timeout, default value 30000ms(30 seconds)
spring.datasource.hikari.connection-timeout=30000
### jdbc template properties
spring.jdbc.template.fetch-size=1000
spring.jdbc.template.max-rows=1000
spring.jdbc.template.query-timeout=10s

### resources
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.mvc.servlet.load-on-startup=0
spring.mvc.static-path-pattern=/static/**
spring.web.resources.static-locations=classpath:/static/

### freemarker
spring.freemarker.templateLoaderPath=classpath:/templates/
spring.freemarker.suffix=.ftl
spring.freemarker.charset=UTF-8
spring.freemarker.request-context-attribute=request
spring.freemarker.settings.number_format=0.##########
spring.freemarker.settings.new_builtin_class_resolver=safer

### mybatis properties
mybatis.mapper-locations=classpath*:/mapper/**/*.xml,classpath*:/mybatis-mapper/*Mapper.xml
mybatis-plus.mapper-locations=${mybatis.mapper-locations}
mybatis-plus.global-config.db-config.update-strategy=IGNORED

spring.session.store-type=none

### redis setting
#spring.redis.host=***********
#spring.redis.port=6379
#spring.redis.database=3

#### 3. spring cloud/micro service Properties
### nacos properties
## using test namespace
#spring.cloud.nacos.discovery.namespace=4da9e0aa-c002-4628-b221-a3e54ad832bc
#spring.cloud.nacos.config.namespace=4da9e0aa-c002-4628-b221-a3e54ad832bc
#spring.cloud.sentinel.enabled=false


#### 4. xcnf properties
## xcnf mvc properties
xcnf.mvc.cors-enable=true

## xcnf security properties
xcnf.security.debug=true
## exclude paths for authentication and authorization. exclude paths of xxl-job.
xcnf.security.exclude-paths=/toLogin,/api/**,/extend-api/**,/jobinfo/**,/joblog/**,/jobgroup/**
xcnf.security.rbac.login.captcha-after-failures=10000

## xcnf file server's type. eg: ftp, sftp, minio, fastdfs, xfs(xmcares file service)
xcnf.data.fsclient.type=ftp
xcnf.data.fsclient.ftp.host=*************
xcnf.data.fsclient.ftp.username=tech
xcnf.data.fsclient.ftp.password=tech@2023
xcnf.data.fsclient.ftp.pool.jmx-enabled=false
xcnf.data.fsclient.ftp.port=21

#### 5. xotp platform properties
### openfeign service model\uFF1Afor scheduler service remoted invoking.
xbdp.feign.scheduler-service.name=${spring.application.name}
xbdp.feign.scheduler-service.url=127.0.0.1:${server.port}
xbdp.feign.scheduler-service.xxl-job.access-token=default_token

### integrator module properties ======
#xbdp.integrator.file-server-root=/xbdp/integrator
#xbdp.integrator.local-tmp-root=/tmp/integrator
#xbdp.integrator.xxl-job.group=integrator

### developer module properties ======
##
xbdp.task.developer.clear.cron=0 */1 * * * ?
xbdp.developer.scheduler.hookbox=127.0.0.1:8082
xbdp.developer.fileServerRoot=/projectUploadFile/xbdp
xbdp.developer.fileServerSeparator=/
xbdp.developer.tmpRoot=/tmp


### scheduler(Based on XXL-JOB) module properties
## xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
#xxl.job.admin.addresses=http://*************:31411
xxl.job.admin.addresses=http://127.0.0.1:${server.port}
## xxl-job, access token
xxl.job.accessToken=default_token
### xxl-job, i18n (default is zh_CN, and you can choose "zh_CN", "zh_TC" and "en")
xxl.job.i18n=zh_CN
xxl.job.timeout=3
## xxl-job, triggerpool max size
xxl.job.triggerpool.fast.max=200
xxl.job.triggerpool.slow.max=100
### xxl-job, log retention days
xxl.job.logretentiondays=30

### xxl-job, email
spring.mail.host=smtp.qq.com
spring.mail.port=25
spring.mail.username=<EMAIL>
spring.mail.from=<EMAIL>
spring.mail.password=xxx
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory

xbdp.hookbox.integrotro.base-url=http://127.0.0.1:8082




