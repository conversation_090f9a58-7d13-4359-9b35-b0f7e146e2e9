import request from '@/utils/request';
import { baseUrl,integratorUrl,metadataUrl,schedulerUrl } from '@/utils/baseUrl';
import * as utils from '@/utils/utils';

/**
 * 数据同步定义分页列表查询
 * @param data 查询条件
 * @returns {Promise<unknown>}
 */
export function query(data) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync/page-query`,data);
  return request(path, {
    method: 'GET',
  });
}

/**
 * 获取数据同步定义明细信息
 * @param id id
 * @returns {Promise<unknown>}
 */
export function getDatasync(id) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync/get`,{id: id});
  return request(path, {
    method: 'GET',
  });
}

/**
 * 创建数据同步定义信息
 * @param data
 * @returns {Promise<unknown>}
 */
export function create(data) {
  return request(`${integratorUrl}/datasync/add`, {
    method: 'POST',
    body: data,
  });
}

/**
 * 更新数据同步定义信息
 * @param data
 * @returns {Promise<unknown>}
 */
export function update(data) {
  return request(`${integratorUrl}/datasync/update`, {
    method: 'POST',
    body: data,
  });
}

/**
 * 发布数据同步定义信息
 * @param data
 * @returns {Promise<unknown>}
 */
export function publish(data) {
  return request(`${integratorUrl}/datasync/publish`, {
    method: 'POST',
    body: data,
  });
}

/**
 * 删除数据同步定义信息
 * @param id
 * @returns {Promise<unknown>}
 */
export function datasyncDelete(id) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync/delete`,{id: id});
  return request(path, {
    method: 'GET',
  });
}

/**
 * 读取配置文件信息/datasync/displayJson
 * @param id
 * @returns {Promise<unknown>}
 */
export function readerJson(id) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync/displayJson`,{id: id});
  return request(path, {
    method: 'GET',
  });
}




export function querySourceList(data) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-model/category/list-query`,data);
  return request(path, {
    method: 'GET',
  });
}

export function getTemplate(data) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync/template/get`,data);
  return request(path, {
    method: 'GET',
  });
}

// 插件列表
export function datasyncPluginList(data) {
  return request(`${metadataUrl}/datasync-model/list-query`, {
    method: 'POST',
    body: data,
  });
}
// 数据源列表
export function datasyncSourceList() {
  return request(`${metadataUrl}/datasource/list-query`, {
    method: 'POST',

  });
}


// 数据表字段列表
export function datasyncSourceTableList(data) {
  const path=utils.getQueryPath(`${integratorUrl}/datasource-table/detail-queryByName`,data);
  return request(path, {
    method: 'GET',
  });
}

// 获取任务列表
export function datasChildJobIdList() {
  return request(`${metadataUrl}/datasync-job/instance/list`, {
    method: 'GET',

  });
}

//获取集成模型信息-根据数据源
export function datasyncSourceGet(data) {
  const path=utils.getQueryPath(`${metadataUrl}/datasync-model/get/by/datasource`,data);
  return request(path, {
    method: 'GET',
  });
}



//获取集成模型信息-根据插件ID
export function datasyncPluginGet(data) {
  const path=utils.getQueryPath(`${metadataUrl}/datasync-model/get`,data);
  return request(path, {
    method: 'GET',
  });
}

//新增同步
export function datasyncAdd(data) {
  return request(`${integratorUrl}/datasync/add`, {
    method: 'POST',
    body: data,
  });
}

//修改同步

export function datasyncUpdate(data) {
  return request(`${integratorUrl}/datasync/update`, {
    method: 'POST',
    body: data,
  });
}





// 获取最近5次的任务触发时间/xbdp/job/nextTriggerTime
export function getNextRunTimer(data) {
  const path=utils.getQueryPath(`${integratorUrl}/datasync-job/instance/nextTriggerTime`,data);
  return request(path, {
    method: 'GET',
  });
}


