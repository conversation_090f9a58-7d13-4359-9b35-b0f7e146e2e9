package com.xmcares.platform.hookbox.integrator.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(description="xxl_job_log")
@TableName(value = "xxl_job_log")
public class IntegratorXxlJobLog {
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value="")
    private Long id;

    /**
     * 执行器主键ID
     */
    @TableField(value = "job_group")
    @ApiModelProperty(value="执行器主键ID")
    private Integer jobGroup;

    /**
     * 任务，主键ID
     */
    @TableField(value = "job_id")
    @ApiModelProperty(value="任务，主键ID")
    private Integer jobId;

    /**
     * 执行器地址，本次执行的地址
     */
    @TableField(value = "executor_address")
    @ApiModelProperty(value="执行器地址，本次执行的地址")
    private String executorAddress;

    /**
     * 执行器任务handler
     */
    @TableField(value = "executor_handler")
    @ApiModelProperty(value="执行器任务handler")
    private String executorHandler;

    /**
     * 执行器任务参数
     */
    @TableField(value = "executor_param")
    @ApiModelProperty(value="执行器任务参数")
    private String executorParam;

    /**
     * 执行器任务分片参数，格式如 1/2
     */
    @TableField(value = "executor_sharding_param")
    @ApiModelProperty(value="执行器任务分片参数，格式如 1/2")
    private String executorShardingParam;

    /**
     * 失败重试次数
     */
    @TableField(value = "executor_fail_retry_count")
    @ApiModelProperty(value="失败重试次数")
    private Integer executorFailRetryCount;

    /**
     * 调度-时间
     */
    @TableField(value = "trigger_time")
    @ApiModelProperty(value="调度-时间")
    private Date triggerTime;

    /**
     * 调度-结果
     */
    @TableField(value = "trigger_code")
    @ApiModelProperty(value="调度-结果")
    private Integer triggerCode;

    /**
     * 调度-日志
     */
    @TableField(value = "trigger_msg")
    @ApiModelProperty(value="调度-日志")
    private String triggerMsg;

    /**
     * 执行-时间
     */
    @TableField(value = "handle_time")
    @ApiModelProperty(value="执行-时间")
    private Date handleTime;

    /**
     * 执行-状态
     */
    @TableField(value = "handle_code")
    @ApiModelProperty(value="执行-状态")
    private Integer handleCode;

    /**
     * 执行-日志
     */
    @TableField(value = "handle_msg")
    @ApiModelProperty(value="执行-日志")
    private String handleMsg;

    /**
     * 告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败
     */
    @TableField(value = "alarm_status")
    @ApiModelProperty(value="告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败")
    private Byte alarmStatus;

    /**
     * @return id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取执行器主键ID
     *
     * @return job_group - 执行器主键ID
     */
    public Integer getJobGroup() {
        return jobGroup;
    }

    /**
     * 设置执行器主键ID
     *
     * @param jobGroup 执行器主键ID
     */
    public void setJobGroup(Integer jobGroup) {
        this.jobGroup = jobGroup;
    }

    /**
     * 获取任务，主键ID
     *
     * @return job_id - 任务，主键ID
     */
    public Integer getJobId() {
        return jobId;
    }

    /**
     * 设置任务，主键ID
     *
     * @param jobId 任务，主键ID
     */
    public void setJobId(Integer jobId) {
        this.jobId = jobId;
    }

    /**
     * 获取执行器地址，本次执行的地址
     *
     * @return executor_address - 执行器地址，本次执行的地址
     */
    public String getExecutorAddress() {
        return executorAddress;
    }

    /**
     * 设置执行器地址，本次执行的地址
     *
     * @param executorAddress 执行器地址，本次执行的地址
     */
    public void setExecutorAddress(String executorAddress) {
        this.executorAddress = executorAddress;
    }

    /**
     * 获取执行器任务handler
     *
     * @return executor_handler - 执行器任务handler
     */
    public String getExecutorHandler() {
        return executorHandler;
    }

    /**
     * 设置执行器任务handler
     *
     * @param executorHandler 执行器任务handler
     */
    public void setExecutorHandler(String executorHandler) {
        this.executorHandler = executorHandler;
    }

    /**
     * 获取执行器任务参数
     *
     * @return executor_param - 执行器任务参数
     */
    public String getExecutorParam() {
        return executorParam;
    }

    /**
     * 设置执行器任务参数
     *
     * @param executorParam 执行器任务参数
     */
    public void setExecutorParam(String executorParam) {
        this.executorParam = executorParam;
    }

    /**
     * 获取执行器任务分片参数，格式如 1/2
     *
     * @return executor_sharding_param - 执行器任务分片参数，格式如 1/2
     */
    public String getExecutorShardingParam() {
        return executorShardingParam;
    }

    /**
     * 设置执行器任务分片参数，格式如 1/2
     *
     * @param executorShardingParam 执行器任务分片参数，格式如 1/2
     */
    public void setExecutorShardingParam(String executorShardingParam) {
        this.executorShardingParam = executorShardingParam;
    }

    /**
     * 获取失败重试次数
     *
     * @return executor_fail_retry_count - 失败重试次数
     */
    public Integer getExecutorFailRetryCount() {
        return executorFailRetryCount;
    }

    /**
     * 设置失败重试次数
     *
     * @param executorFailRetryCount 失败重试次数
     */
    public void setExecutorFailRetryCount(Integer executorFailRetryCount) {
        this.executorFailRetryCount = executorFailRetryCount;
    }

    /**
     * 获取调度-时间
     *
     * @return trigger_time - 调度-时间
     */
    public Date getTriggerTime() {
        return triggerTime;
    }

    /**
     * 设置调度-时间
     *
     * @param triggerTime 调度-时间
     */
    public void setTriggerTime(Date triggerTime) {
        this.triggerTime = triggerTime;
    }

    /**
     * 获取调度-结果
     *
     * @return trigger_code - 调度-结果
     */
    public Integer getTriggerCode() {
        return triggerCode;
    }

    /**
     * 设置调度-结果
     *
     * @param triggerCode 调度-结果
     */
    public void setTriggerCode(Integer triggerCode) {
        this.triggerCode = triggerCode;
    }

    /**
     * 获取调度-日志
     *
     * @return trigger_msg - 调度-日志
     */
    public String getTriggerMsg() {
        return triggerMsg;
    }

    /**
     * 设置调度-日志
     *
     * @param triggerMsg 调度-日志
     */
    public void setTriggerMsg(String triggerMsg) {
        this.triggerMsg = triggerMsg;
    }

    /**
     * 获取执行-时间
     *
     * @return handle_time - 执行-时间
     */
    public Date getHandleTime() {
        return handleTime;
    }

    /**
     * 设置执行-时间
     *
     * @param handleTime 执行-时间
     */
    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    /**
     * 获取执行-状态
     *
     * @return handle_code - 执行-状态
     */
    public Integer getHandleCode() {
        return handleCode;
    }

    /**
     * 设置执行-状态
     *
     * @param handleCode 执行-状态
     */
    public void setHandleCode(Integer handleCode) {
        this.handleCode = handleCode;
    }

    /**
     * 获取执行-日志
     *
     * @return handle_msg - 执行-日志
     */
    public String getHandleMsg() {
        return handleMsg;
    }

    /**
     * 设置执行-日志
     *
     * @param handleMsg 执行-日志
     */
    public void setHandleMsg(String handleMsg) {
        this.handleMsg = handleMsg;
    }

    /**
     * 获取告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败
     *
     * @return alarm_status - 告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败
     */
    public Byte getAlarmStatus() {
        return alarmStatus;
    }

    /**
     * 设置告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败
     *
     * @param alarmStatus 告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败
     */
    public void setAlarmStatus(Byte alarmStatus) {
        this.alarmStatus = alarmStatus;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", jobGroup=").append(jobGroup);
        sb.append(", jobId=").append(jobId);
        sb.append(", executorAddress=").append(executorAddress);
        sb.append(", executorHandler=").append(executorHandler);
        sb.append(", executorParam=").append(executorParam);
        sb.append(", executorShardingParam=").append(executorShardingParam);
        sb.append(", executorFailRetryCount=").append(executorFailRetryCount);
        sb.append(", triggerTime=").append(triggerTime);
        sb.append(", triggerCode=").append(triggerCode);
        sb.append(", triggerMsg=").append(triggerMsg);
        sb.append(", handleTime=").append(handleTime);
        sb.append(", handleCode=").append(handleCode);
        sb.append(", handleMsg=").append(handleMsg);
        sb.append(", alarmStatus=").append(alarmStatus);
        sb.append("]");
        return sb.toString();
    }
}
