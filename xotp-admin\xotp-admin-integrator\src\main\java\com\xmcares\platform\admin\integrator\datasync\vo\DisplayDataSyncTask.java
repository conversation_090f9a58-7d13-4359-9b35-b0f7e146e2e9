package com.xmcares.platform.admin.integrator.datasync.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncInstance;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncJob;
import com.xmcares.platform.admin.integrator.datasync.model.SchedulerJob;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> chenYG
 * @date : 2022/3/28 11:07
 */
@ApiModel(value = "DisplayDataSyncTask", description = "数据同步列表视图")
public class DisplayDataSyncTask implements Serializable {

    /** ID */
    @ApiModelProperty(value = "主键")
    private String id;
    /** 父表ID */
    @ApiModelProperty(value = "父表ID")
    private String datasyncId;
    /** 发布时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发布时间")
    private Date publishTime;
    /** 更新时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /** 发布人 */
    @ApiModelProperty(value = "发布人")
    private String publishUser;
    /** 实例名称 */
    @ApiModelProperty(value = "实例名称")
    private String instanceName;
    /** 实例编码 */
    @ApiModelProperty(value = "实例编码")
    private String instanceCode;
    /** 数据来源集成方式 0:内置 1:插件 */
    @ApiModelProperty(value = "数据来源集成方式", notes = "0:内置 1:插件")
    private String orginType;
    /** 数据来源数据源名称 */
    @ApiModelProperty(value = "数据来源数据源名称")
    private String orginDatasourceName;
    /** 数据来源插件路径 */
    @ApiModelProperty(value = "数据来源插件路径")
    private String orginPluginPath;
    /** 数据去向集成方式 0:内置 1：插件 */
    @ApiModelProperty(value = "数据去向集成方式", notes = "0:内置 1:插件")
    private String destType;
    /** 数据去向数据源名称 */
    @ApiModelProperty(value = "数据去向数据源名称")
    private String destDatasourceName;
    /** 数据去向插件路径 */
    @ApiModelProperty(value = "数据去向插件路径")
    private String destPluginPath;
    /** 调度任务ID */
    @ApiModelProperty(value = "调度任务ID")
    private String dispatchId;
    /** 调度执行器ID */
    @ApiModelProperty(value = "调度执行器ID")
    private String jobGroup;
    /** 调度参数 */
    @ApiModelProperty(value = "调度参数")
    private String schedulerExpr;
    /** 路由策略 */
    @ApiModelProperty(value = "路由策略")
    private String routeStrategy;
    /** 阻塞策略 */
    @ApiModelProperty(value = "阻塞策略")
    private String blockStrategy;
    /** 执行超时时间 */
    @ApiModelProperty(value = "执行超时时间")
    private int executorTimeout;
    /** 执行失败重试次数 */
    @ApiModelProperty(value = "执行失败重试次数")
    private int executorFailRetryCount;
    /** 状态 */
    @ApiModelProperty(value = "调度状态")
    private int triggerStatus;

    public static DisplayDataSyncTask createBaseFrom(DatasyncInstance baseInfo) {
        DisplayDataSyncTask result = new DisplayDataSyncTask();
        result.setId(baseInfo.getId());
        result.setDatasyncId(baseInfo.getDatasyncId());
        result.setPublishTime(baseInfo.getPublishTime());
        result.setUpdateTime(baseInfo.getUpdateTime());
        result.setPublishUser(baseInfo.getPublishUser());
        result.setInstanceName(baseInfo.getInstanceName());
        result.setInstanceCode(baseInfo.getInstanceCode());
        result.setOrginType(baseInfo.getOrginType());
        result.setOrginDatasourceName(baseInfo.getOrginDatasourceName());
        result.setOrginPluginPath(baseInfo.getOrginPluginPath());
        result.setDestType(baseInfo.getDestType());
        result.setDestDatasourceName(baseInfo.getDestDatasourceName());
        result.setDestPluginPath(baseInfo.getDestPluginPath());
        result.setDispatchId(baseInfo.getDispatchId());
        result.setJobGroup(null);
        result.setRouteStrategy(null);
        result.setBlockStrategy(null);
        result.setExecutorTimeout(-1);
        result.setExecutorFailRetryCount(-1);
        result.setTriggerStatus(-1);
        return result;
    }

    public static DisplayDataSyncTask createNewBaseFrom(DatasyncJob baseInfo) {
        DisplayDataSyncTask result = new DisplayDataSyncTask();
        result.setId(baseInfo.getId());
        result.setDatasyncId(baseInfo.getDatasyncId());
        result.setPublishTime(baseInfo.getCreateTime());
        result.setUpdateTime(baseInfo.getUpdateTime());
        result.setPublishUser(baseInfo.getCreateUser());
        result.setInstanceName(baseInfo.getJobName());
//        result.setInstanceCode(baseInfo.getInstanceCode());
        result.setOrginType(baseInfo.getOrginType());
        result.setOrginDatasourceName(baseInfo.getOrginDatasourceName());
        result.setOrginPluginPath(baseInfo.getOrginPluginPath());
        result.setDestType(baseInfo.getDestType());
        result.setDestDatasourceName(baseInfo.getDestDatasourceName());
        result.setDestPluginPath(baseInfo.getDestPluginPath());
        result.setDispatchId(baseInfo.getScheduleId());
        result.setJobGroup(null);
        result.setRouteStrategy(null);
        result.setBlockStrategy(null);
        result.setExecutorTimeout(-1);
        result.setExecutorFailRetryCount(-1);
        result.setTriggerStatus(-1);
        return result;
    }

    public void buildDispatchInfo(SchedulerJob schedulerJob) {
        this.jobGroup = schedulerJob.getJobGroup();
        this.routeStrategy = schedulerJob.getExecutorRouteStrategy();
        this.blockStrategy = schedulerJob.getExecutorBlockStrategy();
        this.executorTimeout = schedulerJob.getExecutorTimeout();
        this.executorFailRetryCount = schedulerJob.getExecutorFailRetryCount();
        this.schedulerExpr = schedulerJob.getExpression();
        this.triggerStatus = schedulerJob.getTriggerStatus();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDatasyncId() {
        return datasyncId;
    }

    public void setDatasyncId(String datasyncId) {
        this.datasyncId = datasyncId;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getPublishUser() {
        return publishUser;
    }

    public void setPublishUser(String publishUser) {
        this.publishUser = publishUser;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public String getInstanceCode() {
        return instanceCode;
    }

    public void setInstanceCode(String instanceCode) {
        this.instanceCode = instanceCode;
    }

    public String getOrginType() {
        return orginType;
    }

    public void setOrginType(String orginType) {
        this.orginType = orginType;
    }

    public String getOrginDatasourceName() {
        return orginDatasourceName;
    }

    public void setOrginDatasourceName(String orginDatasourceName) {
        this.orginDatasourceName = orginDatasourceName;
    }

    public String getOrginPluginPath() {
        return orginPluginPath;
    }

    public void setOrginPluginPath(String orginPluginPath) {
        this.orginPluginPath = orginPluginPath;
    }

    public String getDestType() {
        return destType;
    }

    public void setDestType(String destType) {
        this.destType = destType;
    }

    public String getDestDatasourceName() {
        return destDatasourceName;
    }

    public void setDestDatasourceName(String destDatasourceName) {
        this.destDatasourceName = destDatasourceName;
    }

    public String getDestPluginPath() {
        return destPluginPath;
    }

    public void setDestPluginPath(String destPluginPath) {
        this.destPluginPath = destPluginPath;
    }

    public String getDispatchId() {
        return dispatchId;
    }

    public void setDispatchId(String dispatchId) {
        this.dispatchId = dispatchId;
    }

    public String getJobGroup() {
        return jobGroup;
    }

    public void setJobGroup(String jobGroup) {
        this.jobGroup = jobGroup;
    }

    public String getSchedulerExpr() {
        return schedulerExpr;
    }

    public void setSchedulerExpr(String schedulerExpr) {
        this.schedulerExpr = schedulerExpr;
    }

    public String getRouteStrategy() {
        return routeStrategy;
    }

    public void setRouteStrategy(String routeStrategy) {
        this.routeStrategy = routeStrategy;
    }

    public String getBlockStrategy() {
        return blockStrategy;
    }

    public void setBlockStrategy(String blockStrategy) {
        this.blockStrategy = blockStrategy;
    }

    public int getExecutorTimeout() {
        return executorTimeout;
    }

    public void setExecutorTimeout(int executorTimeout) {
        this.executorTimeout = executorTimeout;
    }

    public int getExecutorFailRetryCount() {
        return executorFailRetryCount;
    }

    public void setExecutorFailRetryCount(int executorFailRetryCount) {
        this.executorFailRetryCount = executorFailRetryCount;
    }

    public int getTriggerStatus() {
        return triggerStatus;
    }

    public void setTriggerStatus(int triggerStatus) {
        this.triggerStatus = triggerStatus;
    }
}
