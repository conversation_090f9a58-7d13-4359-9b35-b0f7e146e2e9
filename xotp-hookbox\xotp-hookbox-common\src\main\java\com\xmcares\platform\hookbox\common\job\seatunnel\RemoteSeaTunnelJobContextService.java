/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/23
 */
package com.xmcares.platform.hookbox.common.job.seatunnel;

import com.xmcares.platform.hookbox.common.job.JobContext;
import com.xmcares.platform.hookbox.common.job.context.JobContextImpl;
import com.xmcares.platform.hookbox.common.job.JobContextService;
import com.xmcares.platform.hookbox.common.job.error.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;

/**
 * SeaTunnelJobServiceHttpImpl 是通过 HTTP API 调用 SeaTunnel 服务的实现方式，
 * 用于将任务提交给远程运行中的 SeaTunnel Server（如 seatunnel-engine cluster）。
 *
 * <AUTHOR>
 * @since 2.1.0
 */
public class RemoteSeaTunnelJobContextService implements JobContextService {

    private static final Logger logger = LoggerFactory.getLogger(RemoteSeaTunnelJobContextService.class);

    // REST 客户端实例
    private RestTemplate restTemplate = new RestTemplate();

    // Seatunnel v2 REST API 网关地址
    private String seaTunnelApiEndpoint = "http://***********:15802";


    @SuppressWarnings("rawtypes")
    @Override
    public synchronized void startJob(JobContext jobContext) {
        String jobId = jobContext.getJobId();
        String jobName = jobContext.getJobName();
        String jobInstanceId = jobContext.getJobInstanceId();

        try {
            // 构建 multipart 请求体
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // 注意 seatunnel的作业ID的固定命名为jobId（不同于JobContext的jobId），值为jobInstanceId
            // 附带 jobId 和 jobName 控制幂等性
            body.add("jobId", jobInstanceId);
            body.add("jobName", jobName);

            // SeaTunnel 官方接口接受文件形式的 config
            // 将 jobContent 写为临时文件，并作为 FileSystemResource 上传
            File tempConfig = Files.createTempFile("seatunnel-", ".json").toFile();
            Files.write(tempConfig.toPath(), jobContext.getJobOptions().getBytes(StandardCharsets.UTF_8));
            body.add("config_file", new FileSystemResource(tempConfig));

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            String url = seaTunnelApiEndpoint + "/submit-job/upload";
            ResponseEntity<Map> resp = restTemplate.postForEntity(url, requestEntity, Map.class);

            // 解析响应 JSON, 包括 jobId/jobName
            Map respBody = resp.getBody();
            if (HttpStatus.OK.equals(resp.getStatusCode())
                    && respBody != null
                    && respBody.get("jobId") != null) {
                String returnedId = respBody.get("jobId").toString();
                logger.info("成功上传并提交 SeaTunnel 任务 [{} (ID:{})] → 实际实例ID={}", jobName, jobId, returnedId);
            } else {
                throw new JobExecutionException(jobId, jobInstanceId, "upload 提交失败，响应: " + respBody);
            }
        } catch (Exception e) {
            logger.error("submit-job/upload 失败 [{} (ID:{})]: {}", jobName, jobId, e.getMessage(), e);
            throw new JobExecutionException(jobId, jobInstanceId, "submit-job/upload 调用失败", e);
        }
    }

    @SuppressWarnings("rawtypes")
    @Override
    public synchronized void stopJob(JobContext jobContext) {
        String jobId = jobContext.getJobId();
        String jobName = jobContext.getJobName();
        String jobInstanceId = jobContext.getJobInstanceId();
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("jobId", jobContext.getJobInstanceId());
            //requestBody.put("isStopWithSavePoint", true);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            String url = seaTunnelApiEndpoint + "/stop-job";
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, Map.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                logger.info("成功停止 SeaTunnel 任务 [{}]，实例 ID={}。", jobId, jobInstanceId);
            } else {
                logger.warn("停止任务 [{}] 失败，响应码={}: {}", jobId, response.getStatusCode(), response.getBody());
            }
        } catch (Exception e) {
            logger.error("停止 SeaTunnel 任务 [{}] 失败: {}", jobId, e.getMessage(), e);
        }
    }

    /**
     * 获取指定 SeaTunnel 任务实例的执行日志。（需要根据文档配置 seatunnel 的log4j.properties后才能获取到对应的日志）
     * @param jobContext 任务上下文，包含任务实例ID
     *
     * @docs https://seatunnel.incubator.apache.org/zh-CN/docs/2.3.11/seatunnel-engine/logging
     * @return 包含执行日志内容的字符串，如果获取失败则返回错误信息。
     */
    public String getJobExecutionLog(JobContext jobContext) {
        String jobInstanceId = jobContext.getJobInstanceId();
//        String jobInstanceId = "62474384575160320";
        if (jobInstanceId == null || jobInstanceId.trim().isEmpty()) {
            logger.error("无法获取日志，因为 JobContext 中的 jobInstanceId 为空。");
            return "错误：任务实例ID为空。";
        }

        String url = String.format("%s/logs/job-%s.log", this.seaTunnelApiEndpoint, jobInstanceId);
        logger.info("开始获取 SeaTunnel 任务实例 [{}] 的执行日志，URL: {}", jobInstanceId, url);

        try {
            // 请求日志的原始字节数据，以避免编码问题
            ResponseEntity<byte[]> response = restTemplate.getForEntity(url, byte[].class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                // 虽然响应头可能是 iso-8859-1，但日志文件通常是 UTF-8 编码，我们强制使用 UTF-8 解析
                String logs = new String(response.getBody(), StandardCharsets.UTF_8);
                logger.info("成功获取到任务实例 [{}] 的执行日志。", jobInstanceId);
                return logs;
            } else {
                String errorMsg = String.format("获取任务实例 [%s] 日志失败，响应码=%s", jobInstanceId, response.getStatusCode());
                logger.warn(errorMsg);
                return errorMsg;
            }
        } catch (Exception e) {
            String errorMsg = String.format("调用API获取任务实例 [%s] 日志时发生异常: %s", jobInstanceId, e.getMessage());
            logger.error(errorMsg, e);
            return errorMsg;
        }
    }

    /**
     * 调用 SeaTunnel API 获取指定任务实例的详细信息，包括状态和指标。
     *
     * @param jobContext 任务上下文，必须包含有效的 jobInstanceId。
     * @return SeaTunnelJobInfo 对象，包含任务的详细信息。如果获取失败或任务不存在，则返回 null。
     */
    public SeaTunnelJobInfo getJobLogInfo(JobContext jobContext) {
        String jobInstanceId = jobContext.getJobInstanceId();
        if (jobInstanceId == null || jobInstanceId.trim().isEmpty()) {
            logger.error("无法获取作业信息，因为 JobContext 中的 jobInstanceId 为空。");
            return null;
        }

        String url = String.format("%s/job-info/%s", this.seaTunnelApiEndpoint, jobInstanceId);
        logger.info("开始获取 SeaTunnel 任务实例 [{}] 的详细信息，URL: {}", jobInstanceId, url);

        try {
            ResponseEntity<SeaTunnelJobInfo> response = restTemplate.getForEntity(url, SeaTunnelJobInfo.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                logger.info("成功获取到任务实例 [{}] 的详细信息，状态为: {}", jobInstanceId, response.getBody().getJobStatus());
                return response.getBody();
            } else {
                logger.warn("获取任务实例 [{}] 信息失败，响应码={}，响应体为空或无效。", jobInstanceId, response.getStatusCode());
                return null;
            }
        } catch (HttpClientErrorException.NotFound e) {
            // 404 Not Found 是一个常见且预期的错误，表示任务ID不存在或已从SeaTunnel中清除
            logger.warn("任务实例 [{}] 在 SeaTunnel 中未找到 (404 Not Found)。可能任务已完成并被清理。", jobInstanceId);
            return null;
        } catch (Exception e) {
            String errorMsg = String.format("调用API获取任务实例 [%s] 信息时发生异常: %s", jobInstanceId, e.getMessage());
            logger.error(errorMsg, e);
            return null;
        }
    }

    public void setRestTemplate(RestTemplate restTemplate) {
        if (restTemplate == null) {
            throw new IllegalArgumentException("restTemplate 不能为空");
        }
        this.restTemplate = restTemplate;
    }

    public void setSeaTunnelApiEndpoint(String seaTunnelApiEndpoint) {
        if (seaTunnelApiEndpoint == null || seaTunnelApiEndpoint.trim().isEmpty()) {
            throw new IllegalArgumentException("seaTunnelApiEndpoint 不能为空");
        }
        this.seaTunnelApiEndpoint = seaTunnelApiEndpoint;
    }


    public static void main(String[] args) {
        RemoteSeaTunnelJobContextService service = new RemoteSeaTunnelJobContextService();
        JobContextImpl jobContext = new JobContextImpl("123456", null, "my-test-job");
        jobContext.setJobOptions("{\n" +
                "\t\"env\": {\n" +
                "\t\t\"job.mode\": \"batch\",\n" +
                "\t\t\"variables\": {\n" +
                "\t\t\t\"table_1\": \"table_a\",\n" +
                "\t\t\t\"mysql_table_name\": \"dynamic_users_20250628\",\n" +
                "\t\t\t\"start_timestamp\": \"1718330000000\"\n" +
                "\t\t}\n" +
                "\t},\n" +
                "\t\"source\": [{\n" +
                "\t\t\t\"plugin_name\": \"FakeSource\",\n" +
                "\t\t\t\"plugin_output\": \"fake\",\n" +
                "\t\t\t\"row.num\": 100,\n" +
                "\t\t\t\"schema\": {\n" +
                "\t\t\t\t\"fields\": {\n" +
                "\t\t\t\t\t\"name\": \"string\",\n" +
                "\t\t\t\t\t\"age\": \"int\",\n" +
                "\t\t\t\t\t\"card\": \"int\"\n" +
                "\t\t\t\t}\n" +
                "\t\t\t}\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"transform\": [],\n" +
                "\t\"sink\": [{\n" +
                "\t\t\t\"plugin_name\": \"Console\",\n" +
                "\t\t\t\"plugin_input\": [\"fake\"]\n" +
                "\t\t}\n" +
                "\t]\n" +
                "}");


        service.startJob(jobContext);
    }

}
