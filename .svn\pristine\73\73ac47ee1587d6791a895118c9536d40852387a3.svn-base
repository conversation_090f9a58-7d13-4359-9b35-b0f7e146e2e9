/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/3/2
 */
package com.xmcares.platform.admin.integrator.datasync.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xmcares.framework.commons.context.UserContext;
import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.platform.admin.common.datasource.mq.MqType;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.util.CommonConstants;
import com.xmcares.platform.admin.common.vo.ReturnT;
import com.xmcares.platform.admin.integrator.IntegratorProperties;
import com.xmcares.platform.admin.integrator.common.error.UnknownException;
import com.xmcares.platform.admin.integrator.common.util.ConstantUtils;
import com.xmcares.platform.admin.integrator.datasync.dto.DatasyncDto;
import com.xmcares.platform.admin.integrator.datasync.dto.JobLogPageListDto;
import com.xmcares.platform.admin.integrator.datasync.model.Datasync;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncInstance;
import com.xmcares.platform.admin.integrator.datasync.model.SchedulerJob;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.XxljobClient;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobGroup;
import com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model.XxlJobInfo;
import com.xmcares.platform.admin.integrator.datasync.vo.DataxTempVo;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceRepository;
import com.xxl.job.core.biz.model.LogResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 基于seatunnel 日志查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public class SeatunnelSchedulerRepository {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    public List<String> getLogList() {
        // todo 从存储或系统中获取日志列表
        return new ArrayList<>();
    }

    public String getLogFileContent(String logFileName) {
        // todo 读取指定文件名的日志内容
        return "";
    }
}
