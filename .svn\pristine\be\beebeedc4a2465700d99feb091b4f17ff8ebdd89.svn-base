import React, {PureComponent} from 'react';
import {connect} from 'dva';
import {Form} from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
    Button,
    Card,
    Checkbox,
    Col,
    Divider,
    Input,
    Modal,
    Row,
    Spin,
    Select,
    Tooltip,
    Tree,
    Upload,
    Tag,
    Space,
    DatePicker,
    Typography,

} from 'antd';
import CommonTable from '@/components/CommonTable/index';
import PageHeaderWrapper from '@/components/PageHeaderWrapper/index';
import styles from './Log.less';
import * as utils from '@/utils/utils';
import moment from 'moment';
import ClearLogModal from "@/pages/modules/Scheduler/Log/components/ClearLogModal";
import router from 'umi/router';
import {killJob} from '@/services/Scheduler/Log/Log';
import {messageModal} from "@/utils/messageModal";



let markPage = 1, pageSize = 10;
let needFirst = false; //首次渲染
const confirm = Modal.confirm;
const modelsName = 'log';
const FormItem = Form.Item;
const {RangePicker} = DatePicker;
const {Paragraph} = Typography;

const executeStatus = [
    {code: '-1', name: "全部"},
    {code: '1', name: "成功"},
    {code: '2', name: "失败"},
    {code: '3', name: "进行中"},
];


@connect(({log, tenantManager, deptManager, role, userRole, menu, loading}) => ({
    log,
    tenantManager,
    deptManager,
    role,
    userRole,
    menu,
    loading: loading.models.log,
}))
@Form.create()




export default class Log extends PureComponent {

    state = {
        jobInfoId: '',
        instanceId: "0",
        "jobGroup": 2,
        selectedRows: undefined,
        jobGroups: [],
        jobTasks: [{
            id: 0,
            jobDesc: '全部'
        }],
        defaultValue: [],
        modalVisible: false,
        modalTitle: '',
        selectedRowKeys: undefined
    };

    queryJobGroup = () => {
        const {dispatch, location, form} = this.props;
        const {jobGroup, instanceName} = location.query;
        dispatch({
            type: `${modelsName}/jobGroups`,
            callback: (body) => {
                this.setState({
                    jobGroups: [{
                        id: 0,
                        title: '全部'
                    }, ...body],
                });
                if (jobGroup && needFirst) {
                    form.setFieldsValue({
                        jobGroup: jobGroup
                    });
                    form.setFieldsValue({
                        jobName: instanceName
                    });
                    this.reloadData();
                    needFirst = false;
                    //this.handleJobGroupChange(jobGroup)
                }
            },
        });
    };

    reloadData = () => {
        const {form, dispatch} = this.props;
        form.validateFields((err, values) => {
            var ranTimer;
            for (let key in values) {
                if (values[key] === '') {
                    delete values[key]
                } else if (key === 'filterTime') {
                    ranTimer = values[key];
                    delete values[key];
                }
            }
            if (!err) {
                const params = {
                    page: markPage,
                    rows: pageSize,
                    filterTime: ranTimer ? ranTimer[0].format('YYYY-MM-DD HH:mm:ss') + ' - ' + ranTimer[1].format('YYYY-MM-DD HH:mm:ss') : '',
                    ...values,
                };
                dispatch({
                    type: `${modelsName}/fetch`,
                    payload: params,
                });
            }
        });
    }

    /**
     * CommonTable 分页 排序 触发事件
     * @param pagination 分页
     * @param filtersArg
     * @param sorter 排序
     */
    handleCommonTableChange = (pagination, filtersArg, sorter) => {
        markPage = pagination.current;
        pageSize = pagination.pageSize;
        this.reloadData();
    };

    handleJobGroupChange = value => {
        const {dispatch, location, form} = this.props;
        const {jobGroup} = this.state;
        const {dispatchId} = location.query;
        dispatch({
            type: `${modelsName}/getJobsByGroup`,
            payload: {
                jobGroup: jobGroup
            },
            callback: (body) => {
                if (dispatchId && needFirst) {
                    form.setFieldsValue({
                        jobId: dispatchId
                    });
                    this.reloadData();
                    needFirst = false;
                }
                this.setState({
                    jobTasks: [{
                        id: 0,
                        jobDesc: '全部',
                    }, ...body],
                });
            }
        });
    };

    /**
     * 查询
     */
    handleSearch = (e) => {
        if (e) e.preventDefault();
        markPage = 1;
        this.reloadData();
        this.setState({
            selectedRows: undefined,
            selectedRowKeys: undefined,
        });
    };

    /**
     * 重置
     */
    handleFormReset = () => {
        const data = {
            topThis: this,
        };
        utils.reset(data);
    };

    /**
     * 打开日志清理界面
     */
    openClearLogModel = () => {

        const {jobGroups, jobTasks} = this.state;
        const {form} = this.props;

        form.validateFields((err, values) => {

            console.log(jobGroups, jobTasks, values, 'aklsdjflaksldflasklkasjk');

            let jobGroupName = jobGroups.filter((jobGroup) => {
                return jobGroup.id + "" === values["jobGroup"]
            })[0].title;

            let jobDesc = jobTasks.filter((jobTask) => {
                return jobTask.id + "" === values["jobId"]
            })[0].jobDesc
            this.formRef.openModal({
                jobGroup: values["jobGroup"],
                jobGroupName: jobGroupName,
                jobId: values["jobId"],
                jobDesc: jobDesc,
            }, modelsName);
        });


    }

    /**
     * 日誌清理提交
     * @param prop
     */
    submitForm = (prop) => {
        const {dispatch} = this.props;
        dispatch({
            type: `${modelsName}/clearLog`,
            payload: prop,
        });
    }

    /**
     * 用户列表(第一次渲染后调用)
     */
    componentDidMount() {
        this.queryJobGroup();
        this.handleJobGroupChange();
        const {location} = this.props;
        const {instanceName, jobGroup, instanceId} = location.query;
        if (instanceName && instanceId) {
            this.setState({
                instanceId
            })
            if (jobGroup) {
                needFirst = true;
                return;
            }
        }
        markPage = 1;
        this.reloadData();
    }

    /**
     * 查询框
     */
    renderForm() {
        const {form} = this.props;
        const {instanceId} = this.state;
        const {getFieldDecorator} = this.props.form;
        return (
            <Form onSubmit={this.handleSearch}>
                <Row gutter={{md: 4, lg: 12, xl: 24}}>

                    <Col span={0}>
                        <FormItem label="执行器：">
                            {getFieldDecorator('jobGroup', {
                                initialValue: '2'
                            })(<Select style={{width: '100%'}} allowClear={false} onChange={this.handleJobGroupChange}>
                                {this.state.jobGroups && this.state.jobGroups.map(item => (
                                    <Select.Option key={item.id}>{item.title}</Select.Option>
                                ))}
                            </Select>)}
                        </FormItem>
                    </Col>

                    <Col xl={5} md={8} xs={24}>
                        <FormItem label="任务：">
                            {getFieldDecorator('jobId', {
                                initialValue: instanceId
                            })(<Select showSearch allowClear={false} style={{width: "100%"}}>
                                {this.state.jobTasks && this.state.jobTasks.map(item => (
                                    <Select.Option key={item.id}>
                                        <Tooltip title={item.jobDesc}>
                                            {item.jobDesc && item.jobDesc.length > 16 ? item.jobDesc.slice(0, 20) + '...' : item.jobDesc}
                                        </Tooltip>

                                    </Select.Option>
                                ))}
                            </Select>)}
                        </FormItem>
                        {/*<FormItem label="任务名称">*/}
                        {/*  {getFieldDecorator('jobName', {*/}
                        {/*    rules: [*/}
                        {/*      {max: 200, message: '最多可输入200字'},*/}
                        {/*    ],*/}
                        {/*  })(<Input placeholder="请输入任务名称" onBlur={utils.valToTrim.bind(this, 'jobName', form)}*/}
                        {/*            allowClear={true}/>)}*/}
                        {/*</FormItem>*/}
                    </Col>

                    <Col xl={4} md={8} xs={24}>
                        <FormItem label="执行结果：">
                            {getFieldDecorator('logStatus', {
                                initialValue: '-1'
                            })(<Select style={{width: '100%'}} allowClear={false}>
                                {executeStatus && executeStatus.map(item => (
                                    <Select.Option key={item.code}>{item.name}</Select.Option>
                                ))}
                            </Select>)}
                        </FormItem>
                    </Col>

                    <Col xl={8} md={12} xs={24}>
                        <FormItem label="调度时间：">
                            {getFieldDecorator('filterTime', {
                                initialValue: [moment().startOf('day'), moment().endOf('day')],
                                rules: [],
                            })(<RangePicker showTime allowClear={true} format={'YYYY/MM/DD HH:mm:ss'}
                                            style={{width: '100%'}}
                                            ranges={{
                                                "最近一小时": [moment().subtract(1, 'hours'), moment()],
                                                "今天": [moment().startOf('day'), moment().endOf('day')],
                                                '昨天': [moment(moment().subtract(1, 'day')).startOf('day'), moment(moment().subtract(1, 'day')).endOf('day')],
                                                '本月': [moment().startOf('month'), moment().endOf('month')],
                                                '上个月': [moment(moment().subtract(1, 'month')).startOf('month'), moment(moment().subtract(1, 'month')).endOf('month')],
                                                '最近一周': [moment().subtract(1, 'week'), moment()],
                                                '最近一个月': [moment().subtract(1, 'month'), moment()],
                                            }}
                            />)}
                        </FormItem>
                    </Col>

                    <Col xl={4} md={12} xs={24} style={{paddingTop: '4px'}}>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        <Button style={{marginLeft: 8}} type="primary" onClick={this.handleFormReset}>重置</Button>
                        <Button style={{marginLeft: 8}} type="primary"
                                onClick={this.openClearLogModel}>清理日志</Button>
                    </Col>
                </Row>
            </Form>
        );
    }

    // 查看详情跳转
    handleDetails = (record) => {
        router.push({
            pathname: '/log/logDetails',
            query: {
                logId: record?.id,
                triggerTime: record?.triggerTime,
                executorAddress: record?.executorAddress
            },
        });
    }

    // 停止任务
    stopTask = async (id) => {
        const res = await killJob({id: id});
        if (res) {
            messageModal('success', '停止成功');
            this.handleSearch();
        } else {
            messageModal('error', '停止失败');
        }
    }

    render() {
        const columns = [
            {
                title: '任务ID',
                dataIndex: 'jobId',
                width: '4%',
                render: (text, obj) => {
                    let context = (
                        <div>
                            <p className={styles.logTaskColumnP}>执行器地址：" {obj.executorAddress}</p>
                            <p className={styles.logTaskColumnP}>执行器名称：{obj.executorHandler}</p>
                            <p className={styles.logTaskColumnP}>执行参数：{obj.executorParam}</p>
                        </div>
                    )
                    return (
                        <Button type="link" onClick={() => {
                            Modal.info({
                                title: '任务信息',
                                content: context,
                                style: {top: 20},
                                okButtonProps: {style: {display: 'none'}},
                                maskClosable: true,
                                mask: true,
                            });
                        }}>{text}</Button>
                    );
                },
            },
            {
                title: '任务名称',
                dataIndex: 'jobName',
                width: '12%',
                render: (text) => {
                    return (
                        <Tooltip title={text}>
                            <div className={styles.resultColumnsDiv}>{text}</div>
                        </Tooltip>
                    );
                },
            },
            {
                title: '开始时间',
                dataIndex: 'triggerTime',
                width: '10%',
                render: (text) => {
                    return (
                        <Tooltip title={text}>
                            <div className={styles.resultColumnsDiv}>{text}</div>
                        </Tooltip>
                    );
                },
            },
            {
                title: '调度结果',
                dataIndex: 'triggerCode',
                width: '8%',
                render: (text) => {
                    var color = 'geekblue';
                    var disableText = '未知';
                    if (text > 0) {
                        if (text === 200) {
                            color = 'green';
                            disableText = '成功';
                        } else {
                            color = 'volcano';
                            disableText = '失败';
                        }
                    }
                    return (
                        <Tag color={color}>
                            {disableText}
                        </Tag>
                    );
                },
            },
            {
                title: '调度信息',
                dataIndex: 'triggerMsg',
                width: '8%',
                render: (text) => {
                    if (!text) {
                        return '无'
                    }
                    return (
                        <Button type="link" onClick={() => {
                            Modal.info({
                                title: '调度信息',
                                content: (<div dangerouslySetInnerHTML={{__html: text}}/>),
                                style: {top: 20},
                                okButtonProps: {style: {display: 'none'}},
                                maskClosable: true,
                                mask: true,
                            });
                        }}>查看</Button>
                    );
                },
            },
            {
                title: '执行时间',
                dataIndex: 'handleTime',
                width: '10%',
                render: (text) => {
                    return (
                        <Tooltip title={text}>
                            <div className={styles.resultColumnsDiv}>{text}</div>
                        </Tooltip>
                    );
                },
            },
            {
                title: '执行结果',
                dataIndex: 'handleCode',
                width: '8%',
                render: (text) => {
                    var color = 'geekblue';
                    var disableText = '未知';
                    if (text >= 0) {
                        if (text === 200) {
                            color = 'green';
                            disableText = '成功';
                        } else if (text === 0) {
                            color = 'green';
                            disableText = '运行中';
                        } else {
                            color = 'volcano';
                            disableText = '失败';
                        }
                    }
                    return (
                        <Tag color={color}>
                            {disableText}
                        </Tag>
                    );
                },
            },
            // {
            //     title: '启动时间',
            //     dataIndex: 'scheduleTime',
            //     width: '8%',
            //     render: (text) => {
            //         return (
            //             <Tooltip title={text}>
            //                 <div className={styles.resultColumnsDiv}>{text}</div>
            //             </Tooltip>
            //         );
            //     },
            // },
            {
                title: '结束时间',
                dataIndex: 'finishTime',
                width: '8%',
                render: (text) => {
                    return (
                        <Tooltip title={text}>
                            <div className={styles.resultColumnsDiv}>{text}</div>
                        </Tooltip>
                    );
                },
            },
            {
                title: '写入数量',
                dataIndex: 'writedCount',
                width: '8%',
                render: (text) => {
                    return (
                        <Tooltip title={text}>
                            <div className={styles.resultColumnsDiv}>{text}</div>
                        </Tooltip>
                    );
                },
            },
            {
                title: '状态',
                dataIndex: 'status',
                width: '8%',
                render: (status) => {
                    // 状态码到中文状态信息的映射
                    const statusInfo = {
                        0: { text: '未启动', color: '#d9d9d9' },
                        1: { text: '运行中', color: '#1890ff'},
                        2: { text: '已完成', color: '#52c41a' },
                        3: { text: '失败', color: '#f5222d'},
                        4: { text: '已取消/已停止', color: '#faad14' }
                    };

                    const info = statusInfo[status] || { text: `未知状态`, color: '#d9d9d9', icon: null };

                    return (
                        <Tooltip title={info.text}>
                            <div className={styles.resultColumnsDiv} style={{ color: info.color }}>
                                {info.text}
                            </div>
                        </Tooltip>
                    );
                }
            },
            {
                title: '执行信息',
                dataIndex: 'handleMsg',
                width: '8%',
                fixed: 'right',
                render: (text) => {
                    if (!text) {
                        return '无'
                    }
                    return (
                        <Button type="link" onClick={() => {
                            Modal.info({
                                title: '执行信息',
                                content: (<div dangerouslySetInnerHTML={{__html: text}}/>),
                                style: {top: 20},
                                okButtonProps: {style: {display: 'none'}},
                                maskClosable: true,
                                mask: true,
                            });
                        }}>查看</Button>
                    );
                },
            },
            {
                title: '操作',
                key: 'action',
                width: '200px',
                fixed: 'right',
                render: (text, record) => {
                    return (<Space size="middle">
                        {!record.handleCode && record.handleCode === 0 ?
                            <a onClick={() => this.stopTask(record.id)}>停止任务</a> : ""}
                        <a href={`/log/logDetails?logId=${record?.id}&triggerTime=${record?.triggerTime}&executorAddress=${record?.executorAddress}`}
                           target="_blank" rel="noopener noreferrer">查看日志</a>
                    </Space>)
                },
            },
        ];
        const {
            selectedRows,
        } = this.state;
        const {log: {data, loading, logList}, menu: {currentBtnArray}} = this.props;

        const methods = {
            submitForm: this.submitForm,
        }

        return (
            <PageHeaderWrapper>
                <Card bordered={false}>
                    <div>
                        <div className={styles.tableListForm}>{this.renderForm()}</div>
                        <CommonTable
                            rowSelectionShow={false}
                            loading={loading}
                            data={data}
                            columns={columns}
                            current={markPage}
                            scrollX={'120%'}
                            onChange={this.handleCommonTableChange}
                        />
                    </div>
                </Card>
                <ClearLogModal wrappedComponentRef={(inst) => this.formRef = inst} {...methods}/>
            </PageHeaderWrapper>
        );
    }
}
