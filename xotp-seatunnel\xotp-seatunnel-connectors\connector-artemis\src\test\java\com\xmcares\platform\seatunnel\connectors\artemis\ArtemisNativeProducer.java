/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/26
 */
package com.xmcares.platform.seatunnel.connectors.artemis;

import org.apache.activemq.artemis.api.core.ActiveMQException;
import org.apache.activemq.artemis.api.core.SimpleString;
import org.apache.activemq.artemis.api.core.client.*;

import java.nio.charset.StandardCharsets;
import java.util.Scanner;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public class ArtemisNativeProducer {
    // Artemis Broker 的连接地址
    private static final String BROKER_URL = "tcp://localhost:61616";
    // 要发送数据的主题名称（在原生客户端中，这对应于一个 Address）
    private static final String TOPIC_ADDRESS_NAME = "my_topic"; // 确保与 SeaTunnel 配置中的 topic_name 一致
    private static final String USERNAME = "test"; // <-- 替换为你的 Artemis 用户名
    private static final String PASSWORD = "test";
    public static void main(String[] args) {
        ServerLocator locator = null;
        ClientSessionFactory sessionFactory = null;
        ClientSession session = null;
        ClientProducer producer = null;
        Scanner scanner = new Scanner(System.in);

        try {
            // 1. 创建 ServerLocator
            locator = ActiveMQClient.createServerLocator(BROKER_URL);

            locator.setConnectionTTL(60000);
            locator.setCallTimeout(30000);
            locator.setRetryInterval(1000);
            locator.setReconnectAttempts(-1);

            // 2. 创建客户端会话工厂
            sessionFactory = locator.createSessionFactory();

            // 3. 创建客户端会话
            // createSession(boolean autoCommitSends, boolean autoCommitAcks)
            // autoCommitSends: true 意味着消息发送后自动提交
            // autoCommitAcks: true 意味着消息确认后自动提交
            session = sessionFactory.createSession(USERNAME, PASSWORD, true, true,
                    true, true, 1); // <-- 在这里传入用户名和密码

            // 4. 创建消息生产者，消息将发送到 TOPIC_ADDRESS_NAME 地址
            producer = session.createProducer(new SimpleString(TOPIC_ADDRESS_NAME));

            // 启动会话
            session.start();

            System.out.println("Artemis Native Topic Producer (v2.19.1 FIXED) started. Sending messages to address: " + TOPIC_ADDRESS_NAME);
            System.out.println("Enter messages (or type 'exit' to quit):");

            String input;
            int messageCount = 0;
            while (true) {
                System.out.print("Message " + (++messageCount) + " (JSON recommended): ");
                input = scanner.nextLine();

                if ("exit".equalsIgnoreCase(input)) {
                    break;
                }

                // 5. 创建原生消息
                // createMessage(boolean durable)
                ClientMessage message = session.createMessage(true); // true 表示消息持久化

                // ****** 核心修改 ******
                // 将字符串的字节写入消息体缓冲区
                message.getBodyBuffer().writeBytes(input.getBytes(StandardCharsets.UTF_8));
                // ****** 核心修改 ******

                // 可选：添加 ContentType 属性，帮助消费者解析
                // SimpleString 的使用在 2.19.1 仍然是标准做法，但新版本可能直接接受 String
                message.putStringProperty(new SimpleString("ContentType"), new SimpleString("application/json"));

                // 6. 发送消息
                producer.send(message);
                System.out.println("  Sent: " + input);

                // 简单的延迟
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

        } catch (Exception e) {
            System.err.println("ArtemisMQ Exception occurred: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 7. 关闭原生客户端资源，确保资源被正确释放
            try {
                if (producer != null) producer.close();
                if (session != null) session.close();
                if (sessionFactory != null) sessionFactory.close();
                if (locator != null) locator.close();
                if (scanner != null) scanner.close();
            } catch (ActiveMQException e) {
                System.err.println("Error closing ArtemisMQ resources: " + e.getMessage());
            }
            System.out.println("Artemis Native Topic Producer (v2.19.1 FIXED) stopped.");
        }
    }
}
