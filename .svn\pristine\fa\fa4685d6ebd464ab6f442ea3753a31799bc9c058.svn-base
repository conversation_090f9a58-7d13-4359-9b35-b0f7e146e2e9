package com.xmcares.platform.hookbox.common.job.seatunnel;

/**
 * SeaTunnelJobInfo
 *
 * <AUTHOR>
 * @Descriptions SeaTunnelJobInfo
 * @Date 2025/7/28 11:23
 */

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 用于映射 SeaTunnel /job-info/{jobId} API 响应的 DTO。
 */
@JsonIgnoreProperties(ignoreUnknown = true) // 忽略未知的JSON字段，增加兼容性
public class SeaTunnelJobInfo {

    @JsonProperty("jobId")
    private String jobId;

    @JsonProperty("jobName")
    private String jobName;

    @JsonProperty("jobStatus")
    private String jobStatus;

    @JsonProperty("errorMsg")
    private String errorMsg;

    @JsonProperty("finishTime")
    private String finishTime;

    @JsonProperty("metrics")
    private Metrics metrics;

    // Getters and Setters ...

    public String getJobId() { return jobId; }
    public void setJobId(String jobId) { this.jobId = jobId; }
    public String getJobName() { return jobName; }
    public void setJobName(String jobName) { this.jobName = jobName; }
    public String getJobStatus() { return jobStatus; }
    public void setJobStatus(String jobStatus) { this.jobStatus = jobStatus; }
    public String getErrorMsg() { return errorMsg; }
    public void setErrorMsg(String errorMsg) { this.errorMsg = errorMsg; }
    public String getFinishTime() { return finishTime; }
    public void setFinishTime(String finishTime) { this.finishTime = finishTime; }
    public Metrics getMetrics() { return metrics; }
    public void setMetrics(Metrics metrics) { this.metrics = metrics; }

    /**
     * 内部类，用于映射 metrics 对象。
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Metrics {
        @JsonProperty("SinkWriteCount")
        private Integer sinkWriteCount;

        @JsonProperty("SinkWriteQPS")
        private Double sinkWriteQPS;

        @JsonProperty("SourceReceivedCount")
        private Integer sourceReceivedCount;

        @JsonProperty("SourceReceivedQPS")
        private Double sourceReceivedQPS;

        // 可以根据需要添加其他指标字段

        // Getters and Setters ...

        public Integer getSinkWriteCount() { return sinkWriteCount; }
        public void setSinkWriteCount(Integer sinkWriteCount) { this.sinkWriteCount = sinkWriteCount; }
        public Double getSinkWriteQPS() { return sinkWriteQPS; }
        public void setSinkWriteQPS(Double sinkWriteQPS) { this.sinkWriteQPS = sinkWriteQPS; }
        public Integer getSourceReceivedCount() { return sourceReceivedCount; }
        public void setSourceReceivedCount(Integer sourceReceivedCount) { this.sourceReceivedCount = sourceReceivedCount; }
        public Double getSourceReceivedQPS() { return sourceReceivedQPS; }
        public void setSourceReceivedQPS(Double sourceReceivedQPS) { this.sourceReceivedQPS = sourceReceivedQPS; }
    }
}
