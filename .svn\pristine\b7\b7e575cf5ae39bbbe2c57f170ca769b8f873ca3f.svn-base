{
  "env": {
    "job.mode": "${jobMode}",
    "job.name": "${jobName}"
  },
  "source": [
  <#-- 根据 sourcePlugin 变量，动态引入对应的 source 模板 -->
    <#include "${orginDatasource.pluginName}_source.ftl">
  ],
  <#-- 如果字段映射列表存在且不为空，则引入 transform 模板 -->
  <#if fieldMappings?? && fieldMappings?has_content>
    "transform": [
      <#include "field_mapping_transform.ftl">
    ],
  </#if>
  "sink": [
    <#-- 根据 sinkPlugin 变量，动态引入对应的 sink 模板 -->
    <#include "${destDatasource.pluginName}_sink.ftl">
  ]
}
