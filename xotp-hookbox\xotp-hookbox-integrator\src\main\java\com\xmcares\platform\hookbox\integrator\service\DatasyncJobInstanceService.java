/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/7/1
 */
package com.xmcares.platform.hookbox.integrator.service;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.platform.hookbox.common.job.JobConstants;
import com.xmcares.platform.hookbox.integrator.mapper.DatasyncJobInstanceMapper;
import com.xmcares.platform.hookbox.integrator.mapper.DatasyncJobMapper;
import com.xmcares.platform.hookbox.integrator.model.DatasyncJob;
import com.xmcares.platform.hookbox.integrator.model.DatasyncJobInstance;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
@Service
public class DatasyncJobInstanceService extends ServiceImpl<DatasyncJobInstanceMapper, DatasyncJobInstance> {


    /**
     * 获取正在运行的同步的任务实例
     *
     * @return
     */
    public List<DatasyncJobInstance> findRunningInstances() {
        return this.baseMapper.selectAllJobInstancesWithJob(
                new QueryWrapper<DatasyncJobInstance>()
                        .eq("dji.status", JobConstants.STATUS_RUNNING)
        );
    }

    /**
     * 获取正在运行的同步的任务实例
     *
     * @param jobId 任务ID
     * @return 运行中的任务实例
     */
    public List<DatasyncJobInstance> findRunningInstancesByJobId(String jobId) {
        if (StringUtils.isBlank(jobId)) {
            throw new IllegalArgumentException("jobId can not be null");
        }

        return this.baseMapper.selectAllJobInstancesWithJob(
                new QueryWrapper<DatasyncJobInstance>()
                        .eq("dji.status", JobConstants.STATUS_RUNNING)
                        .eq("dji.job_id", jobId)
        );
    }

    public void insertJobInstance(DatasyncJobInstance jobInstance) {
        // TODO: 参数校验
        jobInstance.setCreateTime(DateUtil.date());
        this.save(jobInstance);
    }
}
