package com.xmcares.platform.admin.common.database.metaquery;

import com.xmcares.platform.admin.common.database.DataSourceBasedMetaQuery;
import com.xmcares.platform.admin.common.database.metainfo.ColumnInfo;
import com.xmcares.platform.admin.common.database.metainfo.TableInfo;
import com.xmcares.platform.admin.common.errors.MetadataException;
import com.xmcares.platform.admin.common.jdbc.JdbcUtils;
import org.apache.commons.dbutils.handlers.BeanHandler;
import org.apache.commons.dbutils.handlers.BeanListHandler;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.List;

/**
 * Oracle元数据查询
 * <AUTHOR>
 * @since 2.1.0
 */
public class SqlServerMetaQuery extends DataSourceBasedMetaQuery {
    private static final String SQL_TABLES = "SELECT TABLE_NAME As name,TABLE_CATALOG As comment " +
            "FROM INFORMATION_SCHEMA.TABLES t " +
            "WHERE TABLE_TYPE = 'BASE TABLE' " +
            "AND TABLE_CATALOG = ?";

    private static final String SQL_TABLE_COLUMNS = "SELECT COLUMN_NAME AS name, " +
            "DATA_TYPE AS type, " +
            "CHARACTER_MAXIMUM_LENGTH AS max_length, " +
            "IS_NULLABLE AS is_nullable " +
            "FROM INFORMATION_SCHEMA.COLUMNS " +
            "WHERE  TABLE_CATALOG = ? AND TABLE_NAME = ? ";


    public SqlServerMetaQuery(DataSource dataSource) {
        super(dataSource);
    }

    public SqlServerMetaQuery(DataSource dataSource, String schema) {
        super(dataSource, schema);
    }

    @Override
    public TableInfo getTableInfo(String tableName) {
        try {
            return JdbcUtils.executeQuery(this.dataSource, SQL_TABLES + " AND TABLE_NAME = ?",
                    new BeanHandler<>(TableInfo.class), this.schema, tableName);
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取数据库[%s]表[%s]TableInfo失败", this.schema, tableName), e);
        }
    }

    @Override
    public List<TableInfo> getTableInfos() {
        try {
            return JdbcUtils.executeQuery(this.dataSource, SQL_TABLES, new BeanListHandler<>(TableInfo.class), this.schema);
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取数据库[%s]所有表TableInfo失败", this.schema), e);
        }
    }

    @Override
    public List<ColumnInfo> getColumnInfos(String tableName) {
        try {
            return JdbcUtils.executeQuery(this.dataSource, SQL_TABLE_COLUMNS, new BeanListHandler<>(ColumnInfo.class), this.schema, tableName);
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取数据库[%s]表[%s]列ColumnInfo失败", this.schema, tableName), e);
        }
    }
}
