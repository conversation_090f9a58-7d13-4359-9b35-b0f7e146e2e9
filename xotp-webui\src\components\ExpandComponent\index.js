import React, { Component } from 'react';
import Link from 'umi/link';
import { formatMessage } from 'umi/locale';
import Exception from '../Exception';
import {connect} from "dva";


@connect(({ user }) => ({
  currentUser: user.currentUser,
}))
export default class ExpandComponent extends Component {
  iframeRef = React.createRef();
  iframeUrl="";
  isPost=false;

  getCookie= ()=> {
    const cookieStr = document.cookie;
    const cookies = cookieStr.split('; ');
    for (const item of cookies) {
      const [key, value] = item.split('=');
      if (key === 'JSESSIONID') return decodeURIComponent(value);
    }
    return null;
  }
  postMessage = () => {
    const {currentUser} = this.props;
    if (this.iframeRef && this.iframeRef.current) {
      this.iframeRef.current.onload = () => {
        try {
          if(this.isPost===true){
            return;
          }
          this.isPost=true;
          const sessionId = this.getCookie() || currentUser?.sessionId;
          const message = {
            type: 'LOGIN_SESSION_ID',
            cookieKey: 'JSESSIONID',
            cookieValue: sessionId,
          };
          console.log('postMessage', message, currentUser);
          this.iframeRef.current.contentWindow.postMessage(
            message,
            this.iframeUrl
          );
          this.iframeRef.current.contentWindow.postMessage(
            {
              type: 'LOGIN_USER',
              user: currentUser,
            },
            this.iframeUrl
          );
        } catch (e) {

        }
      };
    }
  }

  componentDidMount() {
    this.postMessage();
  }

  componentWillUnmount() {
  }

  render() {
    const {location, children} = this.props;
    if (location && location.pathname) {
      if (location.pathname == '/xdtv/dataScreen') {
        let xdtvUrl = location.pathname;
        this.iframeUrl = (process?.env?.UMI_APP_XDTV_SERVER ?? '') + location.pathname;
        //this.iframeUrl = '/xdtvui' + location.pathname;

        return (
          <iframe
            ref={this.iframeRef}
            src={this.iframeUrl}
            width="100%"
            height="100%"
            sandbox="allow-scripts allow-same-origin allow-forms"
            style={{ width: '100%', height: '100%', border: '0' }}
            frameBorder="0"/>
        );
      }
    }

    return (
      <Exception
        type="404"
        linkElement={Link}
        desc={formatMessage({id: 'app.exception.description.404'})}
        backText={formatMessage({id: 'app.exception.back'})}
      />
    );
  }
}
