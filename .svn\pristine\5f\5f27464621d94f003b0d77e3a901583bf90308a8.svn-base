/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/28
 */
package com.xmcares.platform.hookbox.common.job;

import java.beans.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public interface JobParams extends JobConstants, Serializable {

    String KEY_JOB_ID = "jobId";
    String KEY_SCHEDULE_ID = "scheduleId";
    String KEY_SCHEDULE_LOG_ID = "scheduleLogId";
    String KEY_SCHEDULE_TIME = "scheduleTime";
    String KEY_TRIGGER_TIME = "triggerTime";

    /**
     * 获取指定路径的参数
     * @param path 路径: /a/b
     * @return String
     */
    String getParam(String path);


    /**
     * 获取指定路径的参数并返回为 Map
     * @param path 路径: /a/b
     * @return Map<String, Object>
     */
    Map<String, Object> getParamForMap(String path);

    /**
     * 获取指定路径的参数并返回为 Integer
     * @param path 路径: /a/b
     * @return Long
     */
    Long getParamForLong(String path);
    /**
     * 获取指定路径的参数并返回为 Boolean
     * @param path 路径: /a/b
     * @return Boolean
     */
    Boolean getParamForBoolean(String path);

    <T> T getParamForClass(String path, Class<T> clazz);

    @Transient
    default String getJobId() {
        return this.getParam(KEY_JOB_ID);
    }
    @Transient
    default Long getScheduleId() {
        return this.getParamForLong(KEY_SCHEDULE_ID);
    }
    @Transient
    default Long getScheduleLogId() {
        return this.getParamForLong(KEY_SCHEDULE_LOG_ID);
    }
    @Transient
    default Date getScheduleTime() {
        return this.getParamForClass(KEY_TRIGGER_TIME, Date.class);
    }

    @Transient
    default Date getTriggerTime() {
        return this.getParamForClass(KEY_TRIGGER_TIME, Date.class);
    }

    String toJsonString();
}
