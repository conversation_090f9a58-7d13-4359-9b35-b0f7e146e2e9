package com.xmcares.platform.admin.integrator.datasync.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.xmcares.framework.commons.util.BeanUtils;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.integrator.common.util.DataxFtlUtils;
import com.xmcares.platform.admin.integrator.common.util.IDataxFtlEntity;
import com.xmcares.platform.admin.integrator.common.util.IntegratorMod;
import com.xmcares.platform.admin.integrator.datasync.model.Datasync;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/19 10:48
 */
public class DatasyncDto extends Datasync implements IDataxFtlEntity,Serializable {

    @ApiModelProperty(value = "作业运行模式: BATCH(批处理), STREAM(流处理)")
    @TableField("job_mode")
    private String jobMode;

    @ApiModelProperty(value = "字段映射JSON字符串")
    @TableField("field_mapping_json")
    private String fieldMappingJson;

    /** 数据来源模式 */
    @ApiModelProperty(hidden = true)
    private IntegratorMod fromMod;
    /** 数据去向模式 */
    @ApiModelProperty(hidden = true)
    private IntegratorMod toMod;
    /** 数据源基础信息Json实体对象 */
    @ApiModelProperty(hidden = true)
    private JSONObject orginBaseJsonEntity;
    /** 数据源进阶信息Json实体对象 */
    @ApiModelProperty(hidden = true)
    private JSONObject orginAdvJsonEntity;
    /** 数据源高级信息Json实体对象 */
    @ApiModelProperty(hidden = true)
    private JSONObject orginHighJsonEntity;
    /** 数据源列Json实体对象 */
    @ApiModelProperty(hidden = true)
    private JSONArray orginColumnJsonEntities;
    /** 目标源基础信息Json实体对象 */
    @ApiModelProperty(hidden = true)
    private JSONObject destBaseJsonEntity;
    /** 目标源进阶信息Json实体对象 */
    @ApiModelProperty(hidden = true)
    private JSONObject destAdvJsonEntity;
    /** 目标源高级信息Json实体对象 */
    @ApiModelProperty(hidden = true)
    private JSONObject destHighJsonEntity;
    /** 目标源列Json实体对象 */
    @ApiModelProperty(hidden = true)
    private JSONArray destColumnJsonEntities;

    public DatasyncDto(Datasync datasync) {
        BeanUtils.copyProperties(datasync, this);
    }

    @Override
    public String getOrginPluginName() {
        return getOrginDatasourceName();
    }

    @Override
    public String getDestPluginName() {
        return getDestDatasourceName();
    }

    @Override
    public IntegratorMod getFromMod() {
        if (fromMod == null) {
            fromMod = IntegratorMod.match(getOrginType(), null);
        }
        return fromMod;
    }

    @Override
    public IntegratorMod getToMod() {
        if (toMod == null) {
            toMod = IntegratorMod.match(getDestType(), null);
        }
        return toMod;
    }

    public JSONObject getOrginBaseJsonEntity() {
        if (orginBaseJsonEntity == null && StringUtils.isNotEmpty(getOrginBaseJson())) {
            try {
                orginBaseJsonEntity = JSON.parseObject(getOrginBaseJson());
            } catch (JSONException e) {
                throw new BusinessException("错误的Json格式， " + e.getMessage());
            }
        }
        return orginBaseJsonEntity;
    }

    public JSONObject getOrginAdvJsonEntity() {
        if (orginAdvJsonEntity == null && StringUtils.isNotEmpty(getOrginAdvJson())) {
            try {
                orginAdvJsonEntity = JSON.parseObject(getOrginAdvJson());
            } catch (JSONException e) {
                throw new BusinessException("错误的Json格式， " + e.getMessage());
            }
        }
        return orginAdvJsonEntity;
    }

    public JSONObject getOrginHighJsonEntity() {
        if (orginHighJsonEntity == null && StringUtils.isNotEmpty(getOrginHighJson())) {
            try {
                orginHighJsonEntity = JSON.parseObject(getOrginHighJson());
            } catch (JSONException e) {
                throw new BusinessException("错误的Json格式， " + e.getMessage());
            }
        }
        return orginHighJsonEntity;
    }

    @Override
    public JSONArray getOrginColumnJsonEntities() {
        if (orginColumnJsonEntities == null && StringUtils.isNotEmpty(getOrginColumnJson())) {
            try {
                orginColumnJsonEntities = JSON.parseArray(getOrginColumnJson());
            } catch (JSONException e) {
                throw new BusinessException("错误的Json格式， " + e.getMessage());
            }
        }
        return orginColumnJsonEntities;
    }

    public JSONObject getDestBaseJsonEntity() {
        if (destBaseJsonEntity == null && StringUtils.isNotEmpty(getDestBaseJson())) {
            try {
                destBaseJsonEntity = JSON.parseObject(getDestBaseJson());
            } catch (JSONException e) {
                throw new BusinessException("错误的Json格式， " + e.getMessage());
            }
        }
        return destBaseJsonEntity;
    }

    public JSONObject getDestAdvJsonEntity() {
        if (destAdvJsonEntity == null && StringUtils.isNotEmpty(getDestAdvJson())) {
            try {
                destAdvJsonEntity = JSON.parseObject(getDestAdvJson());
            } catch (JSONException e) {
                throw new BusinessException("错误的Json格式， " + e.getMessage());
            }
        }
        return destAdvJsonEntity;
    }

    public JSONObject getDestHighJsonEntity() {
        if (destHighJsonEntity == null && StringUtils.isNotEmpty(getDestHighJson())) {
            try {
                destHighJsonEntity = JSON.parseObject(getDestHighJson());
            } catch (JSONException e) {
                throw new BusinessException("错误的Json格式， " + e.getMessage());
            }
        }
        return destHighJsonEntity;
    }

    @Override
    public JSONArray getDestColumnJsonEntities() {
        if (destColumnJsonEntities == null && StringUtils.isNotEmpty(getDestColumnJson())) {
            try {
                destColumnJsonEntities = JSON.parseArray(getDestColumnJson());
            } catch (JSONException e) {
                throw new BusinessException("错误的Json格式， " + e.getMessage());
            }
        }
        return destColumnJsonEntities;
    }

    public String getJobMode() {
        return jobMode;
    }

    public void setJobMode(String jobMode) {
        this.jobMode = jobMode;
    }

    public String getFieldMappingJson() {
        return fieldMappingJson;
    }

    public void setFieldMappingJson(String fieldMappingJson) {
        this.fieldMappingJson = fieldMappingJson;
    }

    @Override
    public Map<String, Object> buildOrginMapInfo() {
        Map<String, Object> result = new HashMap<>();
        result.putAll(getOrginBaseJsonEntity() != null ? getOrginBaseJsonEntity() : new HashMap<>());
        result.putAll(getOrginAdvJsonEntity() != null ? getOrginAdvJsonEntity() : new HashMap<>());
        result.putAll(getOrginHighJsonEntity() != null ? getOrginHighJsonEntity() : new HashMap<>());
        Map<String, Object> mulitMap = DataxFtlUtils.buildMulitConfigs(result);
        if (null != mulitMap && !mulitMap.isEmpty()) {
            result.putAll(mulitMap);
        }
        return result;
    }

    @Override
    public Map<String, Object> buildDestMapInfo() {
        Map<String, Object> result = new HashMap<>();
        result.putAll(getDestBaseJsonEntity() != null ? getDestBaseJsonEntity() : new HashMap<>());
        result.putAll(getDestAdvJsonEntity() != null ? getDestAdvJsonEntity() : new HashMap<>());
        result.putAll(getDestHighJsonEntity() != null ? getDestHighJsonEntity() : new HashMap<>());
        Map<String, Object> mulitMap =  DataxFtlUtils.buildMulitConfigs(result);
        if (null != mulitMap && !mulitMap.isEmpty()) {
            result.putAll(mulitMap);
        }
        return result;
    }


}
