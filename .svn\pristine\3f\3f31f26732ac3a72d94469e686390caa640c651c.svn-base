import React, {PureComponent} from 'react';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import CommonTable from '@/components/CommonTable';
import ModelForm from './ModelForm';
import {connect} from 'dva';
import {DeleteOutlined, EditOutlined, EyeInvisibleOutlined, EyeOutlined, PlusCircleOutlined} from '@ant-design/icons';
import {Form} from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {Button, Card, Col, Select, Input, Modal, Row, Divider} from 'antd';
import styles from './ApplicationManager.less';
import {messageModal} from '@/utils/messageModal';
import * as utils from '@/utils/utils';
import ServiceModel from './ServiceModel';
import {authAppAndService, getAppList, getTopicList} from "@/services/DataService/MessageTopic";
import TopicModel from "@/pages/modules/Sharing/ApplicationManager/TopicModel";
import md5 from "md5";

const confirm = Modal.confirm;
const FormItem = Form.Item;
let markPage = 1,
  pageSize = 10;
const serviceStatus = [{code: 0, text: '禁用'}, {code: 1, text: '正常'}];
const appStatus = [{code: 0, text: '不需要'}, {code: 1, text: '需要'}];

@Form.create()
@connect(({ApplicationManager, menu, MessageTopic}) => ({
  ApplicationManager,
  menu,
  MessageTopic
}))
class ApplicationManager extends PureComponent {
  state = {
    rowData: undefined,
    markStatus: 0, //0代表新增，1代表编辑
    selectedRows: [],
    selectedRowKeys: undefined,
    secretVisibility: {},
  };

  toggleSecretVisibility = (rowKey) => {
    this.setState(prevState => ({
      secretVisibility: {
        ...prevState.secretVisibility,
        [rowKey]: !prevState.secretVisibility[rowKey], // 切换当前行的状态
      },
    }));
  };

  componentDidMount() {
    this.reloadData();
  }

  handleCommonTableChange = (pagination, filtersArg, sorter) => {
    markPage = pagination.current;
    pageSize = pagination.pageSize;
    const {form} = this.props;
    let params;
    form.validateFields((err, values) => {
      for (let key in values) {
        if (values[key] === '') {
          delete values[key];
        }
      }
      if (!err) {
        params = {
          pagination: {
            pageNo: 1,
            pageSize: 10,
          },
          ...values,
        };
      }
    });
    let data = {
      ...params,
      pagination: {
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
      },
    };
    const {dispatch} = this.props;
    dispatch({
      type: `ApplicationManager/fetch`,
      payload: data,
      callback: () => {
        this.setState({
          selectedRows: [],
          selectedRowKeys: undefined,
        });
      },
    });
  };
  reloadTranferData = pagination => {
    const {dispatch} = this.props;
    let pg;
    if (pagination) {
      pg = pagination;
    } else {
      pg = {
        pageNo: 1,
        pageSize: 10,
      };
    }
    dispatch({
      type: 'ApplicationManager/fetchServiceAllList',
      payload: {
        pagination: {
          ...pg,
        },
      },
      callback: allData => {
        this.serviceRef.getData(allData);
      },
    });
  };

  reloadTopicTranferData = pagination => {
    const {dispatch} = this.props;
    let pg;
    if (pagination) {
      pg = pagination;
    } else {
      pg = {
        pageNo: 1,
        pageSize: 10,
      };
    }
    dispatch({
      type: 'MessageTopic/fetchMessageTopics',
      payload: {
        pagination: {
          ...pg,
        },
      },
      callback: allData => {
        this.topicRef.getData(allData);
      },
    });
  };

  reloadData = () => {
    const {form, dispatch} = this.props;
    form.validateFields((err, values) => {
      for (let key in values) {
        if (values[key] === '') {
          delete values[key];
        }
      }
      if (!err) {
        const params = {
          pagination: {
            pageNo: 1,
            pageSize: 10,
          },
          ...values,
        };
        dispatch({
          type: `ApplicationManager/fetch`,
          payload: params,
        });
      }
    });
  };
  handleAdd = () => {
    this.formRef.handleMinModalVisible(true, '新增', 1);
    this.setState({
      rowData: undefined,
      markStatus: 0,
    });
  };
  handleEdit = () => {
    const {selectedRowKeys, selectedRows} = this.state;
    if (!selectedRows || selectedRows.length === 0) {
      messageModal('warning', '请选择一项!');
      return;
    }
    this.formRef.handleMinModalVisible(true, '编辑', 2);
    this.setState({
      rowData: selectedRows[0],
      markStatus: 1,
    });
  };

  //服务授权
  handleService = () => {
    const {selectedRowKeys, selectedRows} = this.state;
    if (!selectedRows || selectedRows.length === 0) {
      messageModal('warning', '请选择一项!');
      return;
    }
    const {dispatch} = this.props;
    dispatch({
      type: 'ApplicationManager/fetchServiceList',
      payload: {id: selectedRows[0].id},
      callback: data => {
        dispatch({
          type: 'ApplicationManager/fetchServiceAllList',
          payload: {
            pagination: {
              pageNo: 1,
              pageSize: 1000,
            },
          },
          callback: allData => {
            this.serviceRef.handleMinModalVisible(true, '服务授权', 1);
            this.serviceRef.getData(allData, data);
            this.setState({
              rowData: selectedRows[0],
              markStatus: 2,
            });
          },
        });
      },
    });
  };

  // 主题授权
  handleTopicService = async () => {
    const {selectedRowKeys, selectedRows} = this.state;
    if (!selectedRows || selectedRows.length === 0) {
      messageModal('warning', '请选择一项!');
      return;
    }
    const {dispatch} = this.props;

    dispatch({
      type: `MessageTopic/fetchMessageTopics`,
      payload: {
        pagination: {
          pageNo: 1,
          pageSize: 1000,
        },
      },
      callback: allData => {
        getTopicList(selectedRows[0].id).then(res => {
          const topicList = res.success === true ? res.data : [];
          this.topicRef.handleMinModalVisible(true, '主题授权', 1);
          this.topicRef.getData(allData, topicList);
          this.setState({
            rowData: selectedRows[0],
            markStatus: 3,
          });
        })
      },
    });
  };

  handleSearch = e => {
    e.preventDefault();
    this.reloadData();
    this.setState({
      selectedRows: [],
      selectedRowKeys: undefined,
    });
  };
  handleReset = e => {
    e.preventDefault();
    const {form} = this.props;
    form.resetFields();
  };

  renderForm() {
    const {form} = this.props;
    const {getFieldDecorator} = form;
    return (
      <Form onSubmit={this.handleSearch} onReset={this.handleReset}>
        <Row gutter={24}>
          <Col span={4}>
            <FormItem label="应用名称">
              {getFieldDecorator('name', {
                initialValue: '',
              })(
                <Input
                  placeholder="请输入应用名称"
                  onBlur={utils.valToTrim.bind(this, 'name', form)}
                  allowClear={true}
                />
              )}
            </FormItem>
          </Col>
          <Col span={4}>
            <FormItem label="应用KEY">
              {getFieldDecorator('key', {
                initialValue: '',
              })(
                <Input
                  placeholder="请输入应用KEY"
                  onBlur={utils.valToTrim.bind(this, 'key', form)}
                  allowClear={true}
                />
              )}
            </FormItem>
          </Col>

          <Col span={4}>
            <FormItem label="状态">
              {form.getFieldDecorator('status', {
                initialValue: '',
              })(
                <Select style={{width: '100%'}} allowClear={true}>
                  {serviceStatus.map(item => (
                    <Select.Option key={item.code}>{item.text}</Select.Option>
                  ))}
                </Select>
              )}
            </FormItem>
          </Col>
          <Col span={4}>
            <FormItem label="服务验证">
              {getFieldDecorator('checkService', {
                initialValue: '',
              })(
                <Select style={{width: '100%'}} allowClear={true}>
                  {appStatus.map(item => (
                    <Select.Option key={item.code}>{item.text}</Select.Option>
                  ))}
                </Select>
              )}
            </FormItem>
          </Col>
          <Col span={4}>
            <FormItem label="设备验证">
              {getFieldDecorator('checkDevice', {
                initialValue: '',
              })(
                <Select style={{width: '100%'}} allowClear={true}>
                  {appStatus.map(item => (
                    <Select.Option key={item.code}>{item.text}</Select.Option>
                  ))}
                </Select>
              )}
            </FormItem>
          </Col>
          <Col span={4}>
            <Button type="primary" className={styles.buttonBox} htmlType="submit">
              查询
            </Button>
            <Button type="primary" htmlType="reset">
              重置
            </Button>
          </Col>
        </Row>
      </Form>
    );
  }

  submitForm = (data, mark) => {
    const {dispatch} = this.props;
    const {markStatus, rowData, selectedRowKeys} = this.state;
    if (markStatus == 0) {
      dispatch({
        type: `ApplicationManager/add`,
        payload: {
          ...data,
        },
        callback: res => {
          if (res.code == 0) {
            messageModal('success', '新增成功');
            this.formRef.handleMinModalVisible(false);
            this.reloadData();
          } else {
            messageModal('error', res.message || '新增失败');
          }
        },
      });
    }
    if (markStatus == 1) {
      dispatch({
        type: `ApplicationManager/update`,
        payload: {
          ...data,
        },
        callback: res => {
          if (res.code == 0) {
            this.formRef.handleMinModalVisible(false);
            messageModal('success', '编辑成功');
            this.reloadData();
            this.setState({
              selectedRows: [],
              selectedRowKeys: undefined,
            });
          } else {
            messageModal('error', res.message || '编辑失败');
          }
        },
      });
    }
    if (markStatus == 2) {
      dispatch({
        type: `ApplicationManager/resetService`,
        payload: {
          appId: rowData.id,
          ...data,
        },
        callback: res => {
          if (res.code == 0) {
            this.serviceRef.handleMinModalVisible(false);
            messageModal('success', '服务更新成功');
            this.reloadData();
            this.setState({
              selectedRows: [],
              selectedRowKeys: undefined,
            });
          } else {
            messageModal('error', res.message || '服务更新失败');
          }
        },
      });
    }
    // 主题授权提交
    if (markStatus == 3) {
      authAppAndService({
        appIdList: selectedRowKeys,
        ...data,
      }).then(res => {
        if (res) {
          this.topicRef?.handleMinModalVisible(false);
          messageModal('success', '主题更新成功');
          this.reloadData();
          this.setState({
            selectedRows: [],
            selectedRowKeys: undefined,
          });
        } else {
          messageModal('error', res?.message || '主题更新失败');
        }
      })
    }
  };

  handleDelete = item => {
    const {selectedRowKeys, selectedRows} = this.state;
    if (!selectedRows || selectedRows.length === 0) {
      messageModal('warning', '请选择一项!');
      return;
    }
    const {dispatch} = this.props;
    const that = this;
    confirm({
      title: '确定删除?',
      content: '删除该条记录',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        dispatch({
          type: `ApplicationManager/delete`,
          payload: {id: selectedRows[0].id},
          callback: res => {
            if (res.code == 0) {
              that.reloadData();
              that.setState({
                selectedRows: [],
                selectedRowKeys: undefined,
              });
              messageModal('success', '删除成功!');
            } else {
              messageModal('error', res.message);
            }
          },
        });
      },
    });
  };

  render() {
    const { secretVisibility } = this.state; // 从 state 中获取 secretVisibility

    const columns = [
      {
        title: '应用名称',
        dataIndex: 'name',
        width: '20%',
        align: 'center',
      },
      {
        title: '应用KEY',
        dataIndex: 'key',
        width: '20%',
        align: 'center',
      },
      {
        title: '应用SECRET',
        dataIndex: 'secret',
        width: '15%',
        align: 'center',
        render: (text, record) => {
          const isVisible = secretVisibility[record.key];
          const placeholder = '●●●●●●●';

          // 定义文本区域的样式
          const textSpanStyle = {
            minWidth: '120px',
            display: 'inline-block',
            marginRight: '4px',
            fontFamily: 'monospace',
            verticalAlign: 'middle',
          };

          const buttonStyle = {
            verticalAlign: 'middle',
          };

          return (
            <>
              <span style={textSpanStyle}>
                {isVisible ? text : placeholder}
              </span>
              <Button
                type="link"
                onClick={() => {
                  // navigator.clipboard.writeText(md5(`${record.key + record.secret  }`).toUpperCase())
                  this.toggleSecretVisibility(record.key)
                }}
                style={buttonStyle}
              >
                {isVisible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              </Button>
            </>
          );
        },
      },
      {
        title: '状态',
        dataIndex: 'status',
        align: 'center',
        width: '15%',
        render: text => {
          return text == '0' ? '禁用' : '正常';
        },
      },
      {
        title: '服务验证',
        dataIndex: 'checkService',
        width: '15%',
        align: 'center',
        render: text => {
          return text == '0' ? '不需要' : '需要';
        },
      },
      {
        title: '设备验证',
        dataIndex: 'checkDevice',
        width: '15%',
        align: 'center',
        render: text => {
          return text == '0' ? '不需要' : '需要';
        },
      },
    ];
    const {rowData, selectedRows, markStatus} = this.state;
    const {
      ApplicationManager: {loading, data},
      menu: {currentBtnArray},
    } = this.props;
    const methods = {
      submitForm: this.submitForm,
      data: rowData,
    };
    const serviceMethods = {
      submitForm: this.submitForm,
      data: rowData,
      reloadTranferData: this.reloadTranferData,
      markStatus
    };

    const topicMethods = {
      submitForm: this.submitForm,
      data: rowData,
      reloadTopicTranferData: this.reloadTopicTranferData,
      markStatus
    };
    return (
      <PageHeaderWrapper>
        <Card bordered={false}>
          <div className={styles.tableListForm}> {this.renderForm()}</div>
          <div className={styles.btnStyle}>
            {utils.getBtnName(currentBtnArray).includes('新增') ? (
              <span>
                <PlusCircleOutlined style={{fontSize: 16, color: '#40a9ff'}}/>
                &nbsp;
                <a onClick={this.handleAdd}>新增</a>
                <Divider type="vertical"/>
              </span>
            ) : null}
            {utils.getBtnName(currentBtnArray).includes('修改') ? (
              <span>
                <EditOutlined style={{fontSize: 16, color: '#40a9ff'}}/>
                &nbsp;
                <a onClick={this.handleEdit}>修改</a>
                <Divider type="vertical"/>
              </span>
            ) : null}
            {utils.getBtnName(currentBtnArray).includes('删除') ? (
              <span>
                <DeleteOutlined style={{fontSize: 16, color: 'red'}}/>
                &nbsp;
                <a style={{color: 'red'}} onClick={this.handleDelete}>
                  删除
                </a>
              </span>
            ) : null}
            <Divider type="vertical"/>
            {utils.getBtnName(currentBtnArray).includes('设备管理') ? (
              <span>
                <EditOutlined style={{fontSize: 16, color: '#40a9ff'}}/>
                &nbsp;
                <a onClick={this.handleDevice}>设备管理</a>
                <Divider type="vertical"/>
              </span>
            ) : null}
            {utils.getBtnName(currentBtnArray).includes('服务授权') ? (
              <span>
                <EditOutlined style={{fontSize: 16, color: '#40a9ff'}}/>
                &nbsp;
                <a onClick={this.handleService}>服务授权</a>
              </span>
            ) : null}
            <Divider type="vertical"/>
            {utils.getBtnName(currentBtnArray).includes('主题授权') ? (
              <span>
                <EditOutlined style={{fontSize: 16, color: '#40a9ff'}}/>
                &nbsp;
                <a onClick={this.handleTopicService}>主题授权</a>
              </span>
            ) : null}
          </div>
          <CommonTable
            columns={columns}
            loading={loading}
            current={markPage}
            data={data}
            paginationShow={true}
            onChange={this.handleCommonTableChange}
            rowSelectionShow={true}
            selectedRows={selectedRows}
            selectType="radio"
            onSelect={(selectedRowKeys, selectedRows) => {
              this.setState({
                selectedRowKeys,
                selectedRows,
              });
            }}
          />
          <ModelForm wrappedComponentRef={inst => (this.formRef = inst)} {...methods} />
          <ServiceModel
            wrappedComponentRef={inst => (this.serviceRef = inst)}
            {...serviceMethods}
          />

          <TopicModel
            wrappedComponentRef={inst => (this.topicRef = inst)}
            {...topicMethods}
          />
        </Card>
      </PageHeaderWrapper>
    );
  }
}

export default ApplicationManager;
