package com.xmcares.platform.admin.common.database.metaquery;

import com.xmcares.platform.admin.common.database.DataSourceBasedMetaQuery;
import com.xmcares.platform.admin.common.database.metainfo.ColumnInfo;
import com.xmcares.platform.admin.common.database.metainfo.TableInfo;
import com.xmcares.platform.admin.common.errors.MetadataException;
import com.xmcares.platform.admin.common.jdbc.JdbcUtils;
import org.apache.commons.dbutils.handlers.BeanHandler;
import org.apache.commons.dbutils.handlers.BeanListHandler;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.List;

/**
 * Phoenix on Hbase 元数据查询
 * <AUTHOR>
 * @since 2.1.0
 */
public class PhoenixMetaQuery extends DataSourceBasedMetaQuery {

    private static final String SQL_TABLES = "SELECT table_name AS name, '' AS comment " +
            "FROM system.catalog WHERE table_type = 'u' AND table_schem = ? GROUP BY table_name";
    private static final String SQL_TABLE_COLUMNS = "SELECT column_name AS name, data_type AS type, '' AS comment " +
            "FROM system.catalog WHERE table_schem = ? AND table_name = ? AND column_name IS NOT NULL ORDER BY ordinal_position";

    public PhoenixMetaQuery(DataSource dataSource) {
        super(dataSource);
    }

    public PhoenixMetaQuery(DataSource dataSource, String schema) {
        super(dataSource, schema);
    }

    @Override
    public TableInfo getTableInfo(String tableName) {
        try {
            //Phoenix 的 "GROUP BY table_name" 同 distinct，可以用于去重
            String sql = SQL_TABLES.replace("GROUP BY table_name",   "AND table_name = ? GROUP BY table_name");
            return JdbcUtils.executeQuery(this.dataSource, sql, new BeanHandler<>(TableInfo.class), this.schema, tableName);
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取Phoenix库[%s]表[%s]TableInfo失败", this.schema, tableName), e);
        }
    }

    @Override
    public List<TableInfo> getTableInfos() {
        try {
            return JdbcUtils.executeQuery(this.dataSource, SQL_TABLES, new BeanListHandler<>(TableInfo.class), this.schema);
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取Phoenix库[%s]所有表TableInfo失败", this.schema), e);
        }
    }

    @Override
    public List<ColumnInfo> getColumnInfos(String tableName) {
        try {
            return JdbcUtils.executeQuery(this.dataSource, SQL_TABLE_COLUMNS, new BeanListHandler<>(ColumnInfo.class), this.schema, tableName);
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取Phoenix库[%s]表[%s]列ColumnInfo失败", this.schema, tableName), e);
        }
    }
}
