{"groups": [{"name": "xbdp.developer", "type": "com.xmcares.platform.admin.developer.common.config.DeveloperProperties", "sourceType": "com.xmcares.platform.admin.developer.common.config.DeveloperProperties"}, {"name": "xbdp.developer.scheduler", "type": "com.xmcares.platform.admin.developer.common.config.DeveloperProperties$SchedulerProperties", "sourceType": "com.xmcares.platform.admin.developer.common.config.DeveloperProperties", "sourceMethod": "getScheduler()"}], "properties": [{"name": "xbdp.developer.file-server-dir", "type": "java.lang.String", "description": "文件服务器的根目录", "sourceType": "com.xmcares.platform.admin.developer.common.config.DeveloperProperties"}, {"name": "xbdp.developer.local-tmp-dir", "type": "java.lang.String", "description": "本地临时文件的根目录，默认为${user.dir}/tmp/developer", "sourceType": "com.xmcares.platform.admin.developer.common.config.DeveloperProperties"}, {"name": "xbdp.developer.scheduler.alert-email", "type": "java.lang.String", "description": "调度任务执行时的告警邮箱", "sourceType": "com.xmcares.platform.admin.developer.common.config.DeveloperProperties$SchedulerProperties"}, {"name": "xbdp.developer.scheduler.group", "type": "java.lang.String", "description": "勾盒服务应用的名称", "sourceType": "com.xmcares.platform.admin.developer.common.config.DeveloperProperties$SchedulerProperties"}], "hints": []}