/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/2
 */
package com.xmcares.platform.admin.common.datasource.mq.pulsar;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public class PulsarProperties {

    /**
     * pulsar service url: 格式为“(pulsar|pulsar+ssl)://<host>:<port>”
     */
    private String serviceUrl;
    /**
     * pulsar admin url
     */
    private String adminUrl;

    /**
     * 认证方式：支持 "token"、"tls"、"basic"
     */
    private String authType;

    /**
     * token 字符串，authType=token 时使用
     */
    private String token;

    /**
     * TLS 客户端证书路径，authType=tls 时使用
     */
    private String certPath;

    /**
     * TLS 客户端私钥路径，authType=tls 时使用
     */
    private String keyPath;

    /**
     * 用户名 字符串，authType=basic 时使用
     */
    private String username;
    /**
     * 密码 字符串，authType=basic 时使用
     */
    private String password;

    /**
     * OAuth2 配置 JSON 文件路径，authType=oauth2 时使用，如：file:///xx/oauth2.json
     */
    private String oauth2ConfigFile;

    /**
     * 操作超时时间，默认30s
     */
    private int operationTimeoutSeconds = 30;
    /**
     * 与客户端建立连接的超时时间。默认10s
     */
    private int connectTimeoutSeconds = 10;
    /**
     * 与客户端连接保持活动的间隔。默认30s
     */
    private int keepaliveIntervalSeconds = 30;


    public String getServiceUrl() {
        return this.serviceUrl;
    }

    public void setServiceUrl(String serviceUrl) {
        this.serviceUrl = serviceUrl;
    }

    public String getAdminUrl() {
        return adminUrl;
    }

    public void setAdminUrl(String adminUrl) {
        this.adminUrl = adminUrl;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCertPath() {
        return certPath;
    }

    public void setCertPath(String certPath) {
        this.certPath = certPath;
    }

    public String getKeyPath() {
        return keyPath;
    }

    public void setKeyPath(String keyPath) {
        this.keyPath = keyPath;
    }

    public String getOauth2ConfigFile() {
        return oauth2ConfigFile;
    }

    public void setOauth2ConfigFile(String oauth2ConfigFile) {
        this.oauth2ConfigFile = oauth2ConfigFile;
    }

    public int getOperationTimeoutSeconds() {
        return this.operationTimeoutSeconds;
    }


    public void setOperationTimeoutSeconds(int operationTimeoutSeconds) {
        this.operationTimeoutSeconds = operationTimeoutSeconds;
    }

    public int getConnectionTimeoutSeconds() {
        return this.connectTimeoutSeconds;
    }

    public void setConnectTimeoutSeconds(int connectTimeoutSeconds) {
        this.connectTimeoutSeconds = connectTimeoutSeconds;
    }

    public int getKeepAliveIntervalSeconds() {
        return this.keepaliveIntervalSeconds;
    }

    public void setKeepaliveIntervalSeconds(int keepaliveIntervalSeconds) {
        this.keepaliveIntervalSeconds = keepaliveIntervalSeconds;
    }



    public enum AuthType {
        TOKEN,
        TLS,
        BASIC;

        public boolean equalsIgnoreCase(String value) {
            return this.name().equalsIgnoreCase(value);
        }
    }
}
